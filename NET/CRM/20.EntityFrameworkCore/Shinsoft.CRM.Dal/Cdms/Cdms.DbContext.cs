﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.CRM.Entities.Cdms;

namespace Shinsoft.CRM.Dal.Cdms
{
    public partial class CdmsDbContext : BaseDbContext
    {
	    public CdmsDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 附件
        /// </summary>
		public DbSet<CdmsAttachment> CdmsAttachment { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
		public DbSet<CdmsBrand> CdmsBrand { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsBU> CdmsBU { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
		public DbSet<CdmsCity> CdmsCity { get; set; }

        /// <summary>
        /// 商务部组织架构
        /// </summary>
		public DbSet<CdmsCommerceOrganization> CdmsCommerceOrganization { get; set; }

        /// <summary>
        /// 组织机构省份关联
        /// </summary>
		public DbSet<CdmsCommerceOrganizationInProvince> CdmsCommerceOrganizationInProvince { get; set; }

        /// <summary>
        /// 区县
        /// </summary>
		public DbSet<CdmsCounty> CdmsCounty { get; set; }

        /// <summary>
        /// 客户分类
        /// </summary>
		public DbSet<CdmsCustomerCategory> CdmsCustomerCategory { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
		public DbSet<CdmsDepartment> CdmsDepartment { get; set; }

        /// <summary>
        /// 字典
        /// </summary>
		public DbSet<CdmsDictionary> CdmsDictionary { get; set; }

        /// <summary>
        /// 字典项
        /// </summary>
		public DbSet<CdmsDictionaryItem> CdmsDictionaryItem { get; set; }

        /// <summary>
        /// 流向商业月流向
        /// </summary>
		public DbSet<CdmsDistributorSalesFlowMonthly> CdmsDistributorSalesFlowMonthly { get; set; }

        /// <summary>
        /// 医生讲者申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeaker> CdmsDoctorAndSpeaker { get; set; }

        /// <summary>
        /// 医生讲者所属机构申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeakerBU> CdmsDoctorAndSpeakerBU { get; set; }

        /// <summary>
        /// 医生讲者所属机构申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeakerBURequest> CdmsDoctorAndSpeakerBURequest { get; set; }

        /// <summary>
        /// 医生讲者所属机构申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeakerReceiver> CdmsDoctorAndSpeakerReceiver { get; set; }

        /// <summary>
        /// 医生讲者所属机构申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeakerReceiverRequest> CdmsDoctorAndSpeakerReceiverRequest { get; set; }

        /// <summary>
        /// 医生讲者申请单
        /// </summary>
		public DbSet<CdmsDoctorAndSpeakerRequest> CdmsDoctorAndSpeakerRequest { get; set; }

        /// <summary>
        /// 用户
        /// </summary>
		public DbSet<CdmsEmployee> CdmsEmployee { get; set; }

        /// <summary>
        /// 用户所属角色
        /// </summary>
		public DbSet<CdmsEmployeeInRole> CdmsEmployeeInRole { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsHospitalProcurement> CdmsHospitalProcurement { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsMaterial> CdmsMaterial { get; set; }

        /// <summary>
        /// 分型
        /// </summary>
		public DbSet<CdmsMaterialGroup> CdmsMaterialGroup { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
		public DbSet<CdmsProduct> CdmsProduct { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsProductBatch> CdmsProductBatch { get; set; }

        /// <summary>
        /// 产品线
        /// </summary>
		public DbSet<CdmsProductLine> CdmsProductLine { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
		public DbSet<CdmsProvince> CdmsProvince { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsQuotaOfStageMaster> CdmsQuotaOfStageMaster { get; set; }

        /// <summary>
        /// 收货方
        /// </summary>
		public DbSet<CdmsReceiver> CdmsReceiver { get; set; }

        /// <summary>
        /// 新增机构申请单
        /// </summary>
		public DbSet<CdmsReceiverAddRequest> CdmsReceiverAddRequest { get; set; }

        /// <summary>
        /// 收货方变更申请
        /// </summary>
		public DbSet<CdmsReceiverChangeRequest> CdmsReceiverChangeRequest { get; set; }

        /// <summary>
        /// 收货方负责人
        /// </summary>
		public DbSet<CdmsReceiverManager> CdmsReceiverManager { get; set; }

        /// <summary>
        /// 商务部组织机构欧的流向商业权限
        /// </summary>
		public DbSet<CdmsReceiverOfCommerceOrganization> CdmsReceiverOfCommerceOrganization { get; set; }

        /// <summary>
        /// 商务部组织机构欧的流向商业权限
        /// </summary>
		public DbSet<CdmsReceiverOfCommerceOrganizationHistory> CdmsReceiverOfCommerceOrganizationHistory { get; set; }

        /// <summary>
        /// 收货方变更申请主单据
        /// </summary>
		public DbSet<CdmsReceiverRequestForm> CdmsReceiverRequestForm { get; set; }

        /// <summary>
        /// 收货方类型
        /// </summary>
		public DbSet<CdmsReceiverType> CdmsReceiverType { get; set; }

        /// <summary>
        /// 商务区域
        /// </summary>
		public DbSet<CdmsRegion> CdmsRegion { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
		public DbSet<CdmsRole> CdmsRole { get; set; }

        /// <summary>
        /// 月流向申诉审批历史
        /// </summary>
		public DbSet<CdmsSalesFlowComplaintsApprovalHistory> CdmsSalesFlowComplaintsApprovalHistory { get; set; }

        /// <summary>
        /// 月流向申诉
        /// </summary>
		public DbSet<CdmsSalesFlowComplaintsRequest> CdmsSalesFlowComplaintsRequest { get; set; }

        /// <summary>
        /// 流向周期配置
        /// </summary>
		public DbSet<CdmsSalesFlowCycle> CdmsSalesFlowCycle { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CdmsSystemConfiguration> CdmsSystemConfiguration { get; set; }

        /// <summary>
        /// 目标客户
        /// </summary>
		public DbSet<CdmsTargetReceiver> CdmsTargetReceiver { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Cdmsvw_SalesFlowMonthlySummaryByCommerce> Cdmsvw_SalesFlowMonthlySummaryByCommerce { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Cdmsvw_SalesFlowMonthlySummaryBySales> Cdmsvw_SalesFlowMonthlySummaryBySales { get; set; }

	}
}