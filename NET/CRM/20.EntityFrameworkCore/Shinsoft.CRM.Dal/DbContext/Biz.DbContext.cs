﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.CRM.Entities;

namespace Shinsoft.CRM.Dal
{
    public partial class BizDbContext : BaseDbContext
    {
	    public BizDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Answer> Answer { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AnswerQuestion> AnswerQuestion { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AnswerQuestionOption> AnswerQuestionOption { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
		public DbSet<Attachment> Attachment { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<BusinessUnit> BusinessUnit { get; set; }

        /// <summary>
        /// 日历
        /// </summary>
		public DbSet<Calendar> Calendar { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
		public DbSet<Company> Company { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCfg> CompanyCfg { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCulture> CompanyCulture { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCurrency> CompanyCurrency { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyPermission> CompanyPermission { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanySetting> CompanySetting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyTag> CompanyTag { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
		public DbSet<Contact> Contact { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ContactCustomer> ContactCustomer { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ContactProductLine> ContactProductLine { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
		public DbSet<CostCenter> CostCenter { get; set; }

        /// <summary>
        /// 机构
        /// </summary>
		public DbSet<Customer> Customer { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CustomerCategory> CustomerCategory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CustomerProduct> CustomerProduct { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
		public DbSet<Department> Department { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DepartmentCostCenter> DepartmentCostCenter { get; set; }

        /// <summary>
        /// 字典
        /// </summary>
		public DbSet<Dict> Dict { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DocResource> DocResource { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DoctorOutCall> DoctorOutCall { get; set; }

        /// <summary>
        /// 员工
        /// </summary>
		public DbSet<Employee> Employee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDelegate> EmployeeDelegate { get; set; }

        /// <summary>
        /// 员工岗位
        /// </summary>
		public DbSet<EmployeeStation> EmployeeStation { get; set; }

        /// <summary>
        /// 活动
        /// </summary>
		public DbSet<Event> Event { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventDict> EventDict { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventFeedback> EventFeedback { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventLoc> EventLoc { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventMeeting> EventMeeting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventMeetingSign> EventMeetingSign { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventMember> EventMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventPlan> EventPlan { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventProduct> EventProduct { get; set; }

        /// <summary>
        /// Event周期设置
        /// </summary>
		public DbSet<EventRepeat> EventRepeat { get; set; }

        /// <summary>
        /// 行为评分
        /// </summary>
		public DbSet<EventScore> EventScore { get; set; }

        /// <summary>
        /// 行为评分项
        /// </summary>
		public DbSet<EventScoreDict> EventScoreDict { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventSignIn> EventSignIn { get; set; }

        /// <summary>
        /// 培训
        /// </summary>
		public DbSet<EventTraining> EventTraining { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EventVisit> EventVisit { get; set; }

        /// <summary>
        /// 医院开发
        /// </summary>
		public DbSet<HospitalDevelop> HospitalDevelop { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<HospitalDevelopCategory> HospitalDevelopCategory { get; set; }

        /// <summary>
        /// 医院开发步骤跟进
        /// </summary>
		public DbSet<HospitalDevelopFeedback> HospitalDevelopFeedback { get; set; }

        /// <summary>
        /// 医院开发成员
        /// </summary>
		public DbSet<HospitalDevelopMember> HospitalDevelopMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<HospitalDevelopPhase> HospitalDevelopPhase { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<HospitalDevelopPhaseLog> HospitalDevelopPhaseLog { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<I18n> I18n { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MeetingCategory> MeetingCategory { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MeetingCategoryResult> MeetingCategoryResult { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
		public DbSet<Msg> Msg { get; set; }

        /// <summary>
        /// 收信人
        /// </summary>
		public DbSet<MsgReceiver> MsgReceiver { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MsgTemplate> MsgTemplate { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
		public DbSet<Permission> Permission { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
		public DbSet<Position> Position { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
		public DbSet<Product> Product { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ProductBrand> ProductBrand { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ProductLine> ProductLine { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ProductManufacturer> ProductManufacturer { get; set; }

        /// <summary>
        /// 题目
        /// </summary>
		public DbSet<Question> Question { get; set; }

        /// <summary>
        /// 问题选项
        /// </summary>
		public DbSet<QuestionOption> QuestionOption { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Region> Region { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ReviewAuditor> ReviewAuditor { get; set; }

        /// <summary>
        /// 数据审核内容
        /// </summary>
		public DbSet<ReviewData> ReviewData { get; set; }

        /// <summary>
        /// 数据审核扩展信息
        /// </summary>
		public DbSet<ReviewExtInfo> ReviewExtInfo { get; set; }

        /// <summary>
        /// 数据审核目录
        /// </summary>
		public DbSet<ReviewIndex> ReviewIndex { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ReviewTask> ReviewTask { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
		public DbSet<Role> Role { get; set; }

        /// <summary>
        /// 角色成员
        /// </summary>
		public DbSet<RoleMember> RoleMember { get; set; }

        /// <summary>
        /// 角色权限
        /// </summary>
		public DbSet<RolePermission> RolePermission { get; set; }

        /// <summary>
        /// 角色标签
        /// </summary>
		public DbSet<RoleTag> RoleTag { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SerialNumber> SerialNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SerialSeed> SerialSeed { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
		public DbSet<Station> Station { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<StationCustomer> StationCustomer { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<StationRegion> StationRegion { get; set; }

        /// <summary>
        /// 分公司
        /// </summary>
		public DbSet<SubCompany> SubCompany { get; set; }

        /// <summary>
        /// 问卷
        /// </summary>
		public DbSet<Survey> Survey { get; set; }

        /// <summary>
        /// 问卷题目
        /// </summary>
		public DbSet<SurveyQuestion> SurveyQuestion { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SurveyQuestionOption> SurveyQuestionOption { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SysCulture> SysCulture { get; set; }

        /// <summary>
        /// 系统设置
        /// </summary>
		public DbSet<SysSetting> SysSetting { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<Tag> Tag { get; set; }

        /// <summary>
        /// 用户
        /// </summary>
		public DbSet<User> User { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<UserPwdLog> UserPwdLog { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwCustomerOwner> VwCustomerOwner { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwDailyEvent> VwDailyEvent { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwDoctorOutCall> VwDoctorOutCall { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwEmployeeMsg> VwEmployeeMsg { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwEmployeeRoles> VwEmployeeRoles { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwEmployeeStation> VwEmployeeStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwEventDetail> VwEventDetail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwMeetingReport> VwMeetingReport { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwRoleEmployee> VwRoleEmployee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwRoleMember> VwRoleMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwStation> VwStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwStationLeader> VwStationLeader { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwStationProvince> VwStationProvince { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwTrainingCompleteReport> VwTrainingCompleteReport { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwTrainingReport> VwTrainingReport { get; set; }

	}
}