﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.CRM.Entities;

namespace Shinsoft.CRM.Dal
{
    public partial class MailDbContext : BaseDbContext
    {
	    public MailDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 邮件
        /// </summary>
		public DbSet<Mail> Mail { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<MailAttachment> MailAttachment { get; set; }

        /// <summary>
        /// 邮件服务器
        /// </summary>
		public DbSet<MailServer> MailServer { get; set; }

        /// <summary>
        /// 邮件模板
        /// </summary>
		public DbSet<MailTemplate> MailTemplate { get; set; }

        /// <summary>
        /// 'SMTP服务器
        /// </summary>
		public DbSet<SmtpServer> SmtpServer { get; set; }

	}
}