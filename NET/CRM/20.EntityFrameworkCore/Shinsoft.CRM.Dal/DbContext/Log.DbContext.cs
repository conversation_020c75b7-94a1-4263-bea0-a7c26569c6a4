﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.CRM.Entities;

namespace Shinsoft.CRM.Dal
{
    public partial class LogDbContext : BaseDbContext
    {
	    public LogDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 日志
        /// </summary>
		public DbSet<Log> Log { get; set; }

        /// <summary>
        /// API日志
        /// </summary>
		public DbSet<LogApi> LogApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<LogCategory> LogCategory { get; set; }

        /// <summary>
        /// 异常日志
        /// </summary>
		public DbSet<LogException> LogException { get; set; }

        /// <summary>
        /// 接口日志
        /// </summary>
		public DbSet<LogInterface> LogInterface { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<LogJob> LogJob { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<LogOperate> LogOperate { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<LogPlatform> LogPlatform { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<LogProgram> LogProgram { get; set; }

        /// <summary>
        /// 业务日志
        /// </summary>
		public DbSet<LogTarget> LogTarget { get; set; }

        /// <summary>
        /// 目标类型
        /// </summary>
		public DbSet<LogTargetType> LogTargetType { get; set; }

        /// <summary>
        /// 网页日志
        /// </summary>
		public DbSet<LogWeb> LogWeb { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLog> VwLog { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLogApi> VwLogApi { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLogBiz> VwLogBiz { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLogException> VwLogException { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLogInterface> VwLogInterface { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwLogJob> VwLogJob { get; set; }

	}
}