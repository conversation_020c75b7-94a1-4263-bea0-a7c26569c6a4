﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=SF_CDMS; uid=sf;pwd=**********;Encrypt=True;TrustServerCertificate=True;";

	const string database = "SF_CDMS";

	const string entityNamespace = "Shinsoft.CRM.Entities.Cdms";
	const string entityPrefix = "Cdms";

	const string dalNamespace = "Shinsoft.CRM.Dal.Cdms";
	const string dbContextName = "CdmsDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "";

	string[] entitySchemas = new string[]
	{
		"dbo"
	};

	string[] entityTables = new string[]
	{
		"Employee",													//员工
		"ReceiverManager",											//员工岗位
		"EmployeeInRole",											//员工角色关系
		"Province",													//省份=>Region
		"City",														//城市=>Region
		"County",													//区县=>Region
		"Region",													//Region
		"Dictionary",												//字典
		"DictionaryItem",											//字典项
		"Role",														//角色=>Position
		"Department",												//部门
		"QuotaOfStageMaster",										//阶段指标
		"BU",														//BU=>BusinessUnit
		"ProductLine",												//产品线
		"Brand",													//品牌=>ProductBrand
		"Product",													//产品
		"ReceiverType",												//终端类型=>CustomerCategory
		"Receiver",													//终端=>Customer（机构）
		"CustomerCategory",											//目标终端字典（不同步）
		"TargetReceiver",											//目标终端=>CustomerProduct
		"CommerceOrganization",										//岗位=>Station
		"ReceiverOfCommerceOrganization",							//终端岗位对应=>CustomerStation
		"CommerceOrganizationInProvince",							//岗位省份=>StationRegion
		"DoctorAndSpeaker",											//医生/讲者=>Contact
		"DoctorAndSpeakerBU",										//医生/讲者与BU的关系=>ContactBusinessUnit
		"DoctorAndSpeakerReceiver",									//医生/讲者与机构的关系=>ContactCustomer
		"ReceiverRequestForm",										//申请单
		"DoctorAndSpeakerRequest",									//医生/讲者申请表单
		"DoctorAndSpeakerBURequest",								//医生/讲者与BU的关系申请表单
		"DoctorAndSpeakerReceiverRequest",							//医生/讲者与机构的关系申请表单
		"ReceiverAddRequest",										//机构新增申请表单
		"ReceiverChangeRequest",									//机构变更申请表单
		"SalesFlowCycle",
        "DistributorSalesFlowMonthly",								//流向明细
        "SalesFlowComplaintsRequest",								//流向申诉表
        "SalesFlowComplaintsApprovalHistory",						//申诉审批历史表
		"SystemConfiguration",										//系统配置文件
		"MaterialGroup",											//分型
		"Material",													//物料
		"ProductBatch",												//产品批号
		"Attachment",												//附件
		"ReceiverOfCommerceOrganizationHistory",					//收货方负责人关系表
		"HospitalProcurement",                                      //医院采购
		
	};

	string[] entityViews = new string[]
	{
		"vw_SalesFlowMonthlySummaryBySales",    //销售部汇总
		"vw_SalesFlowMonthlySummaryByCommerce"    //商务部汇总
	};

	string[] entityViewPrefixes = new string[]
	{
		"Vw"
	};

	string[] enumNamespaces = new string[]
	{
		"Shinsoft.Core.Mail"
	};
#>