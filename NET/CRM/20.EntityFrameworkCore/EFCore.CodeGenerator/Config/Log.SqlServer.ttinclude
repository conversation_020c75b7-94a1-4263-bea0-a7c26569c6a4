﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=SF_CRM; uid=sf;pwd=**********;";

	const string database = "SF_CRM";

	const string entityNamespace = "Shinsoft.CRM.Entities";
	const string entityPrefix = "";

	const string dalNamespace = "Shinsoft.CRM.Dal";
	const string dbContextName = "LogDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "";

	string[] entitySchemas = new string[]
	{
		"log",
	};

	string[] entityTables = new string[]
	{
	};

	string[] entityViews = new string[]
	{
	};

	string[] entityViewPrefixes = new string[]
	{
		"Vw"
	};

	string[] enumNamespaces = new string[]
	{
		//"Shinsoft.Core.NLog"
	};
#>