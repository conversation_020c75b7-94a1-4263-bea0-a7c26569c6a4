﻿<#@ template debug="true" hostspecific="true" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ assembly name="EnvDTE" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ include file="../../../20.EntityFrameworkCore/EFCore.CodeGenerator/Config/Biz.SqlServer.ttinclude"#>
<#@ include file="../../../20.EntityFrameworkCore/EFCore.CodeGenerator/DbHelper/SqlServer.ttinclude"#>
<#@ output extension=".cs" #>
<#
	const string modelNamespace = "Shinsoft.CRM.Api.Models";

	this.GenerationEnvironment.Clear();

	using(var dbHelper = new DbHelper(connectionString, database))
	{
		var tables = dbHelper.GetTables(entityPrefix, entitySchemas, entityTables, entityViews, entityViewPrefixes);

#>
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.NLog;
using <#=entityNamespace#>;
<#
			foreach(var enumNamespace in enumNamespaces)
			{
#>
using <#=enumNamespace#>;
<#
			}
#>

#pragma warning disable CS8669

namespace <#=modelNamespace#>
{
<#

		var exincludeColumns = new List<string>{
			"Deleted",
			"Creator",
			"CreateTime",
			"LastEditor",
			"LastEditTime"
		};

		var targetNameCloumnNames = new List<string>{
			"Name",
			"NameCn",
			"NameEn",
			"DisplayName",
			"No",
			"Title",
			"Code"
		};

		foreach(var table in tables)
		{
			var entityName = table.SysTypeName;
			var columns = table.Columns
				.Where(p=>!exincludeColumns.Contains(p.Name))
				.ToList();

			var inheritStr = new StringBuilder();

			if(!string.IsNullOrEmpty(companyIdTypeName) && columns.Any(p=>p.Name == "CompanyId" && p.SysTypeName == companyIdTypeName))
			{
				columns = columns.Where(p=>p.Name != "CompanyId").ToList();

				if(table.IsOperateInfo)
				{
					inheritStr.Append("CompanyOperateInfoModel");
				}
				else
				{
					inheritStr.Append("BaseCompanyModel");
				}
			}
			else
			{
				if(table.IsOperateInfo)
				{
					inheritStr.Append("OperateInfoModel");
				}
				else
				{
					inheritStr.Append("BaseModel");
				}
			}

			var isLogTarget = false;
			var targetName = "string.Empty";
			var targetId = "";

			var pkColumns = columns.Where(p=>p.IsPrimaryKey).ToList();

			if(pkColumns.Any())
			{
				isLogTarget = true;

				if(inheritStr.Length > 0)
				{
					inheritStr.Append(", ILogTarget");
				}
				else
				{
					inheritStr.Append("ILogTarget");
				}

				var targetNameColumn = columns
					.Where(p=>p.DbType == DbType.String && targetNameCloumnNames.Contains(p.Name))
					.OrderBy(p=>targetNameCloumnNames.IndexOf(p.Name))
					.FirstOrDefault();

				if(targetNameColumn != null)
				{
					if(targetNameColumn.SysTypeName == "string?")
					{
						targetName = $"this.{targetNameColumn.Name}.GetValueOrDefault()";
					}
					else
					{
						targetName = $"this.{targetNameColumn.Name}";
					}
				}

				
				if(pkColumns.Count() == 1)
				{
					targetId = $"this.{pkColumns.First().Name}";
				}
				else
				{
					var idStr = new StringBuilder();

					foreach(var column in pkColumns)
					{
						idStr.AppendFormat(",{{this.{0}}}",column.Name );
					}

					idStr.Remove(0,1);

					targetId = $"$\"{idStr}\"";

				}
			}

#>

	#region <#=entityName#>

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
	[Description("<#=table.Comment#>")]
<#
			}
#>
    [MapFromType(typeof(<#=entityName#>), Reverse = true)]
	public abstract class <#=entityName#>Raw : <#=inheritStr.ToString()#>
    {
<#
			if(isLogTarget)
			{
#>
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => <#=targetName#>;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(<#=entityName#>);

        [JsonIgnore]
        object? ILogTarget.TargetId => <#=targetId#>;

        #endregion ILogTarget

<#
			}

			var hasUid = columns.Any(p=>p.Name == "Uid" && p.IsIdentity);

			foreach(var column in columns)
			{
				var hasComment = !string.IsNullOrEmpty(column.Comment);
				var defaultValue = column.SysTypeName == "string" && !column.IsNullable
					? " = string.Empty;"	
					: "";
				var reverse = column.IsPrimaryKey || !(column.IsIdentity || (hasUid && column.Name == "UidPath" && column.SysTypeName == "string"));

				if(hasComment)
				{
#>
		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
		[Description("<#=column.Comment#>")]
<#
				}

				if(!reverse)
				{
#>
        [MapFromProperty(typeof(<#=entityName#>), Reverse = false)]
<#
				}
#>
        public virtual <#=column.SysTypeName#> <#=column.Name#> { get; set; }<#=defaultValue#>

<#
				if(column.IsEnum)
				{
					if(hasComment)
					{
#>
		/// <summary>
        /// <#=column.Comment#>
        /// </summary>
		[Description("<#=column.Comment#>")]
<#
					}
#>
		public virtual string <#=column.Name#>Desc => this.<#=column.Name#>.GetDesc();

<#
				}

			}
#>
    }

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
	[Description("<#=table.Comment#>")]
<#
			}
#>
	public abstract partial class <#=entityName#>Meta : <#=entityName#>Raw
	{
	}

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
	[Description("<#=table.Comment#>")]
<#
			}
#>
	public partial class <#=entityName#>Model : <#=entityName#>Meta
	{
	}

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
	[Description("<#=table.Comment#>")]
<#
			}
#>
	public partial class <#=entityName#>Query : <#=entityName#>Meta
	{
	}

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>
    /// </summary>
	[Description("<#=table.Comment#>")]
<#
			}
#>
	public partial class <#=entityName#>Selector : <#=entityName#>Meta
	{
	}

<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>查询条件
    /// </summary>
<#
			}
#>
	[DynamicQueryEntity(typeof(<#=entityName#>))]
	public partial class <#=entityName#>Filter : FilterModel
	{
	}
<#
			if(!string.IsNullOrEmpty(table.Comment))
			{
#>
    /// <summary>
    /// <#=table.Comment#>选择器查询条件
    /// </summary>
<#
			}
#>
	[DynamicQueryEntity(typeof(<#=entityName#>))]
	public partial class <#=entityName#>SelectorFilter : FilterModel
	{
	}

	#endregion <#=entityName#>

<#
		}
	}
#>
}

#pragma warning restore CS8669
