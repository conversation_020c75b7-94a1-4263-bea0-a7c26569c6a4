﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shinsoft.Core.EntityFrameworkCore;
using Column = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;

namespace Shinsoft.CRM.Entities.Cdms
{
    public abstract class CdmsOperateInfo : Entity, ICdmsOperateInfo
    {
        #region Construct

        protected CdmsOperateInfo()
        {
        }

        protected CdmsOperateInfo(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {
        }

        #endregion Construct

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Creator = string.Empty;

        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column("Creator", DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 64)]
        public virtual string Creator
        {
            get => _Creator;
            set
            {
                _Creator = value ?? string.Empty;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator 创建人

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _CreateTime;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column("CreateTime", DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
        public virtual DateTime CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime 创建时间

        #region FinalEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _FinalEditor = string.Empty;

        /// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column("FinalEditor", DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 64)]
        public virtual string FinalEditor
        {
            get => _FinalEditor;
            set
            {
                _FinalEditor = value ?? string.Empty;
                this.SetChangedColumn(_FinalEditor);
            }
        }

        #endregion FinalEditor 最后修改人

        #region FinalEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _FinalEditTime;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column("FinalEditTime", DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
        public virtual DateTime FinalEditTime
        {
            get => _FinalEditTime;
            set
            {
                _FinalEditTime = value;
                this.SetChangedColumn(_FinalEditTime);
            }
        }

        #endregion FinalEditTime 最后修改时间
    }
}