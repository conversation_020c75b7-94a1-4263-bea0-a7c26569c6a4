﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities.Cdms
{
    public interface ICdmsOperateInfo
    {
        /// <summary>
        /// 创建者
        /// </summary>
        [Description("创建者")]
        string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新者
        /// </summary>
        [Description("最后更新者")]
        string FinalEditor { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [Description("最后更新时间")]
        DateTime FinalEditTime { get; set; }
    }
}