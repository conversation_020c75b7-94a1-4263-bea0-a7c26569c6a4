﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities.Cdms
{
    public enum FormStatus
    {
        [Description("无")]
        None = 0,

        /// <summary>
        /// 草稿
        /// </summary>
        [Description("草稿")]
        Draft = 10,

        /// <summary>
        /// 待生效
        /// </summary>
        [Description("待生效")]
        WaitingEffect = 15,

        /// <summary>
        /// 待审批
        /// </summary>
        [Description("待审批")]
        WaitingApproval = 20,

        /// <summary>
        /// 待确认
        /// </summary>
        [Description("待确认")]
        WaitingConfirm = 30,

        /// <summary>
        /// 完成
        /// </summary>
        [Description("完成")]
        Completion = 100,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Description("已拒绝")]
        Reject = 110,

        /// <summary>
        /// 已撤回
        /// </summary>
        [Description("已撤回")]
        Withdrawn = 120
    }
}