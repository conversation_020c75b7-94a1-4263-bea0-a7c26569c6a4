﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities.Cdms
{
    /// <summary>
    /// 当前周期状态
    /// </summary>
    public enum CycleStatus
    {
        [Description("无")]
        None = 0,

        /// <summary>
        /// 未计算
        /// </summary>
        [Description("未计算")]
        NotImported = 1,

        ///// <summary>
        ///// 已导入
        ///// </summary>
        //[Description("已导入")]
        //Imported = 2,

        /// <summary>
        /// 已计算
        /// </summary>
        [Description("已计算")]
        Calculated = 3,

        /// <summary>
        /// 已发布
        /// </summary>
        [Description("已发布")]
        HasBeenReleased = 4,

        /// <summary>
        /// 已冻结
        /// </summary>
        [Description("已冻结")]
        Freezing = 5,
    }
}