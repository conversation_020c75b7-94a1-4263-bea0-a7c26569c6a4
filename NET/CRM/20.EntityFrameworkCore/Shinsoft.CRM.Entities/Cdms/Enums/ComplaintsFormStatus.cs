﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities.Cdms
{
    public enum ComplaintsFormStatus
    {
        None = 0,

        /// <summary>
        /// 已提交
        /// </summary>
        [Description("申诉中")]
        Request = 10,

        ///// <summary>
        ///// 已审批
        ///// </summary>
        //[Description("已审批")]
        //Approval = 20,

        /// <summary>
        /// 已确认
        /// </summary>
        [Description("已确认")]
        Confirm = 30,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Description("已拒绝")]
        Reject = 40,

        ///// <summary>
        ///// 已取消
        ///// </summary>
        //[Description("已取消")]
        //Cancel = 50
    }
}
