﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities.Cdms
{
    public enum FormType
    {
        [Description("无")]
        None = 0,

        /// <summary>
        /// 升级流向商业
        /// </summary>
        [Description("升级流向商业")]
        Upgrade = 1,

        /// <summary>
        /// 取消
        /// </summary>
        [Description("取消流向商业")]
        Cancel = 2,

        /// <summary>
        /// 新增产品渠道
        /// </summary>
        [Description("新增产品渠道")]
        AddProductChannel = 3,

        /// <summary>
        /// 流向商业信息变更
        /// </summary>
        [Description("流向商业信息变更")]
        ChangeDistributor = 4,

        /// <summary>
        /// 产品渠道变更
        /// </summary>
        [Description("变更产品渠道")]
        UpdateProductChannel = 5,

        /// <summary>
        /// 停用产品渠道
        /// </summary>
        [Description("停用产品渠道")]
        DeleteProductChannel = 6,

        /// <summary>
        /// 收货方信息变更
        /// </summary>
        [Description("收货方信息变更")]
        ChangeReceiver = 7,

        /// <summary>
        /// 新增流向商业
        /// </summary>
        [Description("新增流向商业")]
        AddDistributor = 8,

        /// <summary>
        /// 流向商业客户变更
        /// </summary>
        [Description("流向商业客户变更")]
        ChangeDistributorContractor = 9,

        /// <summary>
        /// 新增医生
        /// </summary>
        [Description("新增医生")]
        AddDoctor = 10,

        /// <summary>
        /// 新增讲者
        /// </summary>
        [Description("新增讲者")]
        AddSpeaker = 11,

        /// <summary>
        /// 升级讲者
        /// </summary>
        [Description("升级讲者")]
        UpgradeSpeaker = 12,

        /// <summary>
        /// 变更医生
        /// </summary>
        [Description("变更医生")]
        ChangeDoctor = 13,

        /// <summary>
        /// 新增机构
        /// </summary>
        [Description("新增机构")]
        AddReceiver = 14,
    }
}