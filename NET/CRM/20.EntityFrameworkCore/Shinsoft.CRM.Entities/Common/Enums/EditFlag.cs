﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    [Flags]
    public enum EditFlag
    {
        None = 0,

        /// <summary>
        /// 编码可编辑
        /// </summary>
        [Description("编码可编辑")]
        Code = 1,


        /// <summary>
        /// 名称可编辑
        /// </summary>
        [Description("名称可编辑")]
        Name = 2,


        /// <summary>
        /// 可编辑
        /// </summary>
        [Description("可删除")]
        Delete = 4,


        /// <summary>
        /// 子对象可维护
        /// </summary>
        [Description("子对象可维护")]
        Child = 8


    }
}
