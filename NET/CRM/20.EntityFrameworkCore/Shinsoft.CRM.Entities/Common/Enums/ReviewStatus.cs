﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum ReviewStatus
    {
        None = 0,

        /// <summary>
        /// 审批中
        /// </summary>
        [Description("审批中")]
        Processing = 1,

        /// <summary>
        /// 待审批
        /// </summary>
        [Description("待审批")]
        [EnumGroup("AuditList")]
        Pending = 2,

        /// <summary>
        /// 审批通过
        /// </summary>
        [Description("审批通过")]
        [EnumGroup("AuditList")]
        [EnumGroup("Audit")]
        Approved = 10,

        /// <summary>
        /// 部分审批
        /// </summary>
        [Description("部分审批")]
        PartialApproved = 15,

        /// <summary>
        /// 审批拒绝
        /// </summary>
        [Description("审批拒绝")]
        [EnumGroup("Audit")]
        Rejected = 20,
    }
}