﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    [Flags]
    public enum PermissionFlag
    {
        None = 0,

        /// <summary>
        /// 隐藏
        /// </summary>
        [Description("隐藏")]
        [EnumGroup("Sys")]
        Invisible = 1,

        /// <summary>
        /// 可代理
        /// </summary>
        [Description("可代理")]
        [EnumGroup("Sys")]
        Agentable = 2,

        /// <summary>
        /// 允许全部标签
        /// </summary>
        [Description("允许全部标签")]
        [EnumGroup("Tag")]
        AllowAllTags = 1024,
    }
}