﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum UserType
    {
        None = 0,

        /// <summary>
        /// 用户
        /// </summary>
        [Description("员工")]
        Employee = 1,

        /// <summary>
        /// 开发者
        /// </summary>
        [Description("开发者")]
        [EnumGroup("User")]
        Developer = 10,

        /// <summary>
        /// 服务账号
        /// </summary>
        [Description("服务账号")]
        [EnumGroup("User")]
        Service = 80,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        [EnumGroup("User")]
        Other = 99,
    }
}
