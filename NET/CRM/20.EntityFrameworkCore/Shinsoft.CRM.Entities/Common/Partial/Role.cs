﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Role
    {
        /// <summary>
        /// 权限
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<string, List<string>> Permission { get; set; } = new();
    }
}
