﻿using Shinsoft.Core;
using Shinsoft.Core.Caching.Redis;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Permission : IParent<Permission>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Ordinal;

        #endregion IOrder

        [NotMapped, JsonIgnore, XmlIgnore]
        public List<Permission> InvisibleChilren { get; set; } = new();
    }
}
