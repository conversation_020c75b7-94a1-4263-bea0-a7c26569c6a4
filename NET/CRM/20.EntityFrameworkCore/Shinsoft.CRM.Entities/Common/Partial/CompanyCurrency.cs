﻿using Shinsoft.Core;
using Shinsoft.Core.Caching;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class CompanyCurrency : IHashCache, IOrder
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Currency;

        #endregion IHashCache

        #region IOrder

        IComparable IOrder.Order => this.IsStandard ? "" : this.Currency;

        #endregion IOrder
    }
}