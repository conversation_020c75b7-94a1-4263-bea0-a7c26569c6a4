﻿using Shinsoft.Core.Caching;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Company : IHashCache
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.ID.ToString();

        #endregion IHashCache
    }
}