﻿using Shinsoft.Core.Caching;
using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Region : IUid<Region>, IOrder
    {
        #region Const

        public static partial class Inverses
        {
            public const string StationProvinces = nameof(Region.StationProvinces);
        }

        #endregion Const

        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Code;

        #endregion IOrder

        #region Inverse

#pragma warning disable CS8603 // 可能返回 null 引用。

        #region StationProvinces

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<VwStationProvince>? _StationProvinces;

        [InverseProperty(VwStationProvince.Foreigns.Province)]
        public virtual List<VwStationProvince> StationProvinces
        {
            get => this.LazyLoad(ref _StationProvinces);
            set => _StationProvinces = value;
        }

        #endregion StationProvinces

#pragma warning restore CS8603 // 可能返回 null 引用。

        #endregion Inverse

        [NotMapped, XmlIgnore, JsonIgnore]
        public virtual string Text => this.Parent == null
            ? this.Name
            : $"{this.Parent.Text}/{this.Name}";
    }
}