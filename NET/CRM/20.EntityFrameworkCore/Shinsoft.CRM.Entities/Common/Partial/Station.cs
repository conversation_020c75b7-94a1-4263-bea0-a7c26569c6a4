﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Station : IUid<Station>
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        /// <summary>
        /// 下级岗位
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public List<Station> Stations { get; set; } = new();


    }
}
