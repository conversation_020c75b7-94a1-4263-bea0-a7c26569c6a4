﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class CompanyCfg
    {
        [NotMapped, XmlIgnore, JsonIgnore]
        public MailSendType EnumMailSendType => this.MailSendType.ToEnum<MailSendType>();
    }
}
