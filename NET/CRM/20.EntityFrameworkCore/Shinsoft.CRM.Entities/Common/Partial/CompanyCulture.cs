﻿using Shinsoft.Core.Caching;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class CompanyCulture : IHashCache
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Culture;

        #endregion IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<string, object?>? I18nDict { get; set; }
    }
}