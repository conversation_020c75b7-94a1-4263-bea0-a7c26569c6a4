﻿using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class User : ICompanyUser, IIdentity
    {
        //#region Foreign

        //#region Employee

        //[NotMapped, XmlIgnore, JsonIgnore]
        //private Employee? _DefaultEmployee;

        //[ForeignKey(Columns.DefaultEmployeeId)]
        //public virtual Employee? DefaultEmployee
        //{
        //    get => this.LazyLoad(ref _DefaultEmployee);
        //    set => _DefaultEmployee = value;
        //}

        //#endregion Employee

        //#endregion Foreign

        #region IUser

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UniqueName => this.LoginName;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UserId => this.ID.AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityId => (this.Identity?.ID).AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityDisplayName => this.Identity?.DisplayName ?? this.DisplayName;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string AgentId => (this.Agent?.ID).AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string AgentDisplayName => this.Agent?.DisplayName ?? string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        string IIdentityKey.RoleId => string.Empty;

        [NotMapped, XmlIgnore, JsonIgnore]
        string IUser.RoleName => string.Empty;

        #endregion IUser

        #region ICompanyUser

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyId => this.OperatorCompanyId.AsString();

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyCode => this.OperatorCompany?.Code ?? string.Empty;

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyName => this.OperatorCompany?.Name ?? string.Empty;

        #endregion ICompanyUser

        #region Operator Company

        private Company? _OperatorCompany = null;

        /// <summary>
        /// 操作公司（可能为空）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public virtual Company? OperatorCompany
        {
            get => _OperatorCompany ??= this.Identity != null ? this.Identity.Company : this.DefaultCompany;
            set
            {
                _OperatorCompany = value;
                _OperatorCompanyId = value?.ID;
            }
        }

        private Guid? _OperatorCompanyId = null;

        /// <summary>
        /// 操作公司ID（可能为空）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public virtual Guid? OperatorCompanyId => _OperatorCompanyId ??= this.OperatorCompany?.ID ?? this.Identity?.ID ?? this.DefaultCompanyId;

        #endregion Operator Company

        #region Current Company

        /// <summary>
        /// 当前公司（操作公司为空时报错）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Company CurrentCompany
        {
            get => this.OperatorCompany ?? throw new InvalidOperationException("当前公司不存在");
            set => this.OperatorCompany = value;
        }

        /// <summary>
        /// 当前公司ID（操作公司为空时报错）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Guid CurrentCompanyId => this.CurrentCompany.ID;

        #endregion Current Company

        #region Identity

        /// <summary>
        /// 当前身份员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? Identity { get; set; }

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityEmail => this.Identity?.Email ?? this.Email;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string IdentityMobile => this.Identity?.Mobile ?? this.Mobile;

        #endregion Identity

        #region IIdentityKey

        [NotMapped, XmlIgnore, JsonIgnore]
        public ProgramFlag Program { get; set; }

        [NotMapped, XmlIgnore, JsonIgnore]
        public IIdentityKey IdentityKey => new ApiIdentityKey
        {
            Program = this.Program,
            UserId = this.UserId,
            IdentityId = this.IdentityId,
            AgentId = this.AgentId,
            RoleId = string.Empty
        };

        #endregion IIdentityKey

        #region Agent

        /// <summary>
        /// 代理人员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? Agent { get; set; }

        [NotMapped, XmlIgnore, JsonIgnore]
        public bool IsAgent => this.Agent != null;

        #endregion Agent

        #region LineManager

        /// <summary>
        /// 当前身份员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? LineManager { get; set; }

        #endregion LineManager

        [NotMapped, XmlIgnore, JsonIgnore]
        public virtual bool PwdExpired => this.PwdExpiredTime <= SysDateTime.Now;

        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<Company, List<Employee>> MyIdentities { get; set; } = new();

        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<Employee, List<Employee>> MyDelegates { get; set; } = new();

        /// <summary>
        /// 岗位
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public List<Station> Stations { get; set; } = new();

        /// <summary>
        /// 权限
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<string, List<string>> Permission { get; set; } = new();
    }
}