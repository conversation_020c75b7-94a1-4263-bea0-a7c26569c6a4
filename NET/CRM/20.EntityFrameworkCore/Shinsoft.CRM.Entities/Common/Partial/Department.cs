﻿using Shinsoft.Core;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class Department : IUid<Department>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Name;

        #endregion IOrder

        [NotMapped, XmlIgnore, JsonIgnore]
        public List<CostCenter> CostCenters { get; set; } = new();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Text => this.Code.IsEmpty() ? this.Name : $"[{this.Code}] {this.Name}";
    }
}