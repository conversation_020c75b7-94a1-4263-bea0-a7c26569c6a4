﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Shinsoft.Core;

namespace Shinsoft.CRM.Entities
{
    public partial class SubCompany : IOrder
    {
        #region IOrder

        IComparable IOrder.Order => this.Sort;

        #endregion IOrder

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? Text => this.ShortName.IsEmpty() ? this.Name : $"[{this.ShortName}] {this.Name}";
    }
}