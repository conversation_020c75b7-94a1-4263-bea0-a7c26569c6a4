﻿using Shinsoft.Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public partial class Log : ICompanyLog
    {
    }

    public partial class LogApi : ICompanyLog
    {
    }

    public partial class LogApi : ICompanyLog
    {
    }

    public partial class LogWeb : ICompanyLog
    {
    }

    public partial class LogTarget : ICompanyLog
    {
    }

    public partial class LogInterface : ICompanyLog
    {
    }

    public partial class LogException : ICompanyLog
    {
    }

    public partial class VwLog : ICompanyLog
    {
    }

    public partial class VwLogApi : ICompanyLog
    {
    }

    public partial class VwLogBiz : ICompanyLog
    {
    }

    public partial class VwLogInterface : ICompanyLog
    {
    }

    public partial class VwLogJob : ICompanyLog
    {
    }

    public partial class VwLogException : ICompanyLog
    {
    }
}
