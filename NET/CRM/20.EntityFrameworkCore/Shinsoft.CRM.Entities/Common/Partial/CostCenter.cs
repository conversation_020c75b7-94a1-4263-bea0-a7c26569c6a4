﻿using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Shinsoft.CRM.Entities
{
    public partial class CostCenter : IUid<CostCenter>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Code;

        #endregion IOrder

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? Text => this.Code.IsEmpty() ? this.Name : $"[{this.Code}] {this.Name}";
    }
}