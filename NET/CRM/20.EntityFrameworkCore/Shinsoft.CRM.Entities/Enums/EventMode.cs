﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    /// <summary>
    /// 行为形式
    /// </summary>
    public enum EventMode
    {
        None = 0,

        /// <summary>
        /// 线下
        /// </summary>
        [Description("线下")]
        [EnumGroup(nameof(EventType.Visit))]
        [EnumGroup(nameof(EventType.Meeting))]
        [EnumGroup(nameof(EventType.Training))]
        [EnumGroup("Member")]
        Offline = 1,

        /// <summary>
        /// 线上
        /// </summary>
        [Description("线上")]
        [EnumGroup(nameof(EventType.Visit))]
        [EnumGroup(nameof(EventType.Meeting))]
        [EnumGroup(nameof(EventType.Training))]
        [EnumGroup("Member")]
        Online = 2,

        /// <summary>
        /// 线上+线下混合
        /// </summary>
        [Description("线上+线下混合")]
        [EnumGroup(nameof(EventType.Meeting))]
        [EnumGroup(nameof(EventType.Training))]
        OnlineAndOffline = 3,
    }
}