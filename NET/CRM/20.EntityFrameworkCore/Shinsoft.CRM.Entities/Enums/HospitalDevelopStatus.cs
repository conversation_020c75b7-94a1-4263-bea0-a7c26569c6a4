﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum HospitalDevelopStatus
    {
        None = 0,

        /// <summary>
        /// 审核中
        /// </summary>
        [Description("审核中")]
        AddAuditing = 1,

        /// <summary>
        /// 进行中
        /// </summary>
        [Description("进行中")]
        Following = 10,

        /// <summary>
        /// 成功
        /// </summary>
        [Description("成功")]
        [EnumGroup("Complete")]
        Succeed = 20,

        /// <summary>
        /// 失败
        /// </summary>
        [Description("失败")]
        [EnumGroup("Complete")]
        Failed = 30,

        /// <summary>
        /// 无效
        /// </summary>
        [Description("无效")]
        Invalid = -10,
    }
}