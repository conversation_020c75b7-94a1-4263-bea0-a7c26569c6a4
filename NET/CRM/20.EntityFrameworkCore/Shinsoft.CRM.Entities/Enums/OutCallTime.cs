﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum OutCallTime
    {
        None = 0,

        /// <summary>
        /// 上午
        /// </summary>
        [Description("上午")]
        Morning = 1,

        /// <summary>
        /// 下午
        /// </summary>
        [Description("下午")]
        Afternoon = 2,

        /// <summary>
        /// 晚间
        /// </summary>
        [Description("晚间")]
        [EnumGroup(Visible = false)]
        Evening = 3,

        /// <summary>
        /// 夜间
        /// </summary>
        [Description("夜间")]
        [EnumGroup(Visible = false)]
        Night = 4
    }
}