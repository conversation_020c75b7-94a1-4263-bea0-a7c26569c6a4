﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    [Flags]
    public enum EventMemberFlag
    {
        None = 0,

        /// <summary>
        /// 受访者
        /// </summary>
        [Description("受访者")]
        Interviewee = 1,

        /// <summary>
        /// 协访人
        /// </summary>
        [Description("协访人")]
        Cooperator = 2,

        /// <summary>
        /// 主讲人
        /// </summary>
        [Description("主席")]
        MainSpeaker = 4,

        /// <summary>
        /// 讲者
        /// </summary>
        [Description("讲者")]
        Speaker = 8,

        /// <summary>
        /// 必要参与者
        /// </summary>
        [Description("必要参与者")]
        MustJoinMembers = 16,

        /// <summary>
        /// 其他参与者
        /// </summary>
        [Description("其他参与者")]
        OtherJoinMembers = 32,
    }
}