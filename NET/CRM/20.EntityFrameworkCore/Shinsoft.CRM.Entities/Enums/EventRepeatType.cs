﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    /// <summary>
    /// Event重复频率
    /// </summary>
    public enum EventRepeatType
    {
        /// <summary>
        /// 永不
        /// </summary>
        [Description("永不")]
        [EnumGroup(Visible = true)]
        None = 0,

        /// <summary>
        /// 每天
        /// </summary>
        [Description("每天")]
        Daily = 1,

        /// <summary>
        /// 每周
        /// </summary>
        [Description("每周")]
        Weekly = 2,

        /// <summary>
        /// 每月
        /// </summary>
        [Description("每月")]
        Monthly = 3,

        /// <summary>
        /// 每年
        /// </summary>
        [Description("每年")]
        Yearly = 4
    }
}