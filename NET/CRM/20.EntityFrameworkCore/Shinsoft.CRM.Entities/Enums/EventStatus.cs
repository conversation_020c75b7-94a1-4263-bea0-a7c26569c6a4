﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum EventStatus
    {
        None = 0,

        /// <summary>
        /// 审核中
        /// </summary>
        [Description("审核中")]
        [EnumGroup("Report")]
        AddAuditing = 1,

        /// <summary>
        /// 执行中
        /// </summary>
        [Description("执行中")]
        [EnumGroup("Report")]
        Valid = 10,

        /// <summary>
        /// 已完成
        /// </summary>
        [Description("已完成")]
        [EnumGroup("Report")]
        Finished = 20,

        /// <summary>
        /// 已取消
        /// </summary>
        [Description("已取消")]
        [EnumGroup("Report")]
        Canceled = 30,

        /// <summary>
        /// 已删除
        /// </summary>
        [Description("已删除")]
        [EnumGroup("", Visible = false)]
        Deleted = -1,

        /// <summary>
        /// 无效
        /// </summary>
        [Description("无效")]
        Invalid = -10,
    }
}