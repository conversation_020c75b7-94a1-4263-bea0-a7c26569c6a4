﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Entities
{
    public enum EventType
    {
        [Description("行为")]
        None = 0,

        /// <summary>
        /// 拜访
        /// </summary>
        [Description("拜访")]
        [EnumGroup("Report")]
        Visit = 1,

        /// <summary>
        /// 会议
        /// </summary>
        [Description("会议")]
        [EnumGroup("Report")]
        Meeting = 2,

        /// <summary>
        /// 培训
        /// </summary>
        [Description("培训")]
        [EnumGroup("Report")]
        Training = 3,
    }
}