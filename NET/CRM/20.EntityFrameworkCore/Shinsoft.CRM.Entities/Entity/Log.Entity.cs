﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using Column = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;

#pragma warning disable CS8669

namespace Shinsoft.CRM.Entities
{

	#region Log

    /// <summary>
    /// 日志
    /// </summary>
    [Description("日志")]
	[Table("Log", Schema = "log")]
	public partial class Log : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

			/// <summary>
			/// 类别
			/// </summary>
            public const string Category = "Category";

			/// <summary>
			/// 级别 
			/// </summary>
            public const string Level = "Level";

			/// <summary>
			/// 时间
			/// </summary>
            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

			/// <summary>
			/// 平台ID
			/// </summary>
            public const string PlatformId = "PlatformId";

			/// <summary>
			/// 程序ID
			/// </summary>
            public const string ProgramId = "ProgramId";

			/// <summary>
			/// 操作ID
			/// </summary>
            public const string OperateId = "OperateId";

			/// <summary>
			/// 任务ID
			/// </summary>
            public const string JobId = "JobId";

			/// <summary>
			/// 消息
			/// </summary>
            public const string Message = "Message";

			/// <summary>
			/// 执行时间
			/// </summary>
            public const string Duration = "Duration";

			/// <summary>
			/// 备注
			/// </summary>
            public const string Remark = "Remark";

			/// <summary>
			/// 用户ID
			/// </summary>
            public const string UserId = "UserId";

			/// <summary>
			/// 用户唯一名
			/// </summary>
            public const string UserUniqueName = "UserUniqueName";

			/// <summary>
			/// 用户姓名
			/// </summary>
            public const string UserDisplayName = "UserDisplayName";

			/// <summary>
			/// 当前身份ID
			/// </summary>
            public const string IdentityId = "IdentityId";

			/// <summary>
			/// 当前身份名称
			/// </summary>
            public const string IdentityDisplayName = "IdentityDisplayName";

			/// <summary>
			/// 代理人ID
			/// </summary>
            public const string AgentId = "AgentId";

			/// <summary>
			/// 代理人名称
			/// </summary>
            public const string AgentDisplayName = "AgentDisplayName";

        }

        public static partial class Foreigns
        {

			/// <summary>
			/// 平台
			/// </summary>
            public const string LogPlatform = "LogPlatform";

			/// <summary>
			/// 程序
			/// </summary>
            public const string LogProgram = "LogProgram";

			/// <summary>
			/// 操作
			/// </summary>
            public const string LogOperate = "LogOperate";

			/// <summary>
			/// 任务
			/// </summary>
            public const string LogJob = "LogJob";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// API日志
			/// </summary>
            public const string LogApi = "LogApi";

			/// <summary>
			/// 异常日志
			/// </summary>
            public const string LogException = "LogException";

			/// <summary>
			/// 接口日志
			/// </summary>
            public const string LogInterface = "LogInterface";

			/// <summary>
			/// 业务日志
			/// </summary>
            public const string LogBiz = "LogBiz";

			/// <summary>
			/// 网页日志
			/// </summary>
            public const string LogWeb = "LogWeb";

        }

        #endregion Const

        #region Construct

        public Log()
        {

        }

        public Log(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category 类别

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

		/// <summary>
        /// 类别
        /// </summary>
        [Description("类别")]
        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level 级别 

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

		/// <summary>
        /// 级别 
        /// </summary>
        [Description("级别 ")]
        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime 时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

		/// <summary>
        /// 时间
        /// </summary>
        [Description("时间")]
        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region PlatformId 平台ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private int? _PlatformId;

		/// <summary>
        /// 平台ID
        /// </summary>
        [Description("平台ID")]
        [Column(Columns.PlatformId, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual int? PlatformId
        {
            get => _PlatformId;
            set
            {
                _PlatformId = value;
                this.SetChangedColumn(_PlatformId);
            }
        }

        #endregion PlatformId

        #region ProgramId 程序ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private int? _ProgramId;

		/// <summary>
        /// 程序ID
        /// </summary>
        [Description("程序ID")]
        [Column(Columns.ProgramId, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual int? ProgramId
        {
            get => _ProgramId;
            set
            {
                _ProgramId = value;
                this.SetChangedColumn(_ProgramId);
            }
        }

        #endregion ProgramId

        #region OperateId 操作ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private int? _OperateId;

		/// <summary>
        /// 操作ID
        /// </summary>
        [Description("操作ID")]
        [Column(Columns.OperateId, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual int? OperateId
        {
            get => _OperateId;
            set
            {
                _OperateId = value;
                this.SetChangedColumn(_OperateId);
            }
        }

        #endregion OperateId

        #region JobId 任务ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private int? _JobId;

		/// <summary>
        /// 任务ID
        /// </summary>
        [Description("任务ID")]
        [Column(Columns.JobId, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual int? JobId
        {
            get => _JobId;
            set
            {
                _JobId = value;
                this.SetChangedColumn(_JobId);
            }
        }

        #endregion JobId

        #region Message 消息

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

		/// <summary>
        /// 消息
        /// </summary>
        [Description("消息")]
        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration 执行时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

		/// <summary>
        /// 执行时间
        /// </summary>
        [Description("执行时间")]
        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark 备注

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

		/// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId 用户ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

		/// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName 用户唯一名

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

		/// <summary>
        /// 用户唯一名
        /// </summary>
        [Description("用户唯一名")]
        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName 用户姓名

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

		/// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId 当前身份ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

		/// <summary>
        /// 当前身份ID
        /// </summary>
        [Description("当前身份ID")]
        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName 当前身份名称

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

		/// <summary>
        /// 当前身份名称
        /// </summary>
        [Description("当前身份名称")]
        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId 代理人ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

		/// <summary>
        /// 代理人ID
        /// </summary>
        [Description("代理人ID")]
        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName 代理人名称

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

		/// <summary>
        /// 代理人名称
        /// </summary>
        [Description("代理人名称")]
        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #endregion Properties

        #region Foreign

		#region LogPlatform 平台

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogPlatform? _LogPlatform;

		/// <summary>
        /// 平台
        /// </summary>
        [ForeignKey(Columns.PlatformId)]
        public virtual LogPlatform? LogPlatform
        {

            get => this.LazyLoad(ref _LogPlatform);
            set => _LogPlatform = value;
        }


        #endregion LogPlatform

		#region LogProgram 程序

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogProgram? _LogProgram;

		/// <summary>
        /// 程序
        /// </summary>
        [ForeignKey(Columns.ProgramId)]
        public virtual LogProgram? LogProgram
        {

            get => this.LazyLoad(ref _LogProgram);
            set => _LogProgram = value;
        }


        #endregion LogProgram

		#region LogOperate 操作

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogOperate? _LogOperate;

		/// <summary>
        /// 操作
        /// </summary>
        [ForeignKey(Columns.OperateId)]
        public virtual LogOperate? LogOperate
        {

            get => this.LazyLoad(ref _LogOperate);
            set => _LogOperate = value;
        }


        #endregion LogOperate

		#region LogJob 任务

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogJob? _LogJob;

		/// <summary>
        /// 任务
        /// </summary>
        [ForeignKey(Columns.JobId)]
        public virtual LogJob? LogJob
        {

            get => this.LazyLoad(ref _LogJob);
            set => _LogJob = value;
        }


        #endregion LogJob

        #endregion Foreign

		#region Inverse

        #region LogApi API日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogApi? _LogApi;

		/// <summary>
        /// API日志
        /// </summary>
		[InverseProperty("Log")]
        public virtual LogApi? LogApi
        {
            get => this.LazyLoad(ref _LogApi);
            set => _LogApi = value;
        }

        #endregion LogApi

        #region LogException 异常日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogException? _LogException;

		/// <summary>
        /// 异常日志
        /// </summary>
		[InverseProperty("Log")]
        public virtual LogException? LogException
        {
            get => this.LazyLoad(ref _LogException);
            set => _LogException = value;
        }

        #endregion LogException

        #region LogInterface 接口日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogInterface? _LogInterface;

		/// <summary>
        /// 接口日志
        /// </summary>
		[InverseProperty("Log")]
        public virtual LogInterface? LogInterface
        {
            get => this.LazyLoad(ref _LogInterface);
            set => _LogInterface = value;
        }

        #endregion LogInterface

        #region LogBiz 业务日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogTarget? _LogBiz;

		/// <summary>
        /// 业务日志
        /// </summary>
		[InverseProperty("Log")]
        public virtual LogTarget? LogBiz
        {
            get => this.LazyLoad(ref _LogBiz);
            set => _LogBiz = value;
        }

        #endregion LogBiz

        #region LogWeb 网页日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogWeb? _LogWeb;

		/// <summary>
        /// 网页日志
        /// </summary>
		[InverseProperty("Log")]
        public virtual LogWeb? LogWeb
        {
            get => this.LazyLoad(ref _LogWeb);
            set => _LogWeb = value;
        }

        #endregion LogWeb

		#endregion Inverse
    }

	#endregion Log


	#region LogApi

    /// <summary>
    /// API日志
    /// </summary>
    [Description("API日志")]
	[Table("LogApi", Schema = "log")]
	public partial class LogApi : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// Api类型
			/// </summary>
            public const string ApiType = "ApiType";

            public const string Succeed = "Succeed";

			/// <summary>
			/// 输入
			/// </summary>
            public const string Input = "Input";

			/// <summary>
			/// 输出
			/// </summary>
            public const string Output = "Output";

        }

        public static partial class Foreigns
        {

            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogApi()
        {

        }

        public LogApi(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region ApiType Api类型

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _ApiType;

		/// <summary>
        /// Api类型
        /// </summary>
        [Description("Api类型")]
        [Column(Columns.ApiType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 20)]
		public virtual string? ApiType
        {
            get => _ApiType;
            set
            {
                _ApiType = value;
                this.SetChangedColumn(_ApiType);
            }
        }

        #endregion ApiType

        #region Succeed

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _Succeed;

        [Column(Columns.Succeed, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? Succeed
        {
            get => _Succeed;
            set
            {
                _Succeed = value;
                this.SetChangedColumn(_Succeed);
            }
        }

        #endregion Succeed

        #region Input 输入

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Input = string.Empty;

		/// <summary>
        /// 输入
        /// </summary>
        [Description("输入")]
        [Column(Columns.Input, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Input
        {
            get => _Input;
            set
            {
                _Input = value ?? string.Empty;
                this.SetChangedColumn(_Input);
            }
        }

        #endregion Input

        #region Output 输出

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Output = string.Empty;

		/// <summary>
        /// 输出
        /// </summary>
        [Description("输出")]
        [Column(Columns.Output, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Output
        {
            get => _Output;
            set
            {
                _Output = value ?? string.Empty;
                this.SetChangedColumn(_Output);
            }
        }

        #endregion Output

        #endregion Properties

        #region Foreign

		#region Log

        [NotMapped, XmlIgnore, JsonIgnore]
        private Log? _Log;

        [ForeignKey(Columns.ID)]
        public virtual Log Log
        {

            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }


        #endregion Log

        #endregion Foreign
    }

	#endregion LogApi


	#region LogCategory

	[Table("LogCategory", Schema = "log")]
	public partial class LogCategory : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Category = "Category";

        }

        #endregion Const

        #region Construct

        public LogCategory()
        {

        }

        public LogCategory(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Category = string.Empty;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string Category
        {
            get => _Category;
            set
            {
                _Category = value ?? string.Empty;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #endregion Properties
    }

	#endregion LogCategory


	#region LogException

    /// <summary>
    /// 异常日志
    /// </summary>
    [Description("异常日志")]
	[Table("LogException", Schema = "log")]
	public partial class LogException : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// 异常
			/// </summary>
            public const string Exception = "Exception";

			/// <summary>
			/// 堆栈跟踪
			/// </summary>
            public const string StackTrace = "StackTrace";

        }

        public static partial class Foreigns
        {

            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogException()
        {

        }

        public LogException(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region Exception 异常

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

		/// <summary>
        /// 异常
        /// </summary>
        [Description("异常")]
        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace 堆栈跟踪

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _StackTrace = string.Empty;

		/// <summary>
        /// 堆栈跟踪
        /// </summary>
        [Description("堆栈跟踪")]
        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value ?? string.Empty;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties

        #region Foreign

		#region Log

        [NotMapped, XmlIgnore, JsonIgnore]
        private Log? _Log;

        [ForeignKey(Columns.ID)]
        public virtual Log Log
        {

            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }


        #endregion Log

        #endregion Foreign
    }

	#endregion LogException


	#region LogInterface

    /// <summary>
    /// 接口日志
    /// </summary>
    [Description("接口日志")]
	[Table("LogInterface", Schema = "log")]
	public partial class LogInterface : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// 接口地址
			/// </summary>
            public const string Address = "Address";

			/// <summary>
			/// 接口方法
			/// </summary>
            public const string Method = "Method";

			/// <summary>
			/// 接口输入
			/// </summary>
            public const string Request = "Request";

			/// <summary>
			/// 接口输出
			/// </summary>
            public const string Response = "Response";

        }

        public static partial class Foreigns
        {

            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogInterface()
        {

        }

        public LogInterface(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region Address 接口地址

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Address;

		/// <summary>
        /// 接口地址
        /// </summary>
        [Description("接口地址")]
        [Column(Columns.Address, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Address
        {
            get => _Address;
            set
            {
                _Address = value;
                this.SetChangedColumn(_Address);
            }
        }

        #endregion Address

        #region Method 接口方法

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

		/// <summary>
        /// 接口方法
        /// </summary>
        [Description("接口方法")]
        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Request 接口输入

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Request;

		/// <summary>
        /// 接口输入
        /// </summary>
        [Description("接口输入")]
        [Column(Columns.Request, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Request
        {
            get => _Request;
            set
            {
                _Request = value;
                this.SetChangedColumn(_Request);
            }
        }

        #endregion Request

        #region Response 接口输出

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Response;

		/// <summary>
        /// 接口输出
        /// </summary>
        [Description("接口输出")]
        [Column(Columns.Response, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Response
        {
            get => _Response;
            set
            {
                _Response = value;
                this.SetChangedColumn(_Response);
            }
        }

        #endregion Response

        #endregion Properties

        #region Foreign

		#region Log

        [NotMapped, XmlIgnore, JsonIgnore]
        private Log? _Log;

        [ForeignKey(Columns.ID)]
        public virtual Log Log
        {

            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }


        #endregion Log

        #endregion Foreign
    }

	#endregion LogInterface


	#region LogJob

	[Table("LogJob", Schema = "log")]
	public partial class LogJob : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Job = "Job";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 日志
			/// </summary>
            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogJob()
        {

        }

        public LogJob(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region Job

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Job;

        [Column(Columns.Job, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Job
        {
            get => _Job;
            set
            {
                _Job = value;
                this.SetChangedColumn(_Job);
            }
        }

        #endregion Job

        #endregion Properties

		#region Inverse

        #region Log 日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<Log>? _Log;

		/// <summary>
        /// 日志
        /// </summary>
		[InverseProperty("LogJob")]
        public virtual List<Log> Log
        {
            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }

        #endregion Log

		#endregion Inverse
    }

	#endregion LogJob


	#region LogOperate

	[Table("LogOperate", Schema = "log")]
	public partial class LogOperate : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Operate = "Operate";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 日志
			/// </summary>
            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogOperate()
        {

        }

        public LogOperate(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #endregion Properties

		#region Inverse

        #region Log 日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<Log>? _Log;

		/// <summary>
        /// 日志
        /// </summary>
		[InverseProperty("LogOperate")]
        public virtual List<Log> Log
        {
            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }

        #endregion Log

		#endregion Inverse
    }

	#endregion LogOperate


	#region LogPlatform

	[Table("LogPlatform", Schema = "log")]
	public partial class LogPlatform : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Platform = "Platform";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 日志
			/// </summary>
            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogPlatform()
        {

        }

        public LogPlatform(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #endregion Properties

		#region Inverse

        #region Log 日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<Log>? _Log;

		/// <summary>
        /// 日志
        /// </summary>
		[InverseProperty("LogPlatform")]
        public virtual List<Log> Log
        {
            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }

        #endregion Log

		#endregion Inverse
    }

	#endregion LogPlatform


	#region LogProgram

	[Table("LogProgram", Schema = "log")]
	public partial class LogProgram : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Program = "Program";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 日志
			/// </summary>
            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogProgram()
        {

        }

        public LogProgram(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #endregion Properties

		#region Inverse

        #region Log 日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<Log>? _Log;

		/// <summary>
        /// 日志
        /// </summary>
		[InverseProperty("LogProgram")]
        public virtual List<Log> Log
        {
            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }

        #endregion Log

		#endregion Inverse
    }

	#endregion LogProgram


	#region LogTarget

    /// <summary>
    /// 业务日志
    /// </summary>
    [Description("业务日志")]
	[Table("LogTarget", Schema = "log")]
	public partial class LogTarget : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// 业务类型ID
			/// </summary>
            public const string LogTargetTypeId = "LogTargetTypeId";

			/// <summary>
			/// 目标
			/// </summary>
            public const string TargetName = "TargetName";

			/// <summary>
			/// 目标ID
			/// </summary>
            public const string TargetId = "TargetId";

        }

        public static partial class Foreigns
        {

            public const string Log = "Log";

			/// <summary>
			/// 业务类型
			/// </summary>
            public const string LogTargetType = "LogTargetType";

        }

        #endregion Const

        #region Construct

        public LogTarget()
        {

        }

        public LogTarget(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region LogTargetTypeId 业务类型ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private int? _LogTargetTypeId;

		/// <summary>
        /// 业务类型ID
        /// </summary>
        [Description("业务类型ID")]
        [Column(Columns.LogTargetTypeId, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual int? LogTargetTypeId
        {
            get => _LogTargetTypeId;
            set
            {
                _LogTargetTypeId = value;
                this.SetChangedColumn(_LogTargetTypeId);
            }
        }

        #endregion LogTargetTypeId

        #region TargetName 目标

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

		/// <summary>
        /// 目标
        /// </summary>
        [Description("目标")]
        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId 目标ID

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

		/// <summary>
        /// 目标ID
        /// </summary>
        [Description("目标ID")]
        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #endregion Properties

        #region Foreign

		#region Log

        [NotMapped, XmlIgnore, JsonIgnore]
        private Log? _Log;

        [ForeignKey(Columns.ID)]
        public virtual Log Log
        {

            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }


        #endregion Log

		#region LogTargetType 业务类型

        [NotMapped, XmlIgnore, JsonIgnore]
        private LogTargetType? _LogTargetType;

		/// <summary>
        /// 业务类型
        /// </summary>
        [ForeignKey(Columns.LogTargetTypeId)]
        public virtual LogTargetType? LogTargetType
        {

            get => this.LazyLoad(ref _LogTargetType);
            set => _LogTargetType = value;
        }


        #endregion LogTargetType

        #endregion Foreign
    }

	#endregion LogTarget


	#region LogTargetType

    /// <summary>
    /// 目标类型
    /// </summary>
    [Description("目标类型")]
	[Table("LogTargetType", Schema = "log")]
	public partial class LogTargetType : Entity, ITable, IPrimaryKey<int>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

			/// <summary>
			/// 目标类型
			/// </summary>
            public const string TargetType = "TargetType";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 业务日志
			/// </summary>
            public const string LogBiz = "LogBiz";

        }

        #endregion Const

        #region Construct

        public LogTargetType()
        {

        }

        public LogTargetType(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Int32, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public virtual int ID { get; set; }

        #endregion ID

        #region TargetType 目标类型

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

		/// <summary>
        /// 目标类型
        /// </summary>
        [Description("目标类型")]
        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #endregion Properties

		#region Inverse

        #region LogBiz 业务日志

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<LogTarget>? _LogBiz;

		/// <summary>
        /// 业务日志
        /// </summary>
		[InverseProperty("LogTargetType")]
        public virtual List<LogTarget> LogBiz
        {
            get => this.LazyLoad(ref _LogBiz);
            set => _LogBiz = value;
        }

        #endregion LogBiz

		#endregion Inverse
    }

	#endregion LogTargetType


	#region LogWeb

    /// <summary>
    /// 网页日志
    /// </summary>
    [Description("网页日志")]
	[Table("LogWeb", Schema = "log")]
	public partial class LogWeb : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string Url = "Url";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

        }

        public static partial class Foreigns
        {

            public const string Log = "Log";

        }

        #endregion Const

        #region Construct

        public LogWeb()
        {

        }

        public LogWeb(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Url = string.Empty;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Url
        {
            get => _Url;
            set
            {
                _Url = value ?? string.Empty;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #endregion Properties

        #region Foreign

		#region Log

        [NotMapped, XmlIgnore, JsonIgnore]
        private Log? _Log;

        [ForeignKey(Columns.ID)]
        public virtual Log Log
        {

            get => this.LazyLoad(ref _Log);
            set => _Log = value;
        }


        #endregion Log

        #endregion Foreign
    }

	#endregion LogWeb


	#region VwLog

	[Table("VwLog", Schema = "log")]
	public partial class VwLog : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Job = "Job";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

            public const string ApiType = "ApiType";

            public const string Succeed = "Succeed";

            public const string Input = "Input";

            public const string Output = "Output";

            public const string InterfaceAddress = "InterfaceAddress";

            public const string InterfaceMethod = "InterfaceMethod";

            public const string InterfaceRequest = "InterfaceRequest";

            public const string InterfaceResponse = "InterfaceResponse";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

        }

        #endregion Const

        #region Construct

        public VwLog()
        {

        }

        public VwLog(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Job

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Job;

        [Column(Columns.Job, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Job
        {
            get => _Job;
            set
            {
                _Job = value;
                this.SetChangedColumn(_Job);
            }
        }

        #endregion Job

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #region ApiType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _ApiType;

        [Column(Columns.ApiType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 20)]
		public virtual string? ApiType
        {
            get => _ApiType;
            set
            {
                _ApiType = value;
                this.SetChangedColumn(_ApiType);
            }
        }

        #endregion ApiType

        #region Succeed

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _Succeed;

        [Column(Columns.Succeed, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? Succeed
        {
            get => _Succeed;
            set
            {
                _Succeed = value;
                this.SetChangedColumn(_Succeed);
            }
        }

        #endregion Succeed

        #region Input

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Input;

        [Column(Columns.Input, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Input
        {
            get => _Input;
            set
            {
                _Input = value;
                this.SetChangedColumn(_Input);
            }
        }

        #endregion Input

        #region Output

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Output;

        [Column(Columns.Output, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Output
        {
            get => _Output;
            set
            {
                _Output = value;
                this.SetChangedColumn(_Output);
            }
        }

        #endregion Output

        #region InterfaceAddress

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceAddress;

        [Column(Columns.InterfaceAddress, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? InterfaceAddress
        {
            get => _InterfaceAddress;
            set
            {
                _InterfaceAddress = value;
                this.SetChangedColumn(_InterfaceAddress);
            }
        }

        #endregion InterfaceAddress

        #region InterfaceMethod

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceMethod;

        [Column(Columns.InterfaceMethod, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? InterfaceMethod
        {
            get => _InterfaceMethod;
            set
            {
                _InterfaceMethod = value;
                this.SetChangedColumn(_InterfaceMethod);
            }
        }

        #endregion InterfaceMethod

        #region InterfaceRequest

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceRequest;

        [Column(Columns.InterfaceRequest, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceRequest
        {
            get => _InterfaceRequest;
            set
            {
                _InterfaceRequest = value;
                this.SetChangedColumn(_InterfaceRequest);
            }
        }

        #endregion InterfaceRequest

        #region InterfaceResponse

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceResponse;

        [Column(Columns.InterfaceResponse, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceResponse
        {
            get => _InterfaceResponse;
            set
            {
                _InterfaceResponse = value;
                this.SetChangedColumn(_InterfaceResponse);
            }
        }

        #endregion InterfaceResponse

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties
    }

	#endregion VwLog


	#region VwLogApi

	[Table("VwLogApi", Schema = "log")]
	public partial class VwLogApi : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

            public const string ApiType = "ApiType";

            public const string Succeed = "Succeed";

            public const string Input = "Input";

            public const string Output = "Output";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

        }

        #endregion Const

        #region Construct

        public VwLogApi()
        {

        }

        public VwLogApi(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #region ApiType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _ApiType;

        [Column(Columns.ApiType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 20)]
		public virtual string? ApiType
        {
            get => _ApiType;
            set
            {
                _ApiType = value;
                this.SetChangedColumn(_ApiType);
            }
        }

        #endregion ApiType

        #region Succeed

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _Succeed;

        [Column(Columns.Succeed, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? Succeed
        {
            get => _Succeed;
            set
            {
                _Succeed = value;
                this.SetChangedColumn(_Succeed);
            }
        }

        #endregion Succeed

        #region Input

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Input;

        [Column(Columns.Input, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Input
        {
            get => _Input;
            set
            {
                _Input = value;
                this.SetChangedColumn(_Input);
            }
        }

        #endregion Input

        #region Output

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Output;

        [Column(Columns.Output, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Output
        {
            get => _Output;
            set
            {
                _Output = value;
                this.SetChangedColumn(_Output);
            }
        }

        #endregion Output

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties
    }

	#endregion VwLogApi


	#region VwLogBiz

	[Table("VwLogBiz", Schema = "log")]
	public partial class VwLogBiz : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

        }

        #endregion Const

        #region Construct

        public VwLogBiz()
        {

        }

        public VwLogBiz(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties
    }

	#endregion VwLogBiz


	#region VwLogException

	[Table("VwLogException", Schema = "log")]
	public partial class VwLogException : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

        }

        #endregion Const

        #region Construct

        public VwLogException()
        {

        }

        public VwLogException(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties
    }

	#endregion VwLogException


	#region VwLogInterface

	[Table("VwLogInterface", Schema = "log")]
	public partial class VwLogInterface : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

            public const string InterfaceAddress = "InterfaceAddress";

            public const string InterfaceMethod = "InterfaceMethod";

            public const string InterfaceRequest = "InterfaceRequest";

            public const string InterfaceResponse = "InterfaceResponse";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

        }

        #endregion Const

        #region Construct

        public VwLogInterface()
        {

        }

        public VwLogInterface(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #region InterfaceAddress

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceAddress;

        [Column(Columns.InterfaceAddress, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? InterfaceAddress
        {
            get => _InterfaceAddress;
            set
            {
                _InterfaceAddress = value;
                this.SetChangedColumn(_InterfaceAddress);
            }
        }

        #endregion InterfaceAddress

        #region InterfaceMethod

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceMethod;

        [Column(Columns.InterfaceMethod, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? InterfaceMethod
        {
            get => _InterfaceMethod;
            set
            {
                _InterfaceMethod = value;
                this.SetChangedColumn(_InterfaceMethod);
            }
        }

        #endregion InterfaceMethod

        #region InterfaceRequest

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceRequest;

        [Column(Columns.InterfaceRequest, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceRequest
        {
            get => _InterfaceRequest;
            set
            {
                _InterfaceRequest = value;
                this.SetChangedColumn(_InterfaceRequest);
            }
        }

        #endregion InterfaceRequest

        #region InterfaceResponse

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceResponse;

        [Column(Columns.InterfaceResponse, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceResponse
        {
            get => _InterfaceResponse;
            set
            {
                _InterfaceResponse = value;
                this.SetChangedColumn(_InterfaceResponse);
            }
        }

        #endregion InterfaceResponse

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #endregion Properties
    }

	#endregion VwLogInterface


	#region VwLogJob

	[Table("VwLogJob", Schema = "log")]
	public partial class VwLogJob : Entity, IView, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

            public const string CompanyCode = "CompanyCode";

            public const string CompanyName = "CompanyName";

            public const string Category = "Category";

            public const string Level = "Level";

            public const string LogTime = "LogTime";

            public const string Logger = "Logger";

            public const string Platform = "Platform";

            public const string Program = "Program";

            public const string Operate = "Operate";

            public const string Job = "Job";

            public const string Message = "Message";

            public const string Duration = "Duration";

            public const string Remark = "Remark";

            public const string InterfaceAddress = "InterfaceAddress";

            public const string InterfaceMethod = "InterfaceMethod";

            public const string InterfaceRequest = "InterfaceRequest";

            public const string InterfaceResponse = "InterfaceResponse";

            public const string Exception = "Exception";

            public const string StackTrace = "StackTrace";

            public const string UserId = "UserId";

            public const string UserUniqueName = "UserUniqueName";

            public const string UserDisplayName = "UserDisplayName";

            public const string IdentityId = "IdentityId";

            public const string IdentityDisplayName = "IdentityDisplayName";

            public const string AgentId = "AgentId";

            public const string AgentDisplayName = "AgentDisplayName";

            public const string TargetType = "TargetType";

            public const string TargetName = "TargetName";

            public const string TargetId = "TargetId";

            public const string Controller = "Controller";

            public const string Action = "Action";

            public const string Method = "Method";

            public const string Headers = "Headers";

            public const string Url = "Url";

            public const string IsAuthenticated = "IsAuthenticated";

            public const string QueryString = "QueryString";

            public const string UserAgent = "UserAgent";

            public const string Identity = "Identity";

            public const string Host = "Host";

            public const string IP = "IP";

        }

        #endregion Const

        #region Construct

        public VwLogJob()
        {

        }

        public VwLogJob(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region CompanyCode

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyCode;

        [Column(Columns.CompanyCode, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? CompanyCode
        {
            get => _CompanyCode;
            set
            {
                _CompanyCode = value;
                this.SetChangedColumn(_CompanyCode);
            }
        }

        #endregion CompanyCode

        #region CompanyName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _CompanyName;

        [Column(Columns.CompanyName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? CompanyName
        {
            get => _CompanyName;
            set
            {
                _CompanyName = value;
                this.SetChangedColumn(_CompanyName);
            }
        }

        #endregion CompanyName

        #region Category

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Category;

        [Column(Columns.Category, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Category
        {
            get => _Category;
            set
            {
                _Category = value;
                this.SetChangedColumn(_Category);
            }
        }

        #endregion Category

        #region Level

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Level;

        [Column(Columns.Level, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Level
        {
            get => _Level;
            set
            {
                _Level = value;
                this.SetChangedColumn(_Level);
            }
        }

        #endregion Level

        #region LogTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime _LogTime;

        [Column(Columns.LogTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual DateTime LogTime
        {
            get => _LogTime;
            set
            {
                _LogTime = value;
                this.SetChangedColumn(_LogTime);
            }
        }

        #endregion LogTime

        #region Logger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Logger;

        [Column(Columns.Logger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Logger
        {
            get => _Logger;
            set
            {
                _Logger = value;
                this.SetChangedColumn(_Logger);
            }
        }

        #endregion Logger

        #region Platform

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Platform;

        [Column(Columns.Platform, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Platform
        {
            get => _Platform;
            set
            {
                _Platform = value;
                this.SetChangedColumn(_Platform);
            }
        }

        #endregion Platform

        #region Program

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Program;

        [Column(Columns.Program, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Program
        {
            get => _Program;
            set
            {
                _Program = value;
                this.SetChangedColumn(_Program);
            }
        }

        #endregion Program

        #region Operate

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Operate;

        [Column(Columns.Operate, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Operate
        {
            get => _Operate;
            set
            {
                _Operate = value;
                this.SetChangedColumn(_Operate);
            }
        }

        #endregion Operate

        #region Job

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Job;

        [Column(Columns.Job, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Job
        {
            get => _Job;
            set
            {
                _Job = value;
                this.SetChangedColumn(_Job);
            }
        }

        #endregion Job

        #region Message

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Message;

        [Column(Columns.Message, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Message
        {
            get => _Message;
            set
            {
                _Message = value;
                this.SetChangedColumn(_Message);
            }
        }

        #endregion Message

        #region Duration

        [NotMapped, XmlIgnore, JsonIgnore]
        private long? _Duration;

        [Column(Columns.Duration, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual long? Duration
        {
            get => _Duration;
            set
            {
                _Duration = value;
                this.SetChangedColumn(_Duration);
            }
        }

        #endregion Duration

        #region Remark

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Remark;

        [Column(Columns.Remark, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Remark
        {
            get => _Remark;
            set
            {
                _Remark = value;
                this.SetChangedColumn(_Remark);
            }
        }

        #endregion Remark

        #region InterfaceAddress

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceAddress;

        [Column(Columns.InterfaceAddress, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? InterfaceAddress
        {
            get => _InterfaceAddress;
            set
            {
                _InterfaceAddress = value;
                this.SetChangedColumn(_InterfaceAddress);
            }
        }

        #endregion InterfaceAddress

        #region InterfaceMethod

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceMethod;

        [Column(Columns.InterfaceMethod, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? InterfaceMethod
        {
            get => _InterfaceMethod;
            set
            {
                _InterfaceMethod = value;
                this.SetChangedColumn(_InterfaceMethod);
            }
        }

        #endregion InterfaceMethod

        #region InterfaceRequest

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceRequest;

        [Column(Columns.InterfaceRequest, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceRequest
        {
            get => _InterfaceRequest;
            set
            {
                _InterfaceRequest = value;
                this.SetChangedColumn(_InterfaceRequest);
            }
        }

        #endregion InterfaceRequest

        #region InterfaceResponse

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _InterfaceResponse;

        [Column(Columns.InterfaceResponse, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? InterfaceResponse
        {
            get => _InterfaceResponse;
            set
            {
                _InterfaceResponse = value;
                this.SetChangedColumn(_InterfaceResponse);
            }
        }

        #endregion InterfaceResponse

        #region Exception

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Exception;

        [Column(Columns.Exception, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Exception
        {
            get => _Exception;
            set
            {
                _Exception = value;
                this.SetChangedColumn(_Exception);
            }
        }

        #endregion Exception

        #region StackTrace

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _StackTrace;

        [Column(Columns.StackTrace, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? StackTrace
        {
            get => _StackTrace;
            set
            {
                _StackTrace = value;
                this.SetChangedColumn(_StackTrace);
            }
        }

        #endregion StackTrace

        #region UserId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserId;

        [Column(Columns.UserId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserId
        {
            get => _UserId;
            set
            {
                _UserId = value;
                this.SetChangedColumn(_UserId);
            }
        }

        #endregion UserId

        #region UserUniqueName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserUniqueName;

        [Column(Columns.UserUniqueName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserUniqueName
        {
            get => _UserUniqueName;
            set
            {
                _UserUniqueName = value;
                this.SetChangedColumn(_UserUniqueName);
            }
        }

        #endregion UserUniqueName

        #region UserDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserDisplayName;

        [Column(Columns.UserDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? UserDisplayName
        {
            get => _UserDisplayName;
            set
            {
                _UserDisplayName = value;
                this.SetChangedColumn(_UserDisplayName);
            }
        }

        #endregion UserDisplayName

        #region IdentityId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityId;

        [Column(Columns.IdentityId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityId
        {
            get => _IdentityId;
            set
            {
                _IdentityId = value;
                this.SetChangedColumn(_IdentityId);
            }
        }

        #endregion IdentityId

        #region IdentityDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IdentityDisplayName;

        [Column(Columns.IdentityDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IdentityDisplayName
        {
            get => _IdentityDisplayName;
            set
            {
                _IdentityDisplayName = value;
                this.SetChangedColumn(_IdentityDisplayName);
            }
        }

        #endregion IdentityDisplayName

        #region AgentId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentId;

        [Column(Columns.AgentId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentId
        {
            get => _AgentId;
            set
            {
                _AgentId = value;
                this.SetChangedColumn(_AgentId);
            }
        }

        #endregion AgentId

        #region AgentDisplayName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AgentDisplayName;

        [Column(Columns.AgentDisplayName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? AgentDisplayName
        {
            get => _AgentDisplayName;
            set
            {
                _AgentDisplayName = value;
                this.SetChangedColumn(_AgentDisplayName);
            }
        }

        #endregion AgentDisplayName

        #region TargetType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetType;

        [Column(Columns.TargetType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetType
        {
            get => _TargetType;
            set
            {
                _TargetType = value;
                this.SetChangedColumn(_TargetType);
            }
        }

        #endregion TargetType

        #region TargetName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetName;

        [Column(Columns.TargetName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? TargetName
        {
            get => _TargetName;
            set
            {
                _TargetName = value;
                this.SetChangedColumn(_TargetName);
            }
        }

        #endregion TargetName

        #region TargetId

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _TargetId;

        [Column(Columns.TargetId, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? TargetId
        {
            get => _TargetId;
            set
            {
                _TargetId = value;
                this.SetChangedColumn(_TargetId);
            }
        }

        #endregion TargetId

        #region Controller

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Controller;

        [Column(Columns.Controller, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Controller
        {
            get => _Controller;
            set
            {
                _Controller = value;
                this.SetChangedColumn(_Controller);
            }
        }

        #endregion Controller

        #region Action

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Action;

        [Column(Columns.Action, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Action
        {
            get => _Action;
            set
            {
                _Action = value;
                this.SetChangedColumn(_Action);
            }
        }

        #endregion Action

        #region Method

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Method;

        [Column(Columns.Method, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Method
        {
            get => _Method;
            set
            {
                _Method = value;
                this.SetChangedColumn(_Method);
            }
        }

        #endregion Method

        #region Headers

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Headers;

        [Column(Columns.Headers, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Headers
        {
            get => _Headers;
            set
            {
                _Headers = value;
                this.SetChangedColumn(_Headers);
            }
        }

        #endregion Headers

        #region Url

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Url;

        [Column(Columns.Url, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? Url
        {
            get => _Url;
            set
            {
                _Url = value;
                this.SetChangedColumn(_Url);
            }
        }

        #endregion Url

        #region IsAuthenticated

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool? _IsAuthenticated;

        [Column(Columns.IsAuthenticated, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual bool? IsAuthenticated
        {
            get => _IsAuthenticated;
            set
            {
                _IsAuthenticated = value;
                this.SetChangedColumn(_IsAuthenticated);
            }
        }

        #endregion IsAuthenticated

        #region QueryString

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _QueryString;

        [Column(Columns.QueryString, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? QueryString
        {
            get => _QueryString;
            set
            {
                _QueryString = value;
                this.SetChangedColumn(_QueryString);
            }
        }

        #endregion QueryString

        #region UserAgent

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _UserAgent;

        [Column(Columns.UserAgent, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? UserAgent
        {
            get => _UserAgent;
            set
            {
                _UserAgent = value;
                this.SetChangedColumn(_UserAgent);
            }
        }

        #endregion UserAgent

        #region Identity

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Identity;

        [Column(Columns.Identity, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Identity
        {
            get => _Identity;
            set
            {
                _Identity = value;
                this.SetChangedColumn(_Identity);
            }
        }

        #endregion Identity

        #region Host

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Host;

        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 200)]
		public virtual string? Host
        {
            get => _Host;
            set
            {
                _Host = value;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region IP

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _IP;

        [Column(Columns.IP, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? IP
        {
            get => _IP;
            set
            {
                _IP = value;
                this.SetChangedColumn(_IP);
            }
        }

        #endregion IP

        #endregion Properties
    }

	#endregion VwLogJob

}

#pragma warning restore CS8669
