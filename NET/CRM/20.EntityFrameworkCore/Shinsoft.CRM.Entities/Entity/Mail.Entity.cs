﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using Column = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;
using Shinsoft.Core.Mail;

#pragma warning disable CS8669

namespace Shinsoft.CRM.Entities
{

	#region Mail

    /// <summary>
    /// 邮件
    /// </summary>
    [Description("邮件")]
	[Table("Mail", Schema = "mail")]
	public partial class Mail : Entity, ITable, IDeleteable, IOperateInfo, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string MailServerId = "MailServerId";

            public const string Trigger = "Trigger";

            public const string ObjectType = "ObjectType";

            public const string ObjectId = "ObjectId";

            public const string ObjectName = "ObjectName";

            public const string IsHtmlBody = "IsHtmlBody";

            public const string From = "From";

            public const string RealFrom = "RealFrom";

            public const string DebugTo = "DebugTo";

            public const string To = "To";

            public const string Cc = "Cc";

            public const string Bcc = "Bcc";

            public const string Subject = "Subject";

            public const string Content = "Content";

            public const string AttachmentPaths = "AttachmentPaths";

            public const string EnumStatus = "EnumStatus";

            public const string PlanSendTime = "PlanSendTime";

            public const string SendTime = "SendTime";

            public const string SendCount = "SendCount";

            public const string SendMessage = "SendMessage";

			/// <summary>
			/// 逻辑删除标记
			/// </summary>
            public const string Deleted = "Deleted";

			/// <summary>
			/// 创建人
			/// </summary>
            public const string Creator = "Creator";

			/// <summary>
			/// 创建时间
			/// </summary>
            public const string CreateTime = "CreateTime";

			/// <summary>
			/// 最后修改人
			/// </summary>
            public const string LastEditor = "LastEditor";

			/// <summary>
			/// 最后修改时间
			/// </summary>
            public const string LastEditTime = "LastEditTime";

        }

        public static partial class Foreigns
        {

            public const string MailServer = "MailServer";

        }

        public static partial class Inverses
        {

            public const string Attachments = "Attachments";

        }

        #endregion Const

        #region Construct

        public Mail()
        {

        }

        public Mail(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region MailServerId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid? _MailServerId;

        [Column(Columns.MailServerId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual Guid? MailServerId
        {
            get => _MailServerId;
            set
            {
                _MailServerId = value;
                this.SetChangedColumn(_MailServerId);
            }
        }

        #endregion MailServerId

        #region Trigger

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Trigger = string.Empty;

        [Column(Columns.Trigger, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string Trigger
        {
            get => _Trigger;
            set
            {
                _Trigger = value ?? string.Empty;
                this.SetChangedColumn(_Trigger);
            }
        }

        #endregion Trigger

        #region ObjectType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _ObjectType = string.Empty;

        [Column(Columns.ObjectType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 200)]
		public virtual string ObjectType
        {
            get => _ObjectType;
            set
            {
                _ObjectType = value ?? string.Empty;
                this.SetChangedColumn(_ObjectType);
            }
        }

        #endregion ObjectType

        #region ObjectId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid? _ObjectId;

        [Column(Columns.ObjectId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual Guid? ObjectId
        {
            get => _ObjectId;
            set
            {
                _ObjectId = value;
                this.SetChangedColumn(_ObjectId);
            }
        }

        #endregion ObjectId

        #region ObjectName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _ObjectName = string.Empty;

        [Column(Columns.ObjectName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string ObjectName
        {
            get => _ObjectName;
            set
            {
                _ObjectName = value ?? string.Empty;
                this.SetChangedColumn(_ObjectName);
            }
        }

        #endregion ObjectName

        #region IsHtmlBody

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _IsHtmlBody;

        [Column(Columns.IsHtmlBody, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool IsHtmlBody
        {
            get => _IsHtmlBody;
            set
            {
                _IsHtmlBody = value;
                this.SetChangedColumn(_IsHtmlBody);
            }
        }

        #endregion IsHtmlBody

        #region From

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _From = string.Empty;

        [Column(Columns.From, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string From
        {
            get => _From;
            set
            {
                _From = value ?? string.Empty;
                this.SetChangedColumn(_From);
            }
        }

        #endregion From

        #region RealFrom

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _RealFrom = string.Empty;

        [Column(Columns.RealFrom, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string RealFrom
        {
            get => _RealFrom;
            set
            {
                _RealFrom = value ?? string.Empty;
                this.SetChangedColumn(_RealFrom);
            }
        }

        #endregion RealFrom

        #region DebugTo

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _DebugTo = string.Empty;

        [Column(Columns.DebugTo, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string DebugTo
        {
            get => _DebugTo;
            set
            {
                _DebugTo = value ?? string.Empty;
                this.SetChangedColumn(_DebugTo);
            }
        }

        #endregion DebugTo

        #region To

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _To = string.Empty;

        [Column(Columns.To, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string To
        {
            get => _To;
            set
            {
                _To = value ?? string.Empty;
                this.SetChangedColumn(_To);
            }
        }

        #endregion To

        #region Cc

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Cc = string.Empty;

        [Column(Columns.Cc, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Cc
        {
            get => _Cc;
            set
            {
                _Cc = value ?? string.Empty;
                this.SetChangedColumn(_Cc);
            }
        }

        #endregion Cc

        #region Bcc

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Bcc = string.Empty;

        [Column(Columns.Bcc, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Bcc
        {
            get => _Bcc;
            set
            {
                _Bcc = value ?? string.Empty;
                this.SetChangedColumn(_Bcc);
            }
        }

        #endregion Bcc

        #region Subject

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Subject = string.Empty;

        [Column(Columns.Subject, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 1000)]
		public virtual string Subject
        {
            get => _Subject;
            set
            {
                _Subject = value ?? string.Empty;
                this.SetChangedColumn(_Subject);
            }
        }

        #endregion Subject

        #region Content

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Content = string.Empty;

        [Column(Columns.Content, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Content
        {
            get => _Content;
            set
            {
                _Content = value ?? string.Empty;
                this.SetChangedColumn(_Content);
            }
        }

        #endregion Content

        #region AttachmentPaths

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _AttachmentPaths;

        [Column(Columns.AttachmentPaths, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual string? AttachmentPaths
        {
            get => _AttachmentPaths;
            set
            {
                _AttachmentPaths = value;
                this.SetChangedColumn(_AttachmentPaths);
            }
        }

        #endregion AttachmentPaths

        #region EnumStatus

        [NotMapped, XmlIgnore, JsonIgnore]
        private MailStatus _EnumStatus;

        [Column(Columns.EnumStatus, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual MailStatus EnumStatus
        {
            get => _EnumStatus;
            set
            {
                _EnumStatus = value;
                this.SetChangedColumn(_EnumStatus);
            }
        }
		[NotMapped]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

        #endregion EnumStatus

        #region PlanSendTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _PlanSendTime;

        [Column(Columns.PlanSendTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? PlanSendTime
        {
            get => _PlanSendTime;
            set
            {
                _PlanSendTime = value;
                this.SetChangedColumn(_PlanSendTime);
            }
        }

        #endregion PlanSendTime

        #region SendTime

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _SendTime;

        [Column(Columns.SendTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? SendTime
        {
            get => _SendTime;
            set
            {
                _SendTime = value;
                this.SetChangedColumn(_SendTime);
            }
        }

        #endregion SendTime

        #region SendCount

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _SendCount;

        [Column(Columns.SendCount, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int SendCount
        {
            get => _SendCount;
            set
            {
                _SendCount = value;
                this.SetChangedColumn(_SendCount);
            }
        }

        #endregion SendCount

        #region SendMessage

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _SendMessage = string.Empty;

        [Column(Columns.SendMessage, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string SendMessage
        {
            get => _SendMessage;
            set
            {
                _SendMessage = value ?? string.Empty;
                this.SetChangedColumn(_SendMessage);
            }
        }

        #endregion SendMessage

        #region Deleted 逻辑删除标记

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _Deleted;

		/// <summary>
        /// 逻辑删除标记
        /// </summary>
        [Description("逻辑删除标记")]
        [Column(Columns.Deleted, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool Deleted
        {
            get => _Deleted;
            set
            {
                _Deleted = value;
                this.SetChangedColumn(_Deleted);
            }
        }

        #endregion Deleted

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Creator;

		/// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column(Columns.Creator, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Creator
        {
            get => _Creator;
            set
            {
                _Creator = value;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _CreateTime;

		/// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column(Columns.CreateTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime

        #region LastEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _LastEditor;

		/// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column(Columns.LastEditor, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? LastEditor
        {
            get => _LastEditor;
            set
            {
                _LastEditor = value;
                this.SetChangedColumn(_LastEditor);
            }
        }

        #endregion LastEditor

        #region LastEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _LastEditTime;

		/// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column(Columns.LastEditTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? LastEditTime
        {
            get => _LastEditTime;
            set
            {
                _LastEditTime = value;
                this.SetChangedColumn(_LastEditTime);
            }
        }

        #endregion LastEditTime

        #endregion Properties

        #region Foreign

		#region MailServer

        [NotMapped, XmlIgnore, JsonIgnore]
        private MailServer? _MailServer;

        [ForeignKey(Columns.MailServerId)]
        public virtual MailServer? MailServer
        {

            get => this.LazyLoad(ref _MailServer);
            set => _MailServer = value;
        }


        #endregion MailServer

        #endregion Foreign

		#region Inverse

        #region Attachments

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<MailAttachment>? _Attachments;

		[InverseProperty("Mail")]
        public virtual List<MailAttachment> Attachments
        {
            get => this.LazyLoad(ref _Attachments);
            set => _Attachments = value;
        }

        #endregion Attachments

		#endregion Inverse
    }

	#endregion Mail


	#region MailAttachment

	[Table("MailAttachment", Schema = "mail")]
	public partial class MailAttachment : Entity, ITable, IDeleteable, IOperateInfo, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string MailId = "MailId";

            public const string FileIndexId = "FileIndexId";

            public const string FileSize = "FileSize";

            public const string ContentType = "ContentType";

            public const string FileName = "FileName";

            public const string FileExt = "FileExt";

			/// <summary>
			/// 逻辑删除标记
			/// </summary>
            public const string Deleted = "Deleted";

			/// <summary>
			/// 创建人
			/// </summary>
            public const string Creator = "Creator";

			/// <summary>
			/// 创建时间
			/// </summary>
            public const string CreateTime = "CreateTime";

			/// <summary>
			/// 最后修改人
			/// </summary>
            public const string LastEditor = "LastEditor";

			/// <summary>
			/// 最后修改时间
			/// </summary>
            public const string LastEditTime = "LastEditTime";

        }

        public static partial class Foreigns
        {

            public const string Mail = "Mail";

        }

        #endregion Const

        #region Construct

        public MailAttachment()
        {

        }

        public MailAttachment(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region MailId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid _MailId;

        [Column(Columns.MailId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual Guid MailId
        {
            get => _MailId;
            set
            {
                _MailId = value;
                this.SetChangedColumn(_MailId);
            }
        }

        #endregion MailId

        #region FileIndexId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid _FileIndexId;

        [Column(Columns.FileIndexId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual Guid FileIndexId
        {
            get => _FileIndexId;
            set
            {
                _FileIndexId = value;
                this.SetChangedColumn(_FileIndexId);
            }
        }

        #endregion FileIndexId

        #region FileSize

        [NotMapped, XmlIgnore, JsonIgnore]
        private long _FileSize;

        [Column(Columns.FileSize, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual long FileSize
        {
            get => _FileSize;
            set
            {
                _FileSize = value;
                this.SetChangedColumn(_FileSize);
            }
        }

        #endregion FileSize

        #region ContentType

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _ContentType = string.Empty;

        [Column(Columns.ContentType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 200)]
		public virtual string ContentType
        {
            get => _ContentType;
            set
            {
                _ContentType = value ?? string.Empty;
                this.SetChangedColumn(_ContentType);
            }
        }

        #endregion ContentType

        #region FileName

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _FileName = string.Empty;

        [Column(Columns.FileName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string FileName
        {
            get => _FileName;
            set
            {
                _FileName = value ?? string.Empty;
                this.SetChangedColumn(_FileName);
            }
        }

        #endregion FileName

        #region FileExt

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _FileExt = string.Empty;

        [Column(Columns.FileExt, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string FileExt
        {
            get => _FileExt;
            set
            {
                _FileExt = value ?? string.Empty;
                this.SetChangedColumn(_FileExt);
            }
        }

        #endregion FileExt

        #region Deleted 逻辑删除标记

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _Deleted;

		/// <summary>
        /// 逻辑删除标记
        /// </summary>
        [Description("逻辑删除标记")]
        [Column(Columns.Deleted, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool Deleted
        {
            get => _Deleted;
            set
            {
                _Deleted = value;
                this.SetChangedColumn(_Deleted);
            }
        }

        #endregion Deleted

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Creator;

		/// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column(Columns.Creator, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Creator
        {
            get => _Creator;
            set
            {
                _Creator = value;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _CreateTime;

		/// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column(Columns.CreateTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime

        #region LastEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _LastEditor;

		/// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column(Columns.LastEditor, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? LastEditor
        {
            get => _LastEditor;
            set
            {
                _LastEditor = value;
                this.SetChangedColumn(_LastEditor);
            }
        }

        #endregion LastEditor

        #region LastEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _LastEditTime;

		/// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column(Columns.LastEditTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? LastEditTime
        {
            get => _LastEditTime;
            set
            {
                _LastEditTime = value;
                this.SetChangedColumn(_LastEditTime);
            }
        }

        #endregion LastEditTime

        #endregion Properties

        #region Foreign

		#region Mail

        [NotMapped, XmlIgnore, JsonIgnore]
        private Mail? _Mail;

        [ForeignKey(Columns.MailId)]
        public virtual Mail Mail
        {

            get => this.LazyLoad(ref _Mail);
            set => _Mail = value;
        }


        #endregion Mail

        #endregion Foreign
    }

	#endregion MailAttachment


	#region MailServer

    /// <summary>
    /// 邮件服务器
    /// </summary>
    [Description("邮件服务器")]
	[Table("MailServer", Schema = "mail")]
	public partial class MailServer : Entity, ITable, IDeleteable, IOperateInfo, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

			/// <summary>
			/// 邮件服务器类型
			/// </summary>
            public const string EnumType = "EnumType";

			/// <summary>
			/// 邮件服务器编码
			/// </summary>
            public const string Code = "Code";

			/// <summary>
			/// 邮件发件人地址
			/// </summary>
            public const string From = "From";

			/// <summary>
			/// Debug收件人地址
			/// </summary>
            public const string DebugTo = "DebugTo";

            public const string SendInterval = "SendInterval";

            public const string RetryInterval = "RetryInterval";

            public const string MaxRetry = "MaxRetry";

            public const string MaxResend = "MaxResend";

            public const string Valid = "Valid";

            public const string Description = "Description";

			/// <summary>
			/// 逻辑删除标记
			/// </summary>
            public const string Deleted = "Deleted";

			/// <summary>
			/// 创建人
			/// </summary>
            public const string Creator = "Creator";

			/// <summary>
			/// 创建时间
			/// </summary>
            public const string CreateTime = "CreateTime";

			/// <summary>
			/// 最后修改人
			/// </summary>
            public const string LastEditor = "LastEditor";

			/// <summary>
			/// 最后修改时间
			/// </summary>
            public const string LastEditTime = "LastEditTime";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 邮件
			/// </summary>
            public const string Mail = "Mail";

			/// <summary>
			/// 'SMTP服务器
			/// </summary>
            public const string SmtpServer = "SmtpServer";

        }

        #endregion Const

        #region Construct

        public MailServer()
        {

        }

        public MailServer(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region EnumType 邮件服务器类型

        [NotMapped, XmlIgnore, JsonIgnore]
        private MailServerType _EnumType;

		/// <summary>
        /// 邮件服务器类型
        /// </summary>
        [Description("邮件服务器类型")]
        [Column(Columns.EnumType, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual MailServerType EnumType
        {
            get => _EnumType;
            set
            {
                _EnumType = value;
                this.SetChangedColumn(_EnumType);
            }
        }

		/// <summary>
        /// 邮件服务器类型
        /// </summary>
        [Description("邮件服务器类型")]
		[NotMapped]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        #endregion EnumType

        #region Code 邮件服务器编码

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Code = string.Empty;

		/// <summary>
        /// 邮件服务器编码
        /// </summary>
        [Description("邮件服务器编码")]
        [Column(Columns.Code, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string Code
        {
            get => _Code;
            set
            {
                _Code = value ?? string.Empty;
                this.SetChangedColumn(_Code);
            }
        }

        #endregion Code

        #region From 邮件发件人地址

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _From = string.Empty;

		/// <summary>
        /// 邮件发件人地址
        /// </summary>
        [Description("邮件发件人地址")]
        [Column(Columns.From, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string From
        {
            get => _From;
            set
            {
                _From = value ?? string.Empty;
                this.SetChangedColumn(_From);
            }
        }

        #endregion From

        #region DebugTo Debug收件人地址

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _DebugTo = string.Empty;

		/// <summary>
        /// Debug收件人地址
        /// </summary>
        [Description("Debug收件人地址")]
        [Column(Columns.DebugTo, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string DebugTo
        {
            get => _DebugTo;
            set
            {
                _DebugTo = value ?? string.Empty;
                this.SetChangedColumn(_DebugTo);
            }
        }

        #endregion DebugTo

        #region SendInterval

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _SendInterval;

        [Column(Columns.SendInterval, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int SendInterval
        {
            get => _SendInterval;
            set
            {
                _SendInterval = value;
                this.SetChangedColumn(_SendInterval);
            }
        }

        #endregion SendInterval

        #region RetryInterval

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _RetryInterval;

        [Column(Columns.RetryInterval, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int RetryInterval
        {
            get => _RetryInterval;
            set
            {
                _RetryInterval = value;
                this.SetChangedColumn(_RetryInterval);
            }
        }

        #endregion RetryInterval

        #region MaxRetry

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _MaxRetry;

        [Column(Columns.MaxRetry, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int MaxRetry
        {
            get => _MaxRetry;
            set
            {
                _MaxRetry = value;
                this.SetChangedColumn(_MaxRetry);
            }
        }

        #endregion MaxRetry

        #region MaxResend

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _MaxResend;

        [Column(Columns.MaxResend, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int MaxResend
        {
            get => _MaxResend;
            set
            {
                _MaxResend = value;
                this.SetChangedColumn(_MaxResend);
            }
        }

        #endregion MaxResend

        #region Valid

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _Valid;

        [Column(Columns.Valid, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool Valid
        {
            get => _Valid;
            set
            {
                _Valid = value;
                this.SetChangedColumn(_Valid);
            }
        }

        #endregion Valid

        #region Description

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Description;

        [Column(Columns.Description, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? Description
        {
            get => _Description;
            set
            {
                _Description = value;
                this.SetChangedColumn(_Description);
            }
        }

        #endregion Description

        #region Deleted 逻辑删除标记

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _Deleted;

		/// <summary>
        /// 逻辑删除标记
        /// </summary>
        [Description("逻辑删除标记")]
        [Column(Columns.Deleted, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool Deleted
        {
            get => _Deleted;
            set
            {
                _Deleted = value;
                this.SetChangedColumn(_Deleted);
            }
        }

        #endregion Deleted

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Creator;

		/// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column(Columns.Creator, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Creator
        {
            get => _Creator;
            set
            {
                _Creator = value;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _CreateTime;

		/// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column(Columns.CreateTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime

        #region LastEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _LastEditor;

		/// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column(Columns.LastEditor, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? LastEditor
        {
            get => _LastEditor;
            set
            {
                _LastEditor = value;
                this.SetChangedColumn(_LastEditor);
            }
        }

        #endregion LastEditor

        #region LastEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _LastEditTime;

		/// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column(Columns.LastEditTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? LastEditTime
        {
            get => _LastEditTime;
            set
            {
                _LastEditTime = value;
                this.SetChangedColumn(_LastEditTime);
            }
        }

        #endregion LastEditTime

        #endregion Properties

		#region Inverse

        #region Mail 邮件

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<Mail>? _Mail;

		/// <summary>
        /// 邮件
        /// </summary>
		[InverseProperty("MailServer")]
        public virtual List<Mail> Mail
        {
            get => this.LazyLoad(ref _Mail);
            set => _Mail = value;
        }

        #endregion Mail

        #region SmtpServer 'SMTP服务器

        [NotMapped, XmlIgnore, JsonIgnore]
        private SmtpServer? _SmtpServer;

		/// <summary>
        /// 'SMTP服务器
        /// </summary>
		[InverseProperty("MailServer")]
        public virtual SmtpServer? SmtpServer
        {
            get => this.LazyLoad(ref _SmtpServer);
            set => _SmtpServer = value;
        }

        #endregion SmtpServer

		#endregion Inverse
    }

	#endregion MailServer


	#region MailTemplate

    /// <summary>
    /// 邮件模板
    /// </summary>
    [Description("邮件模板")]
	[Table("MailTemplate", Schema = "mail")]
	public partial class MailTemplate : Entity, ITable, IDeleteable, IOperateInfo, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string Code = "Code";

            public const string IsBodyHtml = "IsBodyHtml";

            public const string Subject = "Subject";

            public const string Content = "Content";

			/// <summary>
			/// 逻辑删除标记
			/// </summary>
            public const string Deleted = "Deleted";

			/// <summary>
			/// 创建人
			/// </summary>
            public const string Creator = "Creator";

			/// <summary>
			/// 创建时间
			/// </summary>
            public const string CreateTime = "CreateTime";

			/// <summary>
			/// 最后修改人
			/// </summary>
            public const string LastEditor = "LastEditor";

			/// <summary>
			/// 最后修改时间
			/// </summary>
            public const string LastEditTime = "LastEditTime";

        }

        #endregion Const

        #region Construct

        public MailTemplate()
        {

        }

        public MailTemplate(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region Code

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Code;

        [Column(Columns.Code, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Code
        {
            get => _Code;
            set
            {
                _Code = value;
                this.SetChangedColumn(_Code);
            }
        }

        #endregion Code

        #region IsBodyHtml

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _IsBodyHtml;

        [Column(Columns.IsBodyHtml, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool IsBodyHtml
        {
            get => _IsBodyHtml;
            set
            {
                _IsBodyHtml = value;
                this.SetChangedColumn(_IsBodyHtml);
            }
        }

        #endregion IsBodyHtml

        #region Subject

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Subject = string.Empty;

        [Column(Columns.Subject, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 1000)]
		public virtual string Subject
        {
            get => _Subject;
            set
            {
                _Subject = value ?? string.Empty;
                this.SetChangedColumn(_Subject);
            }
        }

        #endregion Subject

        #region Content

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Content = string.Empty;

        [Column(Columns.Content, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual string Content
        {
            get => _Content;
            set
            {
                _Content = value ?? string.Empty;
                this.SetChangedColumn(_Content);
            }
        }

        #endregion Content

        #region Deleted 逻辑删除标记

        [NotMapped, XmlIgnore, JsonIgnore]
        private bool _Deleted;

		/// <summary>
        /// 逻辑删除标记
        /// </summary>
        [Description("逻辑删除标记")]
        [Column(Columns.Deleted, DbType = DbType.Boolean, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual bool Deleted
        {
            get => _Deleted;
            set
            {
                _Deleted = value;
                this.SetChangedColumn(_Deleted);
            }
        }

        #endregion Deleted

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Creator;

		/// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column(Columns.Creator, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Creator
        {
            get => _Creator;
            set
            {
                _Creator = value;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _CreateTime;

		/// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column(Columns.CreateTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime

        #region LastEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _LastEditor;

		/// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column(Columns.LastEditor, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? LastEditor
        {
            get => _LastEditor;
            set
            {
                _LastEditor = value;
                this.SetChangedColumn(_LastEditor);
            }
        }

        #endregion LastEditor

        #region LastEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _LastEditTime;

		/// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column(Columns.LastEditTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? LastEditTime
        {
            get => _LastEditTime;
            set
            {
                _LastEditTime = value;
                this.SetChangedColumn(_LastEditTime);
            }
        }

        #endregion LastEditTime

        #endregion Properties
    }

	#endregion MailTemplate


	#region SmtpServer

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
    [Description("'SMTP服务器")]
	[Table("SmtpServer", Schema = "mail")]
	public partial class SmtpServer : Entity, ITable, IPrimaryKey<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

			/// <summary>
			/// 地址
			/// </summary>
            public const string Host = "Host";

			/// <summary>
			/// 端口
			/// </summary>
            public const string Port = "Port";

			/// <summary>
			/// 加密方式
			/// </summary>
            public const string EnumSmtpSecure = "EnumSmtpSecure";

			/// <summary>
			/// 用户名
			/// </summary>
            public const string Username = "Username";

			/// <summary>
			/// 密码
			/// </summary>
            public const string Password = "Password";

            public const string PickupDirectoryLocation = "PickupDirectoryLocation";

        }

        public static partial class Foreigns
        {

            public const string MailServer = "MailServer";

        }

        #endregion Const

        #region Construct

        public SmtpServer()
        {

        }

        public SmtpServer(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region Host 地址

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _Host = string.Empty;

		/// <summary>
        /// 地址
        /// </summary>
        [Description("地址")]
        [Column(Columns.Host, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string Host
        {
            get => _Host;
            set
            {
                _Host = value ?? string.Empty;
                this.SetChangedColumn(_Host);
            }
        }

        #endregion Host

        #region Port 端口

        [NotMapped, XmlIgnore, JsonIgnore]
        private int _Port;

		/// <summary>
        /// 端口
        /// </summary>
        [Description("端口")]
        [Column(Columns.Port, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual int Port
        {
            get => _Port;
            set
            {
                _Port = value;
                this.SetChangedColumn(_Port);
            }
        }

        #endregion Port

        #region EnumSmtpSecure 加密方式

        [NotMapped, XmlIgnore, JsonIgnore]
        private SmtpSecure _EnumSmtpSecure;

		/// <summary>
        /// 加密方式
        /// </summary>
        [Description("加密方式")]
        [Column(Columns.EnumSmtpSecure, DbType = DbType.Int32, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual SmtpSecure EnumSmtpSecure
        {
            get => _EnumSmtpSecure;
            set
            {
                _EnumSmtpSecure = value;
                this.SetChangedColumn(_EnumSmtpSecure);
            }
        }

		/// <summary>
        /// 加密方式
        /// </summary>
        [Description("加密方式")]
		[NotMapped]
		public virtual string EnumSmtpSecureDesc => this.EnumSmtpSecure.GetDesc();

        #endregion EnumSmtpSecure

        #region Username 用户名

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Username;

		/// <summary>
        /// 用户名
        /// </summary>
        [Description("用户名")]
        [Column(Columns.Username, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Username
        {
            get => _Username;
            set
            {
                _Username = value;
                this.SetChangedColumn(_Username);
            }
        }

        #endregion Username

        #region Password 密码

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Password;

		/// <summary>
        /// 密码
        /// </summary>
        [Description("密码")]
        [Column(Columns.Password, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Password
        {
            get => _Password;
            set
            {
                _Password = value;
                this.SetChangedColumn(_Password);
            }
        }

        #endregion Password

        #region PickupDirectoryLocation

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _PickupDirectoryLocation;

        [Column(Columns.PickupDirectoryLocation, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 500)]
		public virtual string? PickupDirectoryLocation
        {
            get => _PickupDirectoryLocation;
            set
            {
                _PickupDirectoryLocation = value;
                this.SetChangedColumn(_PickupDirectoryLocation);
            }
        }

        #endregion PickupDirectoryLocation

        #endregion Properties

        #region Foreign

		#region MailServer

        [NotMapped, XmlIgnore, JsonIgnore]
        private MailServer? _MailServer;

        [ForeignKey(Columns.ID)]
        public virtual MailServer MailServer
        {

            get => this.LazyLoad(ref _MailServer);
            set => _MailServer = value;
        }


        #endregion MailServer

        #endregion Foreign
    }

	#endregion SmtpServer

}

#pragma warning restore CS8669
