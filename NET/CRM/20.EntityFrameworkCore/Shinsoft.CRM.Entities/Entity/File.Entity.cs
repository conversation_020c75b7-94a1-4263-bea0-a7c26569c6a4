﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shinsoft.Core;
using Shinsoft.Core.EntityFrameworkCore;
using Column = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;

#pragma warning disable CS8669

namespace Shinsoft.CRM.Entities
{

	#region FileContent

    /// <summary>
    /// 文件内容
    /// </summary>
    [Description("文件内容")]
	[Table("FileContent", Schema = "file")]
	public partial class FileContent : Entity, ITable, IPrimaryKey<Guid>, ICompanyEntity<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// 文件内容
			/// </summary>
            public const string Content = "Content";

        }

        public static partial class Foreigns
        {

            public const string FileIndex = "FileIndex";

        }

        #endregion Const

        #region Construct

        public FileContent()
        {

        }

        public FileContent(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual Guid CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region Content 文件内容

        [NotMapped, XmlIgnore, JsonIgnore]
        private byte[] _Content;

		/// <summary>
        /// 文件内容
        /// </summary>
        [Description("文件内容")]
        [Column(Columns.Content, DbType = DbType.Binary, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual byte[] Content
        {
            get => _Content;
            set
            {
                _Content = value;
                this.SetChangedColumn(_Content);
            }
        }

        #endregion Content

        #endregion Properties

        #region Foreign

		#region FileIndex

        [NotMapped, XmlIgnore, JsonIgnore]
        private FileIndex? _FileIndex;

        [ForeignKey(Columns.ID)]
        public virtual FileIndex FileIndex
        {

            get => this.LazyLoad(ref _FileIndex);
            set => _FileIndex = value;
        }


        #endregion FileIndex

        #endregion Foreign
    }

	#endregion FileContent


	#region FileIndex

    /// <summary>
    /// 文件检索
    /// </summary>
    [Description("文件检索")]
	[Table("FileIndex", Schema = "file")]
	public partial class FileIndex : Entity, ITable, IOperateInfo, IPrimaryKey<Guid>, ICompanyEntity<Guid>
    {

        #region Const

        public static partial class Columns
        {

            public const string ID = "ID";

            public const string CompanyId = "CompanyId";

			/// <summary>
			/// 文件大小
			/// </summary>
            public const string FileSize = "FileSize";

            public const string MD5 = "MD5";

            public const string SHA1 = "SHA1";

			/// <summary>
			/// 默认文件类型
			/// </summary>
            public const string ContentType = "ContentType";

			/// <summary>
			/// 默认文件名
			/// </summary>
            public const string FileName = "FileName";

			/// <summary>
			/// 默认文件扩展名
			/// </summary>
            public const string FileExt = "FileExt";

			/// <summary>
			/// 基本文件夹
			/// </summary>
            public const string BaseFolder = "BaseFolder";

			/// <summary>
			/// 相对子路径
			/// </summary>
            public const string SubPath = "SubPath";

			/// <summary>
			/// 创建人
			/// </summary>
            public const string Creator = "Creator";

			/// <summary>
			/// 创建时间
			/// </summary>
            public const string CreateTime = "CreateTime";

			/// <summary>
			/// 最后修改人
			/// </summary>
            public const string LastEditor = "LastEditor";

			/// <summary>
			/// 最后修改时间
			/// </summary>
            public const string LastEditTime = "LastEditTime";

        }

        public static partial class Inverses
        {

			/// <summary>
			/// 文件内容
			/// </summary>
            public const string FileContent = "FileContent";

        }

        #endregion Const

        #region Construct

        public FileIndex()
        {

        }

        public FileIndex(ILazyLoader lazyLoader)
            : base(lazyLoader)
        {

        }

        #endregion Construct

        #region Properties

        #region ID

        [Key]
        [Column(Columns.ID, DbType = DbType.Guid, IsPrimaryKey = true, IsForeignKey = true, IsNullable = false)]
        public virtual Guid ID { get; set; }

        #endregion ID

        #region CompanyId

        [NotMapped, XmlIgnore, JsonIgnore]
        private Guid _CompanyId;

        [Column(Columns.CompanyId, DbType = DbType.Guid, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual Guid CompanyId
        {
            get => _CompanyId;
            set
            {
                _CompanyId = value;
                this.SetChangedColumn(_CompanyId);
            }
        }

        #endregion CompanyId

        #region FileSize 文件大小

        [NotMapped, XmlIgnore, JsonIgnore]
        private long _FileSize;

		/// <summary>
        /// 文件大小
        /// </summary>
        [Description("文件大小")]
        [Column(Columns.FileSize, DbType = DbType.Int64, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false)]
		public virtual long FileSize
        {
            get => _FileSize;
            set
            {
                _FileSize = value;
                this.SetChangedColumn(_FileSize);
            }
        }

        #endregion FileSize

        #region MD5

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _MD5 = string.Empty;

        [Column(Columns.MD5, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string MD5
        {
            get => _MD5;
            set
            {
                _MD5 = value ?? string.Empty;
                this.SetChangedColumn(_MD5);
            }
        }

        #endregion MD5

        #region SHA1

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _SHA1 = string.Empty;

        [Column(Columns.SHA1, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string SHA1
        {
            get => _SHA1;
            set
            {
                _SHA1 = value ?? string.Empty;
                this.SetChangedColumn(_SHA1);
            }
        }

        #endregion SHA1

        #region ContentType 默认文件类型

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _ContentType = string.Empty;

		/// <summary>
        /// 默认文件类型
        /// </summary>
        [Description("默认文件类型")]
        [Column(Columns.ContentType, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 200)]
		public virtual string ContentType
        {
            get => _ContentType;
            set
            {
                _ContentType = value ?? string.Empty;
                this.SetChangedColumn(_ContentType);
            }
        }

        #endregion ContentType

        #region FileName 默认文件名

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _FileName = string.Empty;

		/// <summary>
        /// 默认文件名
        /// </summary>
        [Description("默认文件名")]
        [Column(Columns.FileName, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string FileName
        {
            get => _FileName;
            set
            {
                _FileName = value ?? string.Empty;
                this.SetChangedColumn(_FileName);
            }
        }

        #endregion FileName

        #region FileExt 默认文件扩展名

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _FileExt = string.Empty;

		/// <summary>
        /// 默认文件扩展名
        /// </summary>
        [Description("默认文件扩展名")]
        [Column(Columns.FileExt, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 50)]
		public virtual string FileExt
        {
            get => _FileExt;
            set
            {
                _FileExt = value ?? string.Empty;
                this.SetChangedColumn(_FileExt);
            }
        }

        #endregion FileExt

        #region BaseFolder 基本文件夹

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _BaseFolder = string.Empty;

		/// <summary>
        /// 基本文件夹
        /// </summary>
        [Description("基本文件夹")]
        [Column(Columns.BaseFolder, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string BaseFolder
        {
            get => _BaseFolder;
            set
            {
                _BaseFolder = value ?? string.Empty;
                this.SetChangedColumn(_BaseFolder);
            }
        }

        #endregion BaseFolder

        #region SubPath 相对子路径

        [NotMapped, XmlIgnore, JsonIgnore]
        private string _SubPath = string.Empty;

		/// <summary>
        /// 相对子路径
        /// </summary>
        [Description("相对子路径")]
        [Column(Columns.SubPath, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = false, MaxLength = 500)]
		public virtual string SubPath
        {
            get => _SubPath;
            set
            {
                _SubPath = value ?? string.Empty;
                this.SetChangedColumn(_SubPath);
            }
        }

        #endregion SubPath

        #region Creator 创建人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _Creator;

		/// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [Column(Columns.Creator, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? Creator
        {
            get => _Creator;
            set
            {
                _Creator = value;
                this.SetChangedColumn(_Creator);
            }
        }

        #endregion Creator

        #region CreateTime 创建时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _CreateTime;

		/// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [Column(Columns.CreateTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? CreateTime
        {
            get => _CreateTime;
            set
            {
                _CreateTime = value;
                this.SetChangedColumn(_CreateTime);
            }
        }

        #endregion CreateTime

        #region LastEditor 最后修改人

        [NotMapped, XmlIgnore, JsonIgnore]
        private string? _LastEditor;

		/// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [Column(Columns.LastEditor, DbType = DbType.String, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true, MaxLength = 50)]
		public virtual string? LastEditor
        {
            get => _LastEditor;
            set
            {
                _LastEditor = value;
                this.SetChangedColumn(_LastEditor);
            }
        }

        #endregion LastEditor

        #region LastEditTime 最后修改时间

        [NotMapped, XmlIgnore, JsonIgnore]
        private DateTime? _LastEditTime;

		/// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [Column(Columns.LastEditTime, DbType = DbType.DateTime, IsPrimaryKey = false, IsForeignKey = false, IsNullable = true)]
		public virtual DateTime? LastEditTime
        {
            get => _LastEditTime;
            set
            {
                _LastEditTime = value;
                this.SetChangedColumn(_LastEditTime);
            }
        }

        #endregion LastEditTime

        #endregion Properties

		#region Inverse

        #region FileContent 文件内容

        [NotMapped, XmlIgnore, JsonIgnore]
        private FileContent? _FileContent;

		/// <summary>
        /// 文件内容
        /// </summary>
		[InverseProperty("FileIndex")]
        public virtual FileContent? FileContent
        {
            get => this.LazyLoad(ref _FileContent);
            set => _FileContent = value;
        }

        #endregion FileContent

		#endregion Inverse
    }

	#endregion FileIndex

}

#pragma warning restore CS8669
