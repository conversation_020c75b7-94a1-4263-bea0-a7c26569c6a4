using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.Core.NLog;
using System.ComponentModel.DataAnnotations;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace Shinsoft.CRM.Api.Controllers
{
    /// <summary>
    /// 语音识别
    /// </summary>
    [ApiExplorerSettings(GroupName = "语音识别")]
    public class SpeechController : BaseApiController<SysBll>
    {
        /// <summary>
        /// 上传录音文件并转换为文字
        /// </summary>
        /// <param name="audioFile">音频文件</param>
        /// <returns>识别结果</returns>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "录音文件转文字")]
        public async Task<BizResult<SpeechRecognitionResult>> UploadAudioFile(IFormFile audioFile)
        {
            var result = new BizResult<SpeechRecognitionResult>();

            try
            {
                if (audioFile == null || audioFile.Length == 0)
                {
                    result.Error("请选择音频文件");
                    return result;
                }

                // 验证文件格式
                var allowedExtensions = new[] { ".wav", ".mp3", ".m4a", ".aac" };
                var fileExtension = Path.GetExtension(audioFile.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    result.Error("不支持的音频格式，请上传 WAV、MP3、M4A 或 AAC 格式的文件");
                    return result;
                }

                // 验证文件大小（限制为10MB）
                if (audioFile.Length > 10 * 1024 * 1024)
                {
                    result.Error("文件大小不能超过10MB");
                    return result;
                }

                // 保存临时文件
                var tempFilePath = Path.GetTempFileName();
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await audioFile.CopyToAsync(stream);
                }

                // 调用语音识别服务
                var recognitionResult = await RecognizeAudioFile(tempFilePath);

                // 删除临时文件
                if (System.IO.File.Exists(tempFilePath))
                {
                    System.IO.File.Delete(tempFilePath);
                }

                result.Data = recognitionResult;
            }
            catch (Exception ex)
            {
                result.Error($"语音识别失败：{ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// WebSocket实时语音识别
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "建立WebSocket连接")]
        public async Task<IActionResult> ConnectWebSocket()
        {
            if (HttpContext.WebSockets.IsWebSocketRequest)
            {
                var webSocket = await HttpContext.WebSockets.AcceptWebSocketAsync();
                await HandleWebSocketConnection(webSocket);
                return new EmptyResult();
            }
            else
            {
                return BadRequest("WebSocket连接请求无效");
            }
        }

        /// <summary>
        /// 获取语音识别历史记录
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>历史记录列表</returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取语音识别历史")]
        public BizResult<PagedResult<SpeechHistoryItem>> GetSpeechHistory(
            [FromQuery] int pageIndex = 1, 
            [FromQuery] int pageSize = 20)
        {
            var result = new BizResult<PagedResult<SpeechHistoryItem>>();

            try
            {
                // 这里应该从数据库获取历史记录
                // 暂时返回模拟数据
                var historyItems = new List<SpeechHistoryItem>
                {
                    new SpeechHistoryItem
                    {
                        Id = Guid.NewGuid(),
                        RecognizedText = "这是一条语音识别的测试记录",
                        CreateTime = DateTime.Now.AddMinutes(-10),
                        Duration = 5.2,
                        UserId = this.CurrentUserId
                    }
                };

                var pagedResult = new PagedResult<SpeechHistoryItem>
                {
                    Items = historyItems,
                    TotalCount = historyItems.Count,
                    PageIndex = pageIndex,
                    PageSize = pageSize
                };

                result.Data = pagedResult;
            }
            catch (Exception ex)
            {
                result.Error($"获取历史记录失败：{ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 处理WebSocket连接
        /// </summary>
        /// <param name="webSocket">WebSocket连接</param>
        /// <returns></returns>
        private async Task HandleWebSocketConnection(WebSocket webSocket)
        {
            var buffer = new byte[1024 * 4];
            var audioBuffer = new List<byte>();

            try
            {
                while (webSocket.State == WebSocketState.Open)
                {
                    var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

                    if (result.MessageType == WebSocketMessageType.Binary)
                    {
                        // 接收音频数据
                        audioBuffer.AddRange(buffer.Take(result.Count));

                        if (result.EndOfMessage)
                        {
                            // 处理完整的音频数据
                            var recognitionResult = await ProcessAudioData(audioBuffer.ToArray());
                            
                            // 发送识别结果
                            var response = JsonSerializer.Serialize(recognitionResult);
                            var responseBytes = Encoding.UTF8.GetBytes(response);
                            await webSocket.SendAsync(
                                new ArraySegment<byte>(responseBytes),
                                WebSocketMessageType.Text,
                                true,
                                CancellationToken.None);

                            audioBuffer.Clear();
                        }
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "", CancellationToken.None);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"WebSocket连接错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 识别音频文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>识别结果</returns>
        private async Task<SpeechRecognitionResult> RecognizeAudioFile(string filePath)
        {
            // 这里应该调用实际的Paraformer模型API
            // 暂时返回模拟结果
            await Task.Delay(1000); // 模拟处理时间

            return new SpeechRecognitionResult
            {
                Text = "这是语音识别的结果文本",
                Confidence = 0.95,
                Duration = 5.2,
                Language = "zh-CN"
            };
        }

        /// <summary>
        /// 处理音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>识别结果</returns>
        private async Task<SpeechRecognitionResult> ProcessAudioData(byte[] audioData)
        {
            // 这里应该调用实际的Paraformer模型API进行实时识别
            // 暂时返回模拟结果
            await Task.Delay(100); // 模拟处理时间

            return new SpeechRecognitionResult
            {
                Text = "实时语音识别结果",
                Confidence = 0.92,
                Duration = 1.0,
                Language = "zh-CN"
            };
        }
    }

    /// <summary>
    /// 语音识别结果
    /// </summary>
    public class SpeechRecognitionResult
    {
        /// <summary>
        /// 识别的文本
        /// </summary>
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 音频时长（秒）
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public string Language { get; set; } = "zh-CN";
    }

    /// <summary>
    /// 语音识别历史记录项
    /// </summary>
    public class SpeechHistoryItem
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 识别的文本
        /// </summary>
        public string RecognizedText { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 音频时长
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }
    }

    /// <summary>
    /// 分页结果
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据项
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }
}
