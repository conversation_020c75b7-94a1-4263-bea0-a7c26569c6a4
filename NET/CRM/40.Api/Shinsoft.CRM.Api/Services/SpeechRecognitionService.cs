using Shinsoft.CRM.Api.Models;
using System.Text;
using System.Text.Json;

namespace Shinsoft.CRM.Api.Services
{
    /// <summary>
    /// 语音识别服务
    /// </summary>
    public class SpeechRecognitionService
    {
        private readonly HttpClient _httpClient;
        private readonly AliBaiLianConfig _config;
        private readonly ILogger<SpeechRecognitionService> _logger;

        public SpeechRecognitionService(HttpClient httpClient, AliBaiLianConfig config, ILogger<SpeechRecognitionService> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;
        }

        /// <summary>
        /// 识别音频文件
        /// </summary>
        /// <param name="audioFilePath">音频文件路径</param>
        /// <returns>识别结果</returns>
        public async Task<SpeechRecognitionResult> RecognizeAudioFileAsync(string audioFilePath)
        {
            try
            {
                // 读取音频文件
                var audioBytes = await File.ReadAllBytesAsync(audioFilePath);
                return await RecognizeAudioDataAsync(audioBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "语音文件识别失败: {FilePath}", audioFilePath);
                throw new Exception($"语音识别失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 识别音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>识别结果</returns>
        public async Task<SpeechRecognitionResult> RecognizeAudioDataAsync(byte[] audioData)
        {
            try
            {
                // 构建请求
                var request = new HttpRequestMessage(HttpMethod.Post, _config.SpeechRecognitionUrl);
                request.Headers.Add("Authorization", $"Bearer {_config.ApiKey}");
                request.Headers.Add("Content-Type", "application/json");

                // 构建JSON请求体
                var requestBody = new
                {
                    model = _config.Model,
                    input = new
                    {
                        audio = Convert.ToBase64String(audioData),
                        format = "wav",
                        sample_rate = 16000,
                        channel = 1
                    },
                    parameters = new
                    {
                        incremental_output = false,
                        enable_words = true
                    }
                };

                var jsonContent = JsonSerializer.Serialize(requestBody);
                request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 发送请求
                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("阿里百炼API响应: {Response}", responseContent);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("阿里百炼API调用失败: {StatusCode}, {Content}", response.StatusCode, responseContent);
                    throw new Exception($"语音识别API调用失败: {response.StatusCode} - {responseContent}");
                }

                // 解析响应
                var apiResponse = JsonSerializer.Deserialize<AliBaiLianResponse>(responseContent);

                if (apiResponse?.Output?.Text != null)
                {
                    return new SpeechRecognitionResult
                    {
                        Text = apiResponse.Output.Text,
                        Confidence = 0.95, // 阿里云API可能不返回置信度，使用默认值
                        Duration = CalculateAudioDuration(audioData),
                        Language = "zh-CN"
                    };
                }
                else
                {
                    _logger.LogWarning("语音识别返回空结果: {Response}", responseContent);
                    return new SpeechRecognitionResult
                    {
                        Text = "无法识别语音内容",
                        Confidence = 0.0,
                        Duration = CalculateAudioDuration(audioData),
                        Language = "zh-CN"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "语音识别处理失败");
                throw new Exception($"语音识别失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算音频时长（简单估算）
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>时长（秒）</returns>
        private static double CalculateAudioDuration(byte[] audioData)
        {
            // 简单估算：假设16kHz采样率，16位，单声道
            // 实际项目中应该解析音频文件头获取准确信息
            const int sampleRate = 16000;
            const int bitsPerSample = 16;
            const int channels = 1;
            
            var bytesPerSecond = sampleRate * (bitsPerSample / 8) * channels;
            return (double)audioData.Length / bytesPerSecond;
        }
    }

    /// <summary>
    /// 阿里百炼API响应
    /// </summary>
    public class AliBaiLianResponse
    {
        public AliBaiLianOutput? Output { get; set; }
        public AliBaiLianUsage? Usage { get; set; }
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// 阿里百炼输出
    /// </summary>
    public class AliBaiLianOutput
    {
        public string? Text { get; set; }
        public bool? Finish { get; set; }
    }

    /// <summary>
    /// 阿里百炼使用统计
    /// </summary>
    public class AliBaiLianUsage
    {
        public int Duration { get; set; }
    }

    /// <summary>
    /// 语音识别结果
    /// </summary>
    public class SpeechRecognitionResult
    {
        /// <summary>
        /// 识别的文本
        /// </summary>
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 音频时长（秒）
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public string Language { get; set; } = "zh-CN";
    }
}
