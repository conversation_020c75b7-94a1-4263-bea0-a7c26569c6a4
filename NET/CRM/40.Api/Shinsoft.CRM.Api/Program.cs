using Shinsoft.CRM.Api;
using Shinsoft.CRM.Api.Providers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using NLog.Config;
using NLog.Targets;
using NLog.Targets.Wrappers;
using NLog.Web;
using Quartz;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.Json.Converters;
using Shinsoft.Core.Mvc.Filters;
using Swashbuckle.AspNetCore.Filters;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Net;

ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
ServicePointManager.ServerCertificateValidationCallback =
    (sender, certificate, chain, errors) =>
    {
        return true;
    };

var builder = WebApplication.CreateBuilder(args);

// ע��NLog
var logConfig = new XmlLoggingConfiguration("_config/NLog.config");

foreach (var target in logConfig.AllTargets)
{
    var name = target.Name;

    DatabaseTarget? dbTarget = null;

    if (target is DatabaseTarget dbTarget1)
    {
        dbTarget = dbTarget1;
    }
    else if (target is AsyncTargetWrapper wrapper && wrapper.WrappedTarget is DatabaseTarget dbTarget2)
    {
        dbTarget = dbTarget2;
    }

    if (dbTarget != null)
    {
        dbTarget.ConnectionString = Config.GetConnectionString("LogDbContext");
    }
}

builder.Logging.AddNLog(logConfig);
//builder.Logging.AddNLog("_config/NLog.config");

// ע�� Controller �� JSON
builder.Services.AddControllers(options =>
{
    options.Filters.Add<BadRequestObjectFilter>();
    options.Filters.Add<ApiExceptionFilter>();
    options.Filters.Add<InputTrimFilter>();
    options.Filters.Add<ApiOperateFilter>();
    options.Filters.Add<AuthorizeFilter>();
})
.AddJsonOptions(options =>
{
    options.JsonSerializerOptions.AllowTrailingCommas = true;
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    //options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
    options.JsonSerializerOptions.WriteIndented = false;

    // �����л������ִ�Сд
    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    // �����ڷ����л���ʱ��ԭ��ӦΪ���ֵ��ַ����������ŵ����֣�תΪ����
    options.JsonSerializerOptions.NumberHandling = JsonNumberHandling.AllowReadingFromString;

    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;

    options.JsonSerializerOptions.Converters.Add(new NullableConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new EnumConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new TimeSpanJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new GuidJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new BooleanJsonConverter());

    Config.SetJsonSerializerOptions(options.JsonSerializerOptions);
});

// �Զ��� MvcHttpContext
builder.Services.AddMvcHttpContext();

// ע��ϵͳʱ�����
// �����������õ�ǰʱ��
// ������ʹ��SysDateTime.Now��ʹ�����õ�ʱ��Ϊ��ǰʱ��
// Ĭ�����õ�ǰʱ���24Сʱ����Ч����Чʱ�����AppSetting.json���޸ģ�
builder.Services.AddSingleton<ISysDateTime, SysDateTime>();

// ע�� EFCore��Bll
builder.Services.AddBllServics();

// ע�� AutoMapper
builder.Services.AddAutoMapper<ConfigProfile>();

// ע�� User Provider
builder.Services.AddUserProvider<UserProvider>();

// ע�� JWT
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = Config.Jwt.ValidateIssuer,
            ValidateAudience = Config.Jwt.ValidateAudience,
            //�Ƿ���֤Token��Ч�ڣ�ʹ�õ�ǰʱ����Token��Claims�е�NotBefore��Expires�Ա�
            ValidateLifetime = Config.Jwt.ValidateLifetime,
            ValidateIssuerSigningKey = Config.Jwt.ValidateIssuerSigningKey,
            ValidIssuer = Config.Jwt.Issuer,
            ValidAudience = Config.Jwt.Audience,
            IssuerSigningKey = Config.Jwt.IssuerSigningKey
        };

        if (Config.Jwt.ClockSkew.HasValue)
        {
            //����ƫ��������������Ĭ�������
            options.TokenValidationParameters.ClockSkew = Config.Jwt.ClockSkew.Value;
        }

        options.Events = new JwtBearerEvents
        {
            OnChallenge = context =>
            {
                return Task.CompletedTask;
            },
            OnAuthenticationFailed = context =>
            {
                //ʧЧ��
                if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                {
                    //jwtʧЧ��to do
                    context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                }
                return Task.CompletedTask;
            },
            OnMessageReceived = context =>
            {
                return Task.CompletedTask;
            },
            //��֤�ɹ�
            OnTokenValidated = context =>
            {
                return Task.CompletedTask;
            }
        };
    });

// ���� Quartz ����
builder.Services.UseQuartzAsync();

// ���ÿ�����
builder.Services.AddCors(options =>
    options.AddPolicy("cors", policy =>
    {
        policy = policy.AllowAnyHeader()
            .AllowAnyMethod();

        if (Config.Cors.AllowAnyOrigin)
        {
            //Ĭ��debug״̬Ϊ �������������
            policy.AllowAnyOrigin();
        }
    })
);



List<string> swaggerDocs = new()
{
    "Sys"
};

if (Config.UseSwagger)
{
    var baseType = typeof(ControllerBase);
    var types = Assembly.GetExecutingAssembly().GetTypes();

    foreach (var type in types)
    {
        if (!type.IsAbstract && baseType.IsAssignableFrom(type))
        {
            var attr = type.GetAttribute<ApiExplorerSettingsAttribute>()
                ?? type.GetAttribute<ApiExplorerSettingsAttribute>(true);

            var doc = (attr?.GroupName).IsEmpty()
                ? "Main"
                : (attr?.GroupName).Value();

            if (!swaggerDocs.Contains(doc))
            {
                swaggerDocs.Add(doc);
            }
        }
    }

    swaggerDocs = swaggerDocs.OrderBy(p => p == "Sys" ? 0 : p == "Main" ? 1 : 2).ThenBy(p => p).ToList();

    //ע�� Swagger
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddSwaggerGen(options =>
    {
        // options.SwaggerGeneratorOptions.
        foreach (var doc in swaggerDocs)
        {
            options.SwaggerDoc(doc, new OpenApiInfo { Title = $"{doc} API" });
        }

        options.DocInclusionPredicate((docName, apiDesc) =>
        {
            if (!apiDesc.TryGetMethodInfo(out MethodInfo methodInfo)
                || methodInfo.ReflectedType?.IsAbstract == true)
            {
                return false;
            }
            var names = methodInfo.GetPriorityAttributes<ApiExplorerSettingsAttribute>()
                .Select(attr => attr.GroupName)
                .ToList();

            return names.Contains(docName) || names.Any(p => p.IsEmpty()) || (!names.Any() && docName == "Main");
        });

        options.OperationFilter<SecurityRequirementsOperationFilter>();

        //��api����token����֤��
        options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
        {
            Description = "JWT��Ȩ(���ݽ�������ͷ�н��д���) ֱ�����¿�������Bearer {token}��ע������֮����һ���ո�",
            Name = "Authorization",//jwtĬ�ϵĲ�������
            In = ParameterLocation.Header,//jwtĬ�ϴ��Authorization��Ϣ��λ��(����ͷ��)
            Type = SecuritySchemeType.ApiKey,
        });

        // Ϊ Swagger JSON and UI����xml�ĵ�ע��·��
        var basePath = AppContext.BaseDirectory;

        DirectoryInfo baseFolder = new(basePath);

        foreach (FileInfo file in baseFolder.GetFiles("*.xml"))
        {
            options.IncludeXmlComments(file.FullName);
        }
    });
}

var app = builder.Build();

// ע��ȫ�� Host
HostContext.SetHost(app);

//var schedulerFactory = HostContext.GetRequiredService<ISchedulerFactory>();
//var scheduler = schedulerFactory.GetScheduler().Result;

//var schedulerTask = scheduler.Start();//����������

// �Զ��� MvcHttpContext
app.UseMvcHttpContext();

// �������п���cors����ConfigureServices���������õĿ����������,Ҫд��MapControllers֮ǰ
app.UseCors("cors");

// 启用WebSocket
app.UseWebSockets(new WebSocketOptions
{
    KeepAliveInterval = TimeSpan.FromMinutes(2)
});

if (Config.UseSwagger)
{
    //// Enable middleware to serve generated Swagger as a JSON endpoint.
    app.UseSwagger();

    // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
    app.UseSwaggerUI(c =>
    {
        if (Config.Debug)
        {
            c.DocumentTitle = $"�����԰桿{c.DocumentTitle}";
        }

        if (!Config.Title.IsEmpty())
        {
            c.DocumentTitle = $"{c.DocumentTitle} - {Config.Title}";
        }

        foreach (var doc in swaggerDocs)
        {
            c.SwaggerEndpoint($"{doc}/swagger.json", $"{doc} API");
        }
    });
}

app.UseHttpsRedirection();

//����jwt��֤������д�� app.UseAuthorization();֮ǰ
app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();