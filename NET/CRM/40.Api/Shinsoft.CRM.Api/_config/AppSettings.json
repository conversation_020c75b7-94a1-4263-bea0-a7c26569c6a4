{
  "Title": "Sperogenix CRM",
  "Debug": true, // 调试模式。调试模式时不验证用户密码
  "DebugPwd": "", // 调试模式下，用户密码，为空则不验证
  "UseSwagger": true, // 是否使用 Swagger
  "UserAuthorize": {
    "Sliding": 600, //滑动过期时间（分钟），不设置默认为30分钟
    "AutoLogin": {
      "ExpireDays": 14, //自动登录有效天数，0：永久有效，-1：不允许自动登录
      "DesKey": "Sperogenix_CRM_AutoLogin_B61BCAEA"
    }
  },
  "DefaultCompanyId": "b61bcaea-7b76-4b62-aa06-b0c10e1e1f64",
  "SecretDesKey": "Sperogenix-DES-KEY", // 数据库连接加密秘钥（长度必须大于8位）
  "PdfSavePath": "D:\\Sperogenix\\CRM\\Pdf\\",
  "SitePath": {
    "Base": "https://xl7.corp.shinsoft.net:9204/sf_crm/",
    "Api": "api",
    "Mobile": "",
    "PC": "admin"
  },
  "Jwt": {
    "Issuer": "Shinsoft",
    "Audience": "",
    "SecurityKey": "Shinsoft.CRM.Api_SecurityKey_B61BCAEA-7B76-4B62-AA06-B0C10E1E1F64",
    "ValidateIssuer": false,
    "ValidateAudience": false,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "ClockSkewMins": 5 //过期最大误差时间(分钟)，不设置则默认为5分钟
  },
  "Enums": [
    {
      "Assembly": "Shinsoft.CRM.Entities",
      "Namespace": "Shinsoft.CRM.Entities"
    },
    {
      "Assembly": "Shinsoft.CRM.Common",
      "Namespace": "Shinsoft.CRM.Common"
    },
    {
      "Assembly": "Shinsoft.Core",
      "Namespace": "Shinsoft.Core"
    },
    {
      "Assembly": "Shinsoft.Core",
      "Namespace": "Shinsoft.Core.NLog"
    }
  ],
  "EventExcludeBu": "10_56|MS", //排除Bu，竖线隔开，暂时只有培训使用
  "EmployeeEmailRegex": "^([A-Za-z0-9_\\-\\.])+\\@[Hh][Aa][Nn][Gg][Ss][Ee][Nn][Gg]\\.[Cc][Oo][Mm]$;^([A-Za-z0-9_\\-\\.])+\\@[Hh][Aa][Nn][Gg][Ss][Ee][Nn][Gg]\\.[Cc][Oo][Mm]\\.[Cc][Nn]$",
  //"SysRedis": "**************:6379,password=Password@1,defaultDatabase=1,ssl=false,prefix=Kyuan:",
  "Map": {
    "Provider": "qq",
    "CodeServer": "SWOBZ-XNZCV-LMYPP-5UAIP-CJHSE-BPBW4",
    "BaseUrl": "https://map.shinsoft.net/CRM/Map.html?"
  },
  "File": {
    "ImportFilePath": "D:\\Sperogenix\\CRM\\ImportFile\\"
  },
  "SsoSite": {
    "DesKey": "Sperogenix-DES-KEY",
    "ExpireMinutes": 5,
    //CDMS系统
    "CDMS": {
      "SsoUrl": "http://xl7.corp.shinsoft.net:9204/sf_cdms/",
      "CdmsKey": "Sperogenix",
      "FilePath": "D:\\SperogenixFile\\UploadFiles\\Attachments"
    }
  },
  "WeChat": {
    "Corpid": "ww944997698b10eb0d",
    "CorpSecret": "tttGYdtfLDD2JbsFplLSnM2PDqyvemY2SPMRGtv2m1k",
    "AgentId": "1000002",
    "Authorize": {
      "UrlFormat": "https://open.weixin.qq.com/connect/oauth2/authorize?response_type=code&appid={0}&agentid={1}&scope={2}&redirect_uri={3}&state={4}#wechat_redirect",
      "RedirectUri": "https://xl7.corp.shinsoft.net:9204/sf_crm/#/pages/wechatLogin/wechatLogin",
      "TicketUri": "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token={0}",
      "AgentTicketUri": "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token={0}&type=agent_config"
    },
    "AccessToken": {
      "GetUrlFormat": "https://crm.sperogenix.com:58353/cgi-bin/gettoken?corpid={0}&corpsecret={1}",
      "SkewMins": 5 //过期误差时间(分钟)，不设置则默认为5分钟
    },
    "User": {
      "GetInfoUrlFormat": "https://crm.sperogenix.com:58353/cgi-bin/auth/getuserinfo?access_token={0}&code={1}",
      "GetDetailUrlFormat": "https://crm.sperogenix.com:58353/cgi-bin/auth/getuserdetail?access_token={0}"
    },
    "Msg": {
      "SendUrlFormat": "https://crm.sperogenix.com:58353/cgi-bin/message/send?access_token={0}"
    }
  }, // 系统默认配置（未配置则取默认值） BEGIN
  "GuidOrder": 2,
  "Cors": {
    "AllowAnyOrigin": null //不设置或为空时：debug模式下为true,反之为false；设置时需设置true/false/null
  },
  "Paging": {
    "StartIndex": 1
  },
  "SysDateTime": {
    "EffectHours": 24 //设置SysDateTime修改时间的有效小时数，默认为24小时
  },
  "FileStore": {
    "Type": 1, //文件存储方式,1:文件,2:数据库 (默认：数据库)
    "BaseFolder": "D:\\Sperogenix\\CRM\\", //文件默认存储目录，仅当文件存储方式为文件时有效（默认：空）
    "SubFolder": "{yyyy}\\{MM}" //默认附件存储子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）（默认："{yyyy}\\{MM}"）
  },
  "Mail": {
    "Send": true, //默认:true
    "Debug": true //默认:false
  },
  "Format": {
    "ShowSecond": false
  },
  // 阿里百炼语音识别配置
  "AliBaiLian": {
    "ApiKey": "sk-9f44f8543c584cc0874b064fc1f6784f",
    "BaseUrl": "https://dashscope.aliyuncs.com",
    "SpeechRecognitionUrl": "https://dashscope.aliyuncs.com/api/v1/services/audio/asr/paraformer",
    "Model": "paraformer-v1"
  }
  // 系统默认配置（未配置则取默认值） END
}