﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    internal class UserDetailResult : BaseResult
    {
        [JsonPropertyName("userid")]
        public virtual string UserId { get; set; } = string.Empty;

        [JsonPropertyName("gender")]
        public virtual string Gender { get; set; } = string.Empty;

        [JsonPropertyName("avatar")]
        public virtual string Avatar { get; set; } = string.Empty;

        [JsonPropertyName("qr_code")]
        public virtual string QrCode { get; set; } = string.Empty;

        [JsonPropertyName("mobile")]
        public virtual string Mobile { get; set; } = string.Empty;

        [JsonPropertyName("email")]
        public virtual string Email { get; set; } = string.Empty;

        [JsonPropertyName("biz_mail")]
        public virtual string BizEmail { get; set; } = string.Empty;

        [JsonPropertyName("address")]
        public virtual string Address { get; set; } = string.Empty;
    }
}