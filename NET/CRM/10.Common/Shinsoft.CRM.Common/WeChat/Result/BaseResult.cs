﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    internal abstract class BaseResult
    {
        [JsonPropertyName("errcode")]
        public virtual int ErrCode { get; set; }

        [JsonPropertyName("errmsg")]
        public virtual string ErrMsg { get; set; } = string.Empty;

        [JsonIgnore]
        public virtual bool Succeed => this.ErrCode == 0;
    }
}