﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    internal class SendCard : SendMsg<CardMsg>
    {
        [JsonPropertyName("textcard")]
        public override CardMsg Msg => base.Msg;

        [JsonIgnore]
        public string Title
        {
            get => this.Msg.Title;
            set => this.Msg.Title = value;
        }

        [JsonIgnore]
        public string Description
        {
            get => this.Msg.Description;
            set => this.Msg.Description = value;
        }

        [JsonIgnore]
        public string Url
        {
            get => this.Msg.Url;
            set => this.Msg.Url = value;
        }

        [JsonIgnore]
        public string BtnText
        {
            get => this.Msg.BtnText;
            set => this.Msg.BtnText = value;
        }
    }

    internal class CardMsg : IMsg
    {
        [JsonIgnore]
        public string MsgType => "textcard";

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        [JsonPropertyName("btntxt")]
        public string BtnText { get; set; } = "点击查看详情";
    }
}