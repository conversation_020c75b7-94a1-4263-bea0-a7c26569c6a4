﻿using Shinsoft.CRM.Common.Configration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    internal abstract class SendMsg<TMsg>
        where TMsg : class, IMsg, new()
    {
        public SendMsg()
        {
            this.Msg = new TMsg();
        }

        public virtual TMsg Msg { get; }

        [JsonIgnore]
        public List<string> To { get; set; } = new();

        [JsonPropertyName("touser")]
        public string ToUser => string.Join('|', this.To);

        [JsonPropertyName("msgtype")]
        public string MsgType => this.Msg.MsgType;

        [Json<PERSON>ropertyName("agentid")]
        public string AgentId => Config.WeChat.AgentId;

        [JsonPropertyName("safe")]
        public int Safe { get; set; }
    }
}