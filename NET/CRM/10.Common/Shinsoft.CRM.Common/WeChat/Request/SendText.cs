﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    internal class SendText : SendMsg<TextMsg>
    {
        [JsonPropertyName("text")]
        public override TextMsg Msg => base.Msg;

        [JsonIgnore]
        public string Content
        {
            get => this.Msg.Content;
            set => this.Msg.Content = value;
        }
    }

    internal class TextMsg : IMsg
    {
        [JsonIgnore]
        public string MsgType => "text";

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;
    }
}