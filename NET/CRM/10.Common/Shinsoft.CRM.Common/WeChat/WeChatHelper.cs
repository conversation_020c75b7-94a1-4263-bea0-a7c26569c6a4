﻿using Shinsoft.Core.Http;
using Shinsoft.Core.Json;
using Shinsoft.Core.NLog;
using Shinsoft.CRM.Common.Configration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common.WeChat
{
    public class WeChatHelper
    {
        public static BizResult<AccessToken> GetAccessToken()
        {
            var result = new BizResult<AccessToken>();

            var url = Config.WeChat.AccessToken.GetUrl;

            try
            {
                var json = HttpHelper.GetAsync(url).Result;

                var tokenResult = json.DeserializeInterfaceJson<AccessTokenResult>();

                if (tokenResult == null)
                {
                    result.Error("调用微信获取AccessToken接口返回值错误");
                }
                else
                {
                    if (tokenResult.Succeed)
                    {
                        var token = new AccessToken
                        {
                            Token = tokenResult.AccessToken,
                            ExpiryTime = DateTime.Now.AddSeconds(tokenResult.ExpirySecond).AddMinutes(-Config.WeChat.AccessToken.SkewMins)
                        };

                        result.Data = token;
                    }
                    else
                    {
                        result.Error(tokenResult.ErrMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }

            if (!result.Succeed)
            {
                var msg = string.Join(",", result.Messages);

                NLogHelper.Error("获取微信AccessToken", msg);
            }

            return result;
        }

        public static BizResult<UserInfo> GetUserInfo(string accessToken, string code)
        {
            var result = new BizResult<UserInfo>();

            var urlFormat = Config.WeChat.User.GetInfoUrlFormat;

            try
            {
                var url = string.Format(urlFormat, accessToken, code);

                var json = HttpHelper.GetAsync(url).Result;

                var userInfoResult = json.DeserializeInterfaceJson<UserInfoRessult>();

                if (userInfoResult == null)
                {
                    result.Error("调用微信用户信息接口返回值错误");
                }
                else
                {
                    if (userInfoResult.Succeed)
                    {
                        var userInfo = new UserInfo
                        {
                            UserId = userInfoResult.UserId,
                            UserTicket = userInfoResult.UserTicket,
                        };

                        result.Data = userInfo;
                    }
                    else
                    {
                        result.Error(userInfoResult.ErrMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }

            if (!result.Succeed)
            {
                var msg = string.Join(",", result.Messages);

                NLogHelper.Error("获取微信用户信息", msg);
            }

            return result;
        }

        public static BizResult<UserDetail> GetUserDetail(string accessToken, string ticket)
        {
            var result = new BizResult<UserDetail>();

            if (ticket.IsEmpty())
            {
                result.Error("user_ticket不可以为空");
            }
            else
            {
                var urlFormat = Config.WeChat.User.GetDetailUrlFormat;

                try
                {
                    var url = string.Format(urlFormat, accessToken);

                    var request = new UserTicket
                    {
                        Ticket = ticket
                    };

                    var requestJson = request.ToJson();

                    var json = HttpHelper.PostJsonAsync(url, requestJson).Result;

                    var userDetailResult = json.DeserializeInterfaceJson<UserDetailResult>();

                    if (userDetailResult == null)
                    {
                        result.Error("调用微信获取用户敏感信息接口返回值错误");
                    }
                    else
                    {
                        if (userDetailResult.Succeed)
                        {
                            var userDetail = new UserDetail
                            {
                                UserId = userDetailResult.UserId,
                                Gender = userDetailResult.Gender,
                                Avatar = userDetailResult.Avatar,
                                QrCode = userDetailResult.QrCode,
                                Mobile = userDetailResult.Mobile,
                                Email = userDetailResult.Email,
                                BizEmail = userDetailResult.BizEmail,
                                Address = userDetailResult.Address,
                            };

                            result.Data = userDetail;
                        }
                        else
                        {
                            result.Error(userDetailResult.ErrMsg);
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Error(ex.Message);
                }
            }

            if (!result.Succeed)
            {
                var msg = string.Join(",", result.Messages);

                NLogHelper.Error("获取微信用户敏感信息", msg);
            }

            return result;
        }

        public static BizResult<UserDetail> GetUserDetailByCode(string accessToken, string code)
        {
            var result = new BizResult<UserDetail>();

            var infoResult = GetUserInfo(accessToken, code);

            if (!infoResult.Succeed)
            {
                result.Merge(infoResult);
            }
            else
            {
                var userInfo = infoResult.Data.Value();

                result = GetUserDetail(accessToken, userInfo.UserTicket);
            }

            return result;
        }

        protected static BizResult<string> SendJson(string accessToken, List<string> to, string requestJson)
        {
            var result = new BizResult<string>();

            var urlFormat = Config.WeChat.Msg.SendUrlFormat;

            try
            {
                var url = string.Format(urlFormat, accessToken);

                var json = HttpHelper.PostJsonAsync(url, requestJson).Result;

                var sendMsgResult = json.DeserializeInterfaceJson<SendMsgResult>();

                if (sendMsgResult == null)
                {
                    result.Error("调用微信发送消息接口返回值错误");
                }
                else
                {
                    if (sendMsgResult.Succeed)
                    {
                        result.Data = sendMsgResult.MsgId;
                    }
                    else
                    {
                        result.Error(sendMsgResult.ErrMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }

            return result;
        }

        public static BizResult<string> SendText(string accessToken, List<string> to, string text)
        {
            var result = new BizResult<string>();

            to = to.Where(p => !p.IsEmpty()).Select(p => p.Trim()).Distinct().ToList();

            if (!to.Any())
            {
                result.Error("请选择需要发送消息的用户");
            }

            if (text.IsEmpty())
            {
                result.Error("请输入需要发送的消息");
            }

            if (result.Succeed)
            {
                var request = new SendText
                {
                    To = to,
                    Content = text
                };

                var requestJson = request.ToJson();

                result = SendJson(accessToken, to, requestJson);
            }

            return result;
        }

        public static BizResult<string> SendText(string accessToken, string to, string text)
        {
            return SendText(accessToken, new List<string> { to }, text);
        }

        public static BizResult<string> SendCard(string accessToken, List<string> to, string title, string description, string url, string? btnText = null)
        {
            var result = new BizResult<string>();

            to = to.Where(p => !p.IsEmpty()).Select(p => p.Trim()).Distinct().ToList();

            if (!to.Any())
            {
                result.Error("请选择需要发送消息的用户");
            }

            if (title.IsEmpty())
            {
                result.Error("请输入需要发送的消息标题");
            }

            if (description.IsEmpty())
            {
                result.Error("请输入需要发送的消息内容");
            }

            if (url.IsEmpty())
            {
                result.Error("请输入需要发送的消息链接");
            }

            if (result.Succeed)
            {
                var request = new SendCard
                {
                    To = to,
                    Title = title,
                    Description = description,
                    Url = url,
                };

                if (!btnText.IsEmpty())
                {
                    request.BtnText = btnText!;
                }

                var requestJson = request.ToJson();

                result = SendJson(accessToken, to, requestJson);
            }

            return result;
        }

        public static BizResult<string> SendCard(string accessToken, string to, string title, string description, string url, string? btnText = null)
        {
            return SendCard(accessToken, new List<string> { to }, title, description, url, btnText);
        }


        public static BizResult<Ticket> GetJsapiTicket(string accessToken)
        {
            var result = new BizResult<Ticket>();

            var url = string.Format(Config.WeChat.Authorize.TicketUri, accessToken);
            try
            {
                var json = HttpHelper.GetAsync(url).Result;
                var ticketResult = json.DeserializeInterfaceJson<TicketResult>();

                if (ticketResult == null)
                {
                    result.Error("调用微信获取Ticket接口返回值错误");
                }
                else
                {
                    if (ticketResult.Succeed)
                    {
                        var ticket = new Ticket
                        {
                            StrTicket = ticketResult.StrTicket,
                            ExpiryTime = DateTime.Now.AddSeconds(ticketResult.ExpirySecond).AddMinutes(-Config.WeChat.AccessToken.SkewMins)
                        };

                        result.Data = ticket;
                    }
                    else
                    {
                        result.Error(ticketResult.ErrMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }

            if (!result.Succeed)
            {
                var msg = string.Join(",", result.Messages);

                NLogHelper.Error("获取微信Ticket", msg);
            }

            return result;
        }

        public static BizResult<Ticket> GetJsapiAgentTicket(string accessToken)
        {
            var result = new BizResult<Ticket>();

            //var url = Config.WeChat.Authorize.TicketUri;
            var url = string.Format(Config.WeChat.Authorize.AgentTicketUri, accessToken);
            try
            {
                var json = HttpHelper.GetAsync(url).Result;
                
                var ticketResult = json.DeserializeInterfaceJson<TicketResult>();

                if (ticketResult == null)
                {
                    result.Error("调用微信获取AgentTicket接口返回值错误");
                }
                else
                {
                    if (ticketResult.Succeed)
                    {
                        var ticket = new Ticket
                        {
                            StrTicket = ticketResult.StrTicket,
                            ExpiryTime = DateTime.Now.AddSeconds(ticketResult.ExpirySecond).AddMinutes(-Config.WeChat.AccessToken.SkewMins)
                        };

                        result.Data = ticket;
                    }
                    else
                    {
                        result.Error(ticketResult.ErrMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }

            if (!result.Succeed)
            {
                var msg = string.Join(",", result.Messages);

                NLogHelper.Error("获取微信AgentTicket", msg);
            }

            return result;
        }
    }
}