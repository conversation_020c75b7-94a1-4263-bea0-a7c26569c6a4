﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Shinsoft.Core">
      <HintPath>..\..\00.Reference\net6.0\Shinsoft.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Using Include="Shinsoft.CRM.Entities" />
    <Using Include="Shinsoft.Core" />
  </ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\..\20.EntityFrameworkCore\Shinsoft.CRM.Entities\Shinsoft.CRM.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
