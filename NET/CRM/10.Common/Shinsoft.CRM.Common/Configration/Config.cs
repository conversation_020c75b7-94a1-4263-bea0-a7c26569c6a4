﻿using Shinsoft.Core.Configuration;
using Shinsoft.CRM.Entities;
using System;
using System.Collections.Generic;
using Shinsoft.Core;
using System.Text.Json;
using System.Text.Encodings.Web;
using System.Text.Json.Serialization;
using Shinsoft.Core.Json.Converters;
using System.Text;

namespace Shinsoft.CRM.Common.Configration
{
    public abstract class Config : BaseConfig
    {
        public new static string GetConnectionString(string key)
        {
            var connStr = BaseConfig.GetConnectionString(key);

            return DecryptConnStr(connStr);
        }

        public static string DecryptConnStr(string connStr)
        {
            if (!connStr.Contains("SERVER", StringComparison.OrdinalIgnoreCase)
                && !connStr.Contains(' '))
            {
                // 已加密，需解密

                connStr = DesHelper.DecryptDES(connStr, Config.SecretDesKey);
            }

            return connStr;
        }

        public static Guid DefaultCompanyId => AppSetting.GetMemberValue(Guid.Empty);

        /// <summary>
        /// 标题
        /// </summary>
        public static string Title => Config.Debug
            ? $"【测试版】{AppSetting.GetMemberValue()}"
            : AppSetting.GetMemberValue();

        public static readonly List<EmployeeStatus> AllowLoginEmployeeStatus = new()
        {
            EmployeeStatus.InService
        };

        public static string SecretDesKey => AppSetting.GetMemberValue("CRM-DES-KEY");

        public static string MobileRegex => AppSetting.GetMemberValue(@"^1[356789]\d{9}$");

        public static string EmailRegex => AppSetting.GetMemberValue(@"^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$");

        public static string SysRedis => AppSetting.GetMemberValue();

        public static string DefaultPwd => AppSetting.GetMemberValue(@"123456");

        public static int PwdExpiredMonths => AppSetting.GetMemberValue(3);

        public static string PdfSavePath => AppSetting.GetValue("PdfSavePath");

        public static string EventExcludeBu => AppSetting.GetMemberValue("10_56|MS");

        public abstract class Role
        {
            /// <summary>
            /// 合规审核角色编码
            /// </summary>
            public static string ComplianceAuditor => AppSetting.GetValue("Role:ComplianceAuditor", "Compliance_Auditor");
        }

        public abstract class SitePath
        {
            public static string Base => AppSetting.GetValue("SitePath:Base");

            public static string Api => AppSetting.GetValue("SitePath:Api", "api");

            private static string? _ApiUri = null;

            public static string ApiUri
            {
                get
                {
                    if (_ApiUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = Api.Trim().TrimEnd('/');

                        if (path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase))
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _ApiUri = uri.ToString();
                    }

                    return _ApiUri;
                }
            }

            public static string Mobile => AppSetting.GetValue("SitePath:Mobile", "");

            private static string? _MobileUri = null;

            public static string MobileUri
            {
                get
                {
                    if (_MobileUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = Mobile.Trim().TrimEnd('/');

                        if (path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase))
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _MobileUri = uri.ToString();
                    }

                    return _MobileUri;
                }
            }

            public static string PC => AppSetting.GetValue("SitePath:PC", "pc");

            private static string? _PcUri = null;

            public static string PcUri
            {
                get
                {
                    if (_PcUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = PC.Trim().TrimEnd('/');

                        if (path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase))
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _PcUri = uri.ToString();
                    }

                    return _PcUri;
                }
            }
        }

        public static class File
        {
            public static string ImportFilePath => AppSetting.GetValue("File:ImportFilePath");
        }

        public new class SsoSite : BaseConfig.SsoSite
        {
            public static class CDMS
            {
                public static string SsoUrl => AppSetting.GetValue("SsoSite:CDMS:SsoUrl");
                public static string FilePath => AppSetting.GetValue("SsoSite:CDMS:FilePath");
                public static string CdmsKey => AppSetting.GetValue("SsoSite:CDMS:CdmsKey");
            }
        }

        public abstract class Map
        {
            public static string Provider => AppSetting.GetValue("Map:Provider");
            public static string CodeServer => AppSetting.GetValue("Map:CodeServer");
            public static string CoordType => AppSetting.GetValue("Map:CoordType", "gcj02");

            public static string BaseUrl => AppSetting.GetValue("Map:BaseUrl");
        }

        public abstract class WeChat
        {
            public static bool IsWeChatValidation => AppSetting.GetValue("WeChat:IsWeChatValidation", false);
            public static string Corpid => AppSetting.GetValue("WeChat:Corpid");
            public static string CorpSecret => AppSetting.GetValue("WeChat:CorpSecret");
            public static string AgentId => AppSetting.GetValue("WeChat:AgentId");

            public abstract class Authorize
            {
                public static string UrlFormat => AppSetting.GetValue("WeChat:Authorize:UrlFormat", "https://open.weixin.qq.com/connect/oauth2/authorize?response_type=code&appid={0}&agentid={1}&scope={2}&redirect_uri={3}&state={4}#wechat_redirect");

                public static string RedirectUri => AppSetting.GetValue("WeChat:Authorize:RedirectUri");
                public static string TicketUri => AppSetting.GetValue("WeChat:Authorize:TicketUri");
                public static string AgentTicketUri => AppSetting.GetValue("WeChat:Authorize:AgentTicketUri");

                public static string BaseUrl => GetAuthUrl(false, RedirectUri);
                public static string PrivateInfoUrl => GetAuthUrl(true, RedirectUri);

                public static string GetAuthUrl(bool manual, string redirectUri, object? state = null)
                {
                    var scope = manual ? "snsapi_privateinfo" : "snsapi_base";

                    return string.Format(UrlFormat, WeChat.Corpid, WeChat.AgentId, scope, redirectUri.ToUrlEncode(), state?.ToString()?.ToUrlEncode());
                }
            }

            public abstract class AccessToken
            {
                public static string GetUrlFormat => AppSetting.GetValue("WeChat:AccessToken:GetUrlFormat", "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={0}&corpsecret={1}");

                public static string GetUrl => string.Format(GetUrlFormat, WeChat.Corpid, WeChat.CorpSecret);

                public static int SkewMins => AppSetting.GetValue("WeChat:AccessToken:SkewMins", 5);
            }

            public abstract class User
            {
                public static string GetInfoUrlFormat => AppSetting.GetValue("WeChat:User:GetInfoUrlFormat", "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token={0}&code={1}");
                public static string GetDetailUrlFormat => AppSetting.GetValue("WeChat:User:GetDetailUrlFormat", "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token={0}");
            }

            public abstract class Msg
            {
                public static string SendUrlFormat => AppSetting.GetValue("WeChat:Msg:SendUrlFormat", "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={0}");
            }
        }

        public abstract class Event
        {
            public abstract class Meeting
            {
                public static string SpeechDocAuditorPosition => AppSetting.GetValue("Event:Meeting:SpeechDocAuditorPosition", "Medical_BSM");
            }
        }
    }
}