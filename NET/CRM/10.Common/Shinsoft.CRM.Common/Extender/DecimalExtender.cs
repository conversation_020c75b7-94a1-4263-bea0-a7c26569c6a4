﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class DecimalExtender
    {
        private const decimal _minScore = 0.5m;

        public static decimal ToScore(this decimal self)
        {
            return self <= 0
                ? 0
                : self >= 5
                    ? 5
                    : Convert.ToInt32(self / _minScore) * _minScore;
        }
    }
}