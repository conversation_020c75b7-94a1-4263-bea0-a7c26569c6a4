﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.Util;
using Shinsoft.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace Shinsoft.CRM.Common
{
    public static class ExcelHelper
    {
        public static byte[] WriteToExcelBytes(DataTable table, string sheetName)
        {
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet(sheetName);

            IDataFormat format = workbook.CreateDataFormat();

            //表头样式
            ICellStyle hstyle = workbook.CreateCellStyle();
            hstyle.Alignment = HorizontalAlignment.Center;//居中对齐
            hstyle.VerticalAlignment = VerticalAlignment.Center;
            //背景
            hstyle.BorderTop = BorderStyle.Thin;
            hstyle.BorderBottom = BorderStyle.Thin;
            hstyle.BorderLeft = BorderStyle.Thin;
            hstyle.BorderRight = BorderStyle.Thin;
            hstyle.FillBackgroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillForegroundColor = HSSFColor.PaleBlue.Index;
            hstyle.FillPattern = FillPattern.SolidForeground;
            //边框
            hstyle.BottomBorderColor = HSSFColor.Black.Index;
            hstyle.TopBorderColor = HSSFColor.Black.Index;
            hstyle.LeftBorderColor = HSSFColor.Black.Index;
            hstyle.RightBorderColor = HSSFColor.Black.Index;
            //表头字体设置
            IFont hfont = workbook.CreateFont();
            hfont.FontHeightInPoints = 12;//字号
            hfont.Boldweight = 600;//加粗
            hstyle.SetFont(hfont);

            IRow headerRow = sheet.CreateRow(0);
            // excel标题行
            for (int j = 0; j < table.Columns.Count; j++)
            {
                ICell hCell = headerRow.CreateCell(table.Columns[j].Ordinal);
                hCell.SetCellValue(table.Columns[j].Caption);
                hCell.CellStyle = hstyle;
            }

            ICellStyle style1 = workbook.CreateCellStyle();
            style1.DataFormat = format.GetFormat("#,##0");

            ICellStyle style2 = workbook.CreateCellStyle();
            style2.DataFormat = format.GetFormat("#,##0.00");

            ICellStyle style3 = workbook.CreateCellStyle();
            style3.DataFormat = format.GetFormat("yyyy-MM-dd HH:mm:ss");

            Dictionary<string, ICellStyle> dicStyle = new Dictionary<string, ICellStyle>();
            dicStyle.Add("style1", style1);
            dicStyle.Add("style2", style2);
            dicStyle.Add("style3", style3);

            int rowIndex = 1;
            foreach (DataRow row in table.Rows)
            {
                IRow dataRow = sheet.CreateRow(rowIndex);
                foreach (DataColumn column in table.Columns)
                {
                    ICell dCell = dataRow.CreateCell(column.Ordinal);

                    SetSheetColumnDefaultStyle(workbook, row, dCell, column, dicStyle);
                }
                rowIndex++;
            }

            // 列宽自适应
            for (int col = 0; col < table.Columns.Count; col++)
            {
                int maxLength = 0;
                int sampleSize = Math.Min(20, table.Rows.Count);

                // 仅考虑部分数据行来确定列宽
                for (int row = 0; row < sampleSize; row++)
                {
                    string cellValue = table.Rows[row][col].AsString();
                    maxLength = Math.Max(maxLength, cellValue.Length);
                }

                int maxWidth = maxLength + 5;
                int minWidth = 10;

                sheet.SetColumnWidth(col, Math.Max(minWidth, maxWidth) * 256);
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                byte[] buff = ms.ToArray();
                if (workbook != null)
                {
                    workbook.Close();
                }

                return buff;
            }
        }

        private static void SetSheetColumnDefaultStyle(IWorkbook workbook, DataRow row, ICell dCell, DataColumn column, Dictionary<string, ICellStyle> dicStyle)
        {
            Type type = column.DataType;

            if (type == typeof(short) || type == typeof(int) || type == typeof(long)
                || type == typeof(short?) || type == typeof(int?) || type == typeof(long?))
            {
                dCell.CellStyle = dicStyle["style1"];
                if (!row[column].AsString().IsEmpty())
                {
                    dCell.SetCellValue(row[column].AsString().As<int>());
                }
            }
            else if (type == typeof(decimal) || type == typeof(double) || type == typeof(float)
                     || type == typeof(decimal?) || type == typeof(double?) || type == typeof(float?))
            {
                dCell.CellStyle = dicStyle["style2"];
                dCell.SetCellValue(row[column].AsString().As<double>());
            }
            else if (type == typeof(DateTime) || type == typeof(DateTime?))
            {
                DateTime dtDate;
                if (DateTime.TryParse(row[column].AsString(), out dtDate))
                {
                    dCell.CellStyle = dicStyle["style3"];
                    dCell.SetCellValue(dtDate);
                }
            }
            else if (type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
            {
                DateTime dtDate;
                if (DateTime.TryParse(row[column].AsString(), out dtDate))
                {
                    dCell.CellStyle = dicStyle["style3"];
                    dCell.SetCellValue(dtDate);
                }
            }
            else
            {
                dCell.SetCellValue(row[column].AsString());
            }
        }

        public static DataTable ReadTemplateFromExcelToDataTable(Stream stream, string extension = ".xlsx", bool numAsString = false)
        {
            stream.Position = 0;
            try
            {
                IWorkbook workbook;
                if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream); //07版本及以上
                }

                var dt = new DataTable();
                //读取当前表数据
                ISheet sheet = workbook.GetSheetAt(0);
                //表头
                IRow header = sheet.GetRow(sheet.FirstRowNum);
                List<int> columns = new();

                for (int i = 0; i < header.LastCellNum; i++)
                {
                    object obj = GetValueType(header.Cells[i]);
                    if (obj == null || obj.ToString()?.Trim() == string.Empty)
                    {
                        continue;
                    }
                    else
                        dt.Columns.Add(new DataColumn(obj.ToString()));
                    columns.Add(i);
                }
                //数据
                for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                {
                    int nullCount = 0;
                    var sheetRow = sheet.GetRow(i);
                    if (sheetRow == null)
                        continue;

                    DataRow dr = dt.NewRow();

                    foreach (int j in columns)
                    {
                        var cell = sheetRow.GetCell(j);
                        if (cell == null)
                        {
                            ++nullCount;
                            continue;
                        }
                        var obj = GetValueType(cell);
                        if (obj == null || obj.ToString() == string.Empty || string.IsNullOrWhiteSpace(obj.ToString()))
                            ++nullCount;

                        dr[j] = obj?.ToString()?.Trim();
                    }
                    //跳过空行
                    if (nullCount != columns.Count)
                        dt.Rows.Add(dr);
                }
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
            }
            finally
            {
                stream.Dispose();
            }
        }

        private static object GetValueType(ICell cell, bool numAsString = false)
        {
            if (cell == null)
                return "";
            switch (cell.CellType)
            {
                case CellType.Blank: //BLANK:
                    return "";

                case CellType.Boolean: //BOOLEAN:
                    return cell.BooleanCellValue;

                case CellType.Numeric: //NUMERIC:

                    if (numAsString)
                    {
                        return cell.StringCellValue;
                    }
                    else
                    {
                        try
                        {
                            var cellValue = cell.ToString();
                            if (!string.IsNullOrEmpty(cellValue) && IsNumeric(cellValue))
                            {
                                return cell.NumericCellValue;
                            }

                            DateTime date = cell.DateCellValue;
                            return date.ToString().Replace("0:00:00", "");
                        }
                        catch
                        {
                            return cell.NumericCellValue;
                        }
                    }

                case CellType.String: //STRING:
                    return cell.StringCellValue;

                case CellType.Error: //ERROR:
                    return cell.ErrorCellValue;

                default:
                    return "=" + cell.CellFormula;
            }
        }

        private static bool IsNumeric(string value)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
        }
    }
}