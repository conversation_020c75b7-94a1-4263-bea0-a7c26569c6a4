﻿using Shinsoft.CRM.Common.Configration;
using Shinsoft.CRM.Common.Map;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class MapHelper
    {
        #region 根据地址获取坐标(百度/腾讯/高德)

        public static BizResult<MapLoc> GetCoordByAddress(string address, string city = "")
        {
            var result = new BizResult<MapLoc>();

            var msg = MapService.GetCoordByAddress(Config.Map.Provider, Config.Map.CodeServer, address, city, Config.Map.CoordType);

            if (msg.StartsWith("[Error]"))
            {
                result.Error(msg[7..]);
            }
            else
            {
                var locs = msg.Split(',', StringSplitOptions.TrimEntries).ToList();

                if (Config.Map.Provider == "qq")
                {
                    if (locs.Count < 2 || locs.Take(1).Any(p => !decimal.TryParse(p, out var decimalValue)))
                    {
                        result.Error($"返回值格式错误【{msg}】");
                    }
                    else
                    {
                        result.Data = new MapLoc()
                        {
                            X = locs[0].As<decimal>(),
                            Y = locs[1].As<decimal>(),
                            Province = locs.Count() >= 3 ? locs[2].As<string>() : "",
                            City = locs.Count() >= 4 ? locs[3].As<string>() : "",
                            District = locs.Count() >= 5 ? locs[4].As<string>() : "",
                            Street = locs.Count() >= 6 ? locs[5].As<string>() : ""
                        };
                    }
                }
                else
                {
                    if (locs.Count != 2 || locs.Any(p => !decimal.TryParse(p, out var decimalValue)))
                    {
                        result.Error($"返回值格式错误【{msg}】");
                    }
                    else
                    {
                        result.Data = new MapLoc()
                        {
                            X = locs[0].As<decimal>(),
                            Y = locs[1].As<decimal>()
                        };
                    }
                }
            }

            return result;
        }

        #endregion 根据地址获取坐标(百度/腾讯/高德)

        #region 根据坐标获取地址(百度/腾讯/高德)

        public static BizResult<string> GetAddressByCoord(decimal locX, decimal locY)
        {
            var result = new BizResult<string>();

            var coord = $"{locX},{locY}";

            var msg = MapService.GetAddressByCoord(Config.Map.Provider, Config.Map.CodeServer, coord, "address", Config.Map.CoordType);

            if (msg.StartsWith("[Error]"))
            {
                result.Error(msg[7..]);
            }
            else
            {
                result.Data = msg;
            }

            return result;
        }

        public static BizResult<string> GetAddressByCoord(MapLoc loc)
        {
            return GetAddressByCoord(loc.X, loc.Y);
        }

        #endregion 根据坐标获取地址(百度/腾讯/高德)
    }
}