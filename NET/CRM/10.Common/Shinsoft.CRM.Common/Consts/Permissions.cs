﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class Permissions
    {
        /// <summary>
        /// 系统
        /// </summary>
        public static class System
        {
            /// <summary>
            /// 隐藏
            /// </summary>
            public static class Invisible
            {
                /// <summary>
                /// 系统设置【隐藏】
                /// </summary>
                public const string System_Setup = "System_Setup";

                /// <summary>
                /// 系统缓存管理【隐藏】
                /// </summary>
                public const string SysCache_Manage = "SysCache_Manage";

                /// <summary>
                /// 公司设置【隐藏】
                /// </summary>
                public const string Company_Setup = "Company_Setup";

                /// <summary>
                /// 公司缓存管理【隐藏】
                /// </summary>
                public const string CompanyCache_Manage = "CompanyCache_Manage";
            }

            /// <summary>
            /// 邮件
            /// </summary>
            public static class Mail
            {
                /// <summary>
                /// 邮件查询【暂不使用】
                /// </summary>
                public const string Mail_Query = "Mail_Query";

                /// <summary>
                /// 邮件管理【暂不使用】
                /// </summary>
                public const string Mail_Manage = "Mail_Manage";
            }

            /// <summary>
            /// 日志
            /// </summary>
            public static class Log
            {
                /// <summary>
                /// 日志查询【暂不使用】
                /// </summary>
                public const string Log_Query = "Log_Query";
            }

            /// <summary>
            /// 消息
            /// </summary>
            public static class Msg
            {
                /// <summary>
                /// 消息公告管理
                /// </summary>
                public const string Msg_Announce = "Msg_Announce";
            }
        }

        /// <summary>
        /// 权限管理
        /// </summary>
        public static class Authorization
        {
            /// <summary>
            /// 用户
            /// </summary>
            public static class User
            {
                /// <summary>
                /// 用户查询【暂不使用】
                /// </summary>
                public const string User_Query = "User_Query";

                /// <summary>
                /// 用户管理【暂不使用】
                /// </summary>
                public const string User_Manage = "User_Manage";
            }

            /// <summary>
            /// 员工
            /// </summary>
            public static class Employee
            {
                /// <summary>
                /// 员工查询
                /// </summary>
                public const string Employee_Query = "Employee_Query";

                /// <summary>
                /// 员工管理【暂不使用】
                /// </summary>
                public const string Employee_Manage = "Employee_Manage";
            }

            /// <summary>
            /// 角色
            /// </summary>
            public static class Role
            {
                /// <summary>
                /// 角色查询【暂不使用】
                /// </summary>
                public const string Role_Query = "Role_Query";

                /// <summary>
                /// 角色管理
                /// </summary>
                public const string Role_Manage = "Role_Manage";

                /// <summary>
                /// 管理员
                /// </summary>
                public const string Administrator = "Administrator";
            }
        }

        /// <summary>
        /// 基础数据维护
        /// </summary>
        public static class BasicData
        {
            /// <summary>
            /// 字典管理（含业务字典）
            /// </summary>
            public static class Dict
            {
                /// <summary>
                /// 字典查询【暂不使用】
                /// </summary>
                public const string Dict_Query = "Dict_Query";

                /// <summary>
                /// 字典维护【暂不使用】
                /// </summary>
                public const string Dict_Maintain = "Dict_Maintain";
            }

            /// <summary>
            /// 公司数据
            /// </summary>
            public static class CompanyData
            {
                /// <summary>
                /// 分公司查询【暂不使用】
                /// </summary>
                public const string SubCompany_Query = "SubCompany_Query";

                /// <summary>
                /// 分公司维护【暂不使用】
                /// </summary>
                public const string SubCompany_Maintain = "SubCompany_Maintain";

                /// <summary>
                /// 部门查询【暂不使用】
                /// </summary>
                public const string Department_Query = "Department_Query";

                /// <summary>
                /// 部门维护【暂不使用】
                /// </summary>
                public const string Department_Maintain = "Department_Maintain";

                /// <summary>
                /// 成本中心查询【暂不使用】
                /// </summary>
                public const string CostCenter_Query = "CostCenter_Query";

                /// <summary>
                /// 成本中心维护【暂不使用】
                /// </summary>
                public const string CostCenter_Maintain = "CostCenter_Maintain";

                /// <summary>
                /// 职位查询【暂不使用】
                /// </summary>
                public const string Position_Query = "Position_Query";

                /// <summary>
                /// 职位维护【暂不使用】
                /// </summary>
                public const string Position_Maintain = "Position_Maintain";

                /// <summary>
                /// 岗位查询【暂不使用】
                /// </summary>
                public const string Station_Query = "Station_Query";

                /// <summary>
                /// 岗位维护【暂不使用】
                /// </summary>
                public const string Station_Maintain = "Station_Maintain";

                /// <summary>
                /// 人岗维护【暂不使用】
                /// </summary>
                public const string EmployeeStation_Maintain = "EmployeeStation_Maintain";
            }
        }

        /// <summary>
        /// 报表
        /// </summary>
        public static class Report
        {
            /// <summary>
            /// PC端报表
            /// </summary>
            public static class PC
            {
                /// <summary>
                /// PC端报表查看（全部）
                /// </summary>
                public const string View_All = "Report_PC_View_All";

                /// <summary>
                /// 查看PC端报表（需要配合Tag使用）【暂不使用】
                /// </summary>
                public const string View = "Report_PC_View";
            }

            public static class Mobile
            {
                /// <summary>
                /// 移动端报表查看（全部）
                /// </summary>
                public const string View_All = "Report_Mobile_View_All";

                /// <summary>
                /// 移动端报表查看（需要配合Tag使用）【暂不使用】
                /// </summary>
                public const string View = "Report_Mobile_View";
            }
        }

        /// <summary>
        /// 业务主数据维护
        /// </summary>
        public static class BizData
        {
            /// <summary>
            /// 业务字典
            /// </summary>
            public static class BizDict
            {
                /// <summary>
                /// 业务字典查询【暂不使用】
                /// </summary>
                public const string BizDict_Query = "BizDict_Query";

                /// <summary>
                /// 业务字典维护
                /// </summary>
                public const string BizDict_Maintain = "BizDict_Maintain";
            }

            /// <summary>
            /// 机构
            /// </summary>
            public static class Customer
            {
                /// <summary>
                /// 机构查询（全部）
                /// </summary>
                public const string Customer_Query_All = "Customer_Query_All";

                /// <summary>
                /// 机构维护（全部）【暂不使用】
                /// </summary>
                public const string Customer_Maintain_All = "Customer_Maintain_All";

                /// <summary>
                /// 机构查询（负责的）【自动赋予登录用户】
                /// </summary>
                public const string Customer_Query_Owner = "Customer_Query_Owner";

                /// <summary>
                /// 机构维护（负责的）【自动赋予登录用户】
                /// </summary>
                public const string Customer_Maintain_Owner = "Customer_Maintain_Owner";

                /// <summary>
                /// 机构数据审核（全部）
                /// </summary>
                public const string Customer_Audit_All = "Customer_Audit_All";

                /// <summary>
                /// 机构数据审核（下属）【自动赋予LM】【暂不使用】
                /// </summary>
                public const string Customer_Audit_Sub = "Customer_Audit_Sub";

                /// <summary>
                /// 机构分类查询【暂不使用】
                /// </summary>
                public const string CustomerCategory_Query = "CustomerCategory_Query";

                /// <summary>
                /// 机构分类维护
                /// </summary>
                public const string CustomerCategory_Maintain = "CustomerCategory_Maintain";
            }

            /// <summary>
            /// 客户
            /// </summary>
            public static class Contact
            {
                /// <summary>
                /// 客户查询（全部）
                /// </summary>
                public const string Contact_Query_All = "Contact_Query_All";

                /// <summary>
                /// 客户维护（全部）【暂不使用】
                /// </summary>
                public const string Contact_Maintain_All = "Contact_Maintain_All";

                /// <summary>
                /// 客户查询（负责的）【自动赋予登录用户】
                /// </summary>
                public const string Contact_Query_Owner = "Contact_Query_Owner";

                /// <summary>
                /// 客户维护（负责的）【自动赋予登录用户】
                /// </summary>
                public const string Contact_Maintain_Owner = "Contact_Maintain_Owner";

                /// <summary>
                /// 客户数据审核（全部）
                /// </summary>
                public const string Contact_Audit_All = "Contact_Audit_All";

                /// <summary>
                /// 机构数据审核（下属）【自动赋予LM】
                /// </summary>
                public const string Contact_Audit_Sub = "Contact_Audit_Sub";
            }

            /// <summary>
            /// 产品
            /// </summary>
            public static class Product
            {
                /// <summary>
                /// 产品查询【暂不使用】
                /// </summary>
                public const string Product_Query = "Product_Query";

                /// <summary>
                /// 产品维护
                /// </summary>
                public const string Product_Maintain = "Product_Maintain";
            }

            /// <summary>
            /// 文档资料
            /// </summary>
            public static class DocResource
            {
                /// <summary>
                /// 资料查询【暂不使用】
                /// </summary>
                public const string DocResource_Query = "DocResource_Query";

                /// <summary>
                /// 资料维护
                /// </summary>
                public const string DocResource_Maintain = "DocResource_Maintain";
            }

            /// <summary>
            /// 会议类型
            /// </summary>
            public static class MeetingCategory
            {
                /// <summary>
                /// 会议类型查询
                /// </summary>
                public const string MeetingCategory_Query = "MeetingCategory_Query";

                /// <summary>
                /// 会议类型维护
                /// </summary>
                public const string MeetingCategory_Maintain = "MeetingCategory_Maintain";
            }

            /// <summary>
            /// 医院开发类型
            /// </summary>
            public static class HospitalDevelopCategory
            {
                /// <summary>
                /// 医院开发类型查询
                /// </summary>
                public const string HospitalDevelopCategory_Query = "HospitalDevelopCategory_Query";

                /// <summary>
                /// 医院开发类型维护
                /// </summary>
                public const string HospitalDevelopCategory_Maintain = "HospitalDevelopCategory_Maintain";
            }

            /// <summary>
            /// 题库
            /// </summary>
            public static class Question
            {
                /// <summary>
                /// 题库查询
                /// </summary>
                public const string Question_Query = "Question_Query";

                /// <summary>
                /// 题库维护
                /// </summary>
                public const string Question_Maintain = "Question_Maintain";
            }
        }

        /// <summary>
        /// 行为
        /// </summary>
        public static class Event
        {
            /// <summary>
            /// 查询所有行为
            /// </summary>
            public const string Query_All_Event = "Query_All_Event";

            /// <summary>
            /// 拜访
            /// </summary>
            public static class Visit
            {
                /// <summary>
                /// 拜访数据审核（全部）
                /// </summary>
                public const string Event_Visit_Audit_All = "Event_Visit_Audit_All";

                /// <summary>
                /// 拜访数据审核（下属）【自动赋予LM】
                /// </summary>
                public const string Event_Visit_Audit_Sub = "Event_Visit_Audit_Sub";

                /// <summary>
                /// 主动协访
                /// </summary>
                public const string Event_Visit_Cooperate = "Event_Visit_Cooperate";
            }

            /// <summary>
            /// 会议
            /// </summary>
            public static class Meeting
            {
                /// <summary>
                /// 会议数据审核（全部）
                /// </summary>
                public const string Event_Meeting_Audit_All = "Event_Meeting_Audit_All";

                /// <summary>
                /// 会议数据审核（下属）【自动赋予LM】
                /// </summary>
                public const string Event_Meeting_Audit_Sub = "Event_Meeting_Audit_Sub";
            }

            /// <summary>
            /// 培训
            /// </summary>
            public static class Training
            {
                /// <summary>
                /// 培训数据审核（全部）
                /// </summary>
                public const string Event_Training_Audit_All = "Event_Training_Audit_All";

                /// <summary>
                /// 培训数据审核（下属）【自动赋予LM】
                /// </summary>
                public const string Event_Training_Audit_Sub = "Event_Training_Audit_Sub";
            }

            /// <summary>
            /// 医院开发
            /// </summary>
            public static class HospitalDevelop
            {
                /// <summary>
                /// 医院开发查询（全部）
                /// </summary>
                public const string HospitalDevelop_Query_All = "HospitalDevelop_Query_All";

                /// <summary>
                /// 医院开发数据审核（全部）
                /// </summary>
                public const string HospitalDevelop_Audit_All = "HospitalDevelop_Audit_All";

                /// <summary>
                /// 医院开发数据审核（下属）【自动赋予LM】
                /// </summary>
                public const string HospitalDevelop_Audit_Sub = "HospitalDevelop_Audit_Sub";
            }
        }

        /// <summary>
        /// 流向申诉
        /// </summary>
        public static class Appeal
        {
            /// <summary>
            /// 流向申诉
            /// </summary>
            public const string Appeal_All = "Appeal_All";
        }
    }
}