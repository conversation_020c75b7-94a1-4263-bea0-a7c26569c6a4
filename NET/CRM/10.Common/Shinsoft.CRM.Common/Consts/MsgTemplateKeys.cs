﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class MsgTemplateKeys
    {
        public static class MasterDate
        {
            public static class Customer
            {
                public const string Audit = "MasterDate_Customer_Audit";
            }

            public static class Contact
            {
                public const string Audit = "MasterDate_Contact_Audit";
            }
        }

        public static class Event
        {
            public static class Visit
            {
                public const string Audit = "Event_Visit_Audit";
            }

            public static class Meeting
            {
                public const string Audit = "Event_Meeting_Audit";
            }

            public static class Training
            {
                public const string Audit = "Event_Training_Audit";
                public const string View = "Event_Training_View";
                public const string AnswerMsg = "Event_Training_AnswerMsg";                
            }

            public static class HospitalDevelop
            {
                public const string Audit = "HospitalDevelop_Audit";
            }
        }

        public static class SalesFlowComplaintsRequest
        {
            public static class Reject
            {
                /// <summary>
                /// 流向新增
                /// </summary>
                public const string Add = "ComplaintsRequest_Reject_Add";
                /// <summary>
                /// 流向申诉
                /// </summary>
                public const string Complaints = "ComplaintsRequest_Reject_Complaints";
            }
            public static class Approve
            {
                /// <summary>
                /// 流向通过
                /// </summary>
                public const string ApproveRequest = "ComplaintsRequest_Approve_Add";
            }


        }
    }
}