﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class DictKeys
    {
        public static class Sys
        {
        }

        /// <summary>
        /// 文档资料
        /// </summary>
        public static class DocResource
        {
            /// <summary>
            /// 分组
            /// </summary>
            public const string Group = "DocResource_Group";
        }

        /// <summary>
        /// 机构
        /// </summary>
        public static class Customer
        {
            public static class Hospital
            {
                /// <summary>
                /// 等级（一级，二级）
                /// </summary>
                public const string Grade = "Customer_Hospital_Grade";

                /// <summary>
                /// 等次（甲等，乙等）
                /// </summary>
                public const string Rank = "Customer_Hospital_Rank";
            }

            /// <summary>
            /// 目标客户
            /// </summary>
            public static class Target
            {
                /// <summary>
                /// 目标客户分组
                /// </summary>
                public const string Category = "Customer_Target_Category";
            }
        }

        /// <summary>
        /// 客户
        /// </summary>
        public static class Contact
        {
            public static class Doctor
            {
                /// <summary>
                /// 医生职称
                /// </summary>
                public const string Title = "Contact_Doctor_Title";

                /// <summary>
                /// 医生职务
                /// </summary>
                public const string Position = "Contact_Doctor_Position";

                /// <summary>
                /// 医生级别
                /// </summary>
                public const string Level = "Contact_Doctor_Level";

                /// <summary>
                /// 医生阶段
                /// </summary>
                public const string Stage = "Contact_Doctor_Stage";
            }

            public static class Speaker
            {
                /// <summary>
                /// 讲者级别
                /// </summary>
                public const string Level = "Contact_Speaker_Level";
            }
        }

        /// <summary>
        /// 产品
        /// </summary>
        public static class Product
        {
            /// <summary>
            /// 附件类型
            /// </summary>
            public const string AttachType = "Product_AttachType";

            /// <summary>
            /// 剂型
            /// </summary>
            public const string DosageForm = "Product_DosageForm";
        }

        /// <summary>
        /// 行为
        /// </summary>
        public static class Event
        {
            /// <summary>
            /// 拜访
            /// </summary>
            public static class Visit
            {
                /// <summary>
                /// 类型（暂不使用）
                /// </summary>
                public const string Type = "Event_Visit_Type";

                /// <summary>
                /// 形式（不使用，只是拼接线上线下用）
                /// </summary>
                public const string Mode = "Event_Visit_Mode";

                /// <summary>
                /// 形式（线下）
                /// </summary>
                public const string Mode_Offline = "Event_Visit_Mode_Offline";

                /// <summary>
                /// 形式（线上）
                /// </summary>
                public const string Mode_Online = "Event_Visit_Mode_Online";

                /// <summary>
                /// 目标
                /// </summary>
                public const string Goal = "Event_Visit_Goal";

                /// <summary>
                /// 内容
                /// </summary>
                public const string Content = "Event_Visit_Content";

                /// <summary>
                /// 结果
                /// </summary>
                public const string Result = "Event_Visit_Result";

                /// <summary>
                /// 评分项
                /// </summary>
                public const string Score = "Event_Visit_Score";

                /// <summary>
                /// 内容（其他）
                /// </summary>
                public const string Content_Other = "Other";
            }

            /// <summary>
            /// 会议
            /// </summary>
            public static class Meeting
            {
                /// <summary>
                /// 类型
                /// </summary>
                public const string Type = "Event_Meeting_Type";

                /// <summary>
                /// 目标（暂不使用）
                /// </summary>
                public const string Goal = "Event_Meeting_Goal";

                /// <summary>
                /// 结果
                /// </summary>
                public const string Result = "Event_Meeting_Result";
            }

            /// <summary>
            /// 培训
            /// </summary>
            public static class Training
            {
                /// <summary>
                /// 类型
                /// </summary>
                public const string Type = "Event_Training_Type";

                /// <summary>
                /// 目标（暂不使用）
                /// </summary>
                public const string Goal = "Event_Training_Goal";

                /// <summary>
                /// 结果
                /// </summary>
                public const string Result = "Event_Training_Result";
            }
        }

        public static class HospitalDevelop
        {
            /// <summary>
            /// 类型（暂不使用）
            /// </summary>
            public const string Type = "HospitalDevelop_Type";

            /// <summary>
            /// 目标（暂不使用）
            /// </summary>
            public const string Goal = "HospitalDevelop_Goal";

            /// <summary>
            /// 步骤
            /// </summary>
            public const string Step = "HospitalDevelop_Step";

            /// <summary>
            /// 结果
            /// </summary>
            public static class Result
            {
                /// <summary>
                /// 结果（跟进）
                /// </summary>
                public const string Following = "HospitalDevelop_Result_Following";

                /// <summary>
                /// 结果（成功）
                /// </summary>
                public const string Succeed = "HospitalDevelop_Result_Succeed";

                /// <summary>
                /// 结果（失败）
                /// </summary>
                public const string Failed = "HospitalDevelop_Result_Failed";
            }
        }
    }
}