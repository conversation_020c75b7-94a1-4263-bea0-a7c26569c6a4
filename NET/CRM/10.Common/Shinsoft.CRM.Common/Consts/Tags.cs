﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.CRM.Common
{
    public static class Tags
    {
        public static class Report
        {
            public static class PC
            {
                /// <summary>
                /// 拜访明细
                /// </summary>
                public const string VisitDetail = "Report_PC_VisitDetail";

                /// <summary>
                /// 培训完成报表
                /// </summary>
                public const string TrainingComplete = "Report_PC_TrainingComplete";

                /// <summary>
                /// 培训课程报表
                /// </summary>
                public const string TrainingReport = "Report_PC_TrainingReport";

                /// <summary>
                /// 会议报表
                /// </summary>
                public const string MeetingReport = "Report_PC_MeetingReport";
            }

            public static class Mobile
            {
                /// <summary>
                /// 个人行为汇总
                /// </summary>
                public const string MyEventSummary = "Report_Mobile_MyEventSummary";

                /// <summary>
                /// 目标客户销售占比
                /// </summary>
                public const string TargetReceiverSale = "Report_Mobile_TargetReceiverSale";

                /// <summary>
                /// 销售增长率
                /// </summary>
                public const string RateOfIncrease = "Report_Mobile_RateOfIncrease";
                /// <summary>
                /// 销售达成率
                /// </summary>
                public const string SaleAchievingRate = "Report_Mobile_SaleAchievingRate";
            }
        }
    }
}