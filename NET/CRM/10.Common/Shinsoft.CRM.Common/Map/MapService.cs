﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Text;
using System.IO;
using Newtonsoft.Json.Linq;

#pragma warning disable CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning disable CS8602 // 解引用可能出现空引用。
#pragma warning disable CS8604 // 引用类型参数可能为 null。

#pragma warning disable SYSLIB0014 // 类型或成员已过时

namespace Shinsoft.CRM.Common.Map

{
    /// <summary>
    /// 地图提供商：百度为baidu/腾讯为qq/高德为a
    /// 使用坐标系：百度默认为bd09/腾讯默认为gcj02/高德默认为gcj02/GPS默认为wgs84，坐标系转换有性能损失且数量有限制，尽量使用地图提供商的默认坐标系
    /// 坐标字符串：纬度,经度
    /// </summary>
    public class MapService
    {
        #region 根据IP获取坐标及地址(百度/腾讯/高德)

        /// <summary>
        /// 根据IP获取坐标及地址
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="IP">查询的地址或地点名称</param>
        /// <param name="CoordType">输出的坐标系（bd09/gcj02，为空表示地图提供商的默认坐标系，腾讯/高德只支持默认坐标系）</param>
        /// <returns>坐标（纬度,经度）@地址</returns>
        public static string GetCoordByIp(string MapProvider, string MapCodeServer, string IP = "", string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetCoordByIpBaidu(MapCodeServer, IP, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetCoordByIpQQ(MapCodeServer, IP);
            }
            else if (MapProvider == "a")
            {
                return GetCoordByIpA(MapCodeServer, IP);
            }

            return "[Error]Invalid Map Provider";
        }

        private static string GetCoordByIpBaidu(string MapCodeServer, string IP = "", string CoordType = "bd09")
        {
            string Result;

            try
            {
                string URL = "https://api.map.baidu.com/location/ip?ak=";

                URL += MapCodeServer;

                if (!string.IsNullOrEmpty(IP))
                    URL += "&ip=" + IP;

                if (CoordType == "bd09")
                    URL += "&coor=bd09ll";
                else if (CoordType == "gcj02")
                    URL += "&coor=gcj02";

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        if (jo.SelectToken("..x") != null && jo.SelectToken("..y") != null)
                        {
                            Result = string.Format("{0},{1}", jo.SelectToken("..y"), jo.SelectToken("..x"));

                            string Address = "";
                            if (jo.SelectToken("..province") != null)
                                Address += "," + jo.SelectToken("..province");
                            if (jo.SelectToken("..city") != null)
                                Address += "," + jo.SelectToken("..city");

                            if (!string.IsNullOrEmpty(Address))
                                Result += "@" + Address.Substring(1);
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = "[Error]Code:" + jo["status"];
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetCoordByIpQQ(string MapCodeServer, string IP = "")
        {
            string Result;

            try
            {
                string URL = "https://apis.map.qq.com/ws/location/v1/ip?key=";

                URL += MapCodeServer;

                if (!string.IsNullOrEmpty(IP))
                    URL += "&ip=" + IP;

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        if (jo.SelectToken("..lat") != null && jo.SelectToken("..lng") != null)
                        {
                            Result = string.Format("{0},{1}", jo.SelectToken("..lat"), jo.SelectToken("..lng"));

                            string Address = "";
                            if (jo.SelectToken("..nation") != null)
                                Address += "," + jo.SelectToken("..nation");
                            if (jo.SelectToken("..province") != null)
                                Address += "," + jo.SelectToken("..province");
                            if (jo.SelectToken("..city") != null)
                                Address += "," + jo.SelectToken("..city");
                            if (jo.SelectToken("..district") != null)
                                Address += "," + jo.SelectToken("..district");

                            if (!string.IsNullOrEmpty(Address))
                                Result += "@" + Address.Substring(1);
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetCoordByIpA(string MapCodeServer, string IP = "")
        {
            string Result;

            try
            {
                string URL = "https://restapi.amap.com/v3/ip?key=";

                URL += MapCodeServer;

                if (!string.IsNullOrEmpty(IP))
                    URL += "&ip=" + IP;

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 1)
                    {
                        if (jo["rectangle"] != null)
                        {
                            string rectangle = jo["rectangle"].ToString();

                            Result = ((double.Parse(rectangle.Split(';')[0].Split(',')[1]) + double.Parse(rectangle.Split(';')[1].Split(',')[1])) / 2).ToString();
                            Result += "," + ((double.Parse(rectangle.Split(';')[0].Split(',')[0]) + double.Parse(rectangle.Split(';')[1].Split(',')[0])) / 2).ToString();

                            string Address = "";
                            if (jo.SelectToken("..nation") != null)
                                Address += "," + jo.SelectToken("..nation");
                            if (jo.SelectToken("..province") != null)
                                Address += "," + jo.SelectToken("..province");
                            if (jo.SelectToken("..city") != null)
                                Address += "," + jo.SelectToken("..city");
                            if (jo.SelectToken("..district") != null)
                                Address += "," + jo.SelectToken("..district");

                            if (!string.IsNullOrEmpty(Address))
                                Result += "@" + Address.Substring(1);
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["info"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        #endregion 根据IP获取坐标及地址(百度/腾讯/高德)

        #region 根据地址获取坐标(百度/腾讯/高德)

        /// <summary>
        /// 根据地址获取坐标
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="Address">查询的地址或地点名称</param>
        /// <param name="City">查询的城市</param>
        /// <param name="CoordType">输出的坐标系（bd09/gcj02，为空表示地图提供商的默认坐标系，腾讯只支持默认坐标系，高德只支持默认坐标系）</param>
        /// <returns>坐标（纬度,经度）</returns>
        public static string GetCoordByAddress(string MapProvider, string MapCodeServer, string Address, string City = "", string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetCoordByAddressBaidu(MapCodeServer, Address, City, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetCoordByAddressQQ(MapCodeServer, Address, City);
            }
            else if (MapProvider == "a")
            {
                return GetCoordByAddressA(MapCodeServer, Address, City);
            }

            return "[Error]Invalid Map Provider";
        }

        private static string GetCoordByAddressBaidu(string MapCodeServer, string Address, string City = "", string CoordType = "")
        {
            string Result;

            try
            {
                string URL = "https://api.map.baidu.com/geocoding/v3/?address={0}&city={1}&output=json&ak={2}";

                URL = String.Format(URL, Address, City, MapCodeServer);

                if (CoordType == "gcj02")
                    URL += "&ret_coordtype=gcj02ll";

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        if (jo.SelectToken("..lat") != null && jo.SelectToken("..lng") != null)
                        {
                            Result = string.Format("{0},{1}", jo.SelectToken("..lat"), jo.SelectToken("..lng"));
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = "[Error]Code:" + jo["status"];
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetCoordByAddressQQ(string MapCodeServer, string Address, string City = "")
        {
            string Result;

            try
            {
                string URL = "https://apis.map.qq.com/ws/geocoder/v1/?address={0}&region={1}&key={2}";

                URL = String.Format(URL, Address, City, MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        if (jo.SelectToken("..lat") != null && jo.SelectToken("..lng") != null)
                        {
                            Result = string.Format("{0},{1}", jo.SelectToken("..lat"), jo.SelectToken("..lng"));

                            if (jo.SelectToken("..province") != null)
                            {
                                Result = $"{Result},{jo.SelectToken("..province")}";
                            }

                            if (jo.SelectToken("..city") != null)
                            {
                                Result = $"{Result},{jo.SelectToken("..city")}";
                            }

                            if (jo.SelectToken("..district") != null)
                            {
                                Result = $"{Result},{jo.SelectToken("..district")}";
                            }

                            if (jo.SelectToken("..street") != null)
                            {
                                Result = $"{Result},{jo.SelectToken("..street")}";
                            }
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetCoordByAddressA(string MapCodeServer, string Address, string City = "")
        {
            string Result;

            try
            {
                string URL = "https://restapi.amap.com/v3/geocode/geo?address={0}&city={1}&key={2}";

                URL = String.Format(URL, Address, City, MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 1)
                    {
                        if (jo.SelectToken("..location") != null)
                        {
                            Result = RevCoord(jo.SelectToken("..location").ToString());
                        }
                        else
                        {
                            Result = "[Error]Invalid Coord";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["info"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        #endregion 根据地址获取坐标(百度/腾讯/高德)

        #region 根据坐标获取地址(百度/腾讯/高德)

        /// <summary>
        /// 根据坐标获取地址
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="Coord">查询的坐标（纬度,经度）</param>
        /// <param name="ReturnFormat">返回格式（以address/remark/country/province/city/district/street替换内容，默认为"address,remark"，比如"country-province-city"）</param>
        /// <param name="CoordType">输入的坐标系（bd09/gcj02/wgs84，为空表示地图提供商的默认坐标系，腾讯只支持默认坐标系，高德只支持默认坐标系）</param>
        /// <returns>格式化的地址信息</returns>
        public static string GetAddressByCoord(string MapProvider, string MapCodeServer, string Coord, string ReturnFormat = "address,remark", string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetAddressByCoordBaidu(MapCodeServer, Coord, ReturnFormat, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetAddressByCoordQQ(MapCodeServer, Coord, ReturnFormat);
            }
            else if (MapProvider == "a")
            {
                return GetAddressByCoordA(MapCodeServer, Coord, ReturnFormat);
            }

            return "[Error]Invalid Map Provider";
        }

        private static string GetAddressByCoordBaidu(string MapCodeServer, string Coord, string ReturnFormat = "address,remark", string CoordType = "")
        {
            string Result;

            if (string.IsNullOrEmpty(ReturnFormat)) return "";

            try
            {
                string URL = "https://api.map.baidu.com/reverse_geocoding/v3/?location={0}&output=json&ak={1}";

                URL = String.Format(URL, Coord.Trim(), MapCodeServer);

                if (CoordType == "gcj02")
                    URL += "&coordtype=gcj02ll";
                else if (CoordType == "wgs84")
                    URL += "&coordtype=wgs84ll";

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = ReturnFormat;

                        if (Result.Contains("address") && jo.SelectToken("..formatted_address") != null)
                            Result = Result.Replace("address", jo.SelectToken("..formatted_address").ToString());

                        if (Result.Contains("remark") && jo.SelectToken("..business") != null)
                            Result = Result.Replace("remark", jo.SelectToken("..business").ToString());

                        if (Result.Contains("country") && jo.SelectToken("..addressComponent.country") != null)
                            Result = Result.Replace("country", jo.SelectToken("..addressComponent.country").ToString());

                        if (Result.Contains("province") && jo.SelectToken("..addressComponent.province") != null)
                            Result = Result.Replace("province", jo.SelectToken("..addressComponent.province").ToString());

                        if (Result.Contains("city") && jo.SelectToken("..addressComponent.city") != null)
                            Result = Result.Replace("city", jo.SelectToken("..addressComponent.city").ToString());

                        if (Result.Contains("district") && jo.SelectToken("..addressComponent.district") != null)
                            Result = Result.Replace("district", jo.SelectToken("..addressComponent.district").ToString());

                        if (Result.Contains("street") && jo.SelectToken("..addressComponent.street") != null)
                            Result = Result.Replace("street", jo.SelectToken("..addressComponent.street").ToString());
                    }
                    else
                    {
                        Result = "[Error]Code:" + jo["status"];
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetAddressByCoordQQ(string MapCodeServer, string Coord, string ReturnFormat = "address,remark")
        {
            string Result;

            if (string.IsNullOrEmpty(ReturnFormat)) return "";

            try
            {
                string URL = "https://apis.map.qq.com/ws/geocoder/v1/?location={0}&key={1}";

                URL = String.Format(URL, Coord.Trim(), MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = ReturnFormat;

                        if (Result.Contains("address") && jo.SelectToken("..address") != null)
                            Result = Result.Replace("address", jo.SelectToken("..address").ToString());

                        if (Result.Contains("remark") && jo.SelectToken("...recommend") != null)
                            Result = Result.Replace("remark", jo.SelectToken("..recommend").ToString());

                        if (Result.Contains("country") && jo.SelectToken("..address_component.nation") != null)
                            Result = Result.Replace("country", jo.SelectToken("..address_component.nation").ToString());

                        if (Result.Contains("province") && jo.SelectToken("..address_component.province") != null)
                            Result = Result.Replace("province", jo.SelectToken("..address_component.province").ToString());

                        if (Result.Contains("city") && jo.SelectToken("..address_component.city") != null)
                            Result = Result.Replace("city", jo.SelectToken("..address_component.city").ToString());

                        if (Result.Contains("district") && jo.SelectToken("..address_component.district") != null)
                            Result = Result.Replace("district", jo.SelectToken("..address_component.district").ToString());

                        if (Result.Contains("street") && jo.SelectToken("..address_component.street") != null)
                            Result = Result.Replace("street", jo.SelectToken("..address_component.street").ToString());
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetAddressByCoordA(string MapCodeServer, string Coord, string ReturnFormat = "address,remark", string CoordType = "")
        {
            string Result;

            if (string.IsNullOrEmpty(ReturnFormat)) return "";

            try
            {
                string URL = "https://restapi.amap.com/v3/geocode/regeo?location={0}&key={1}";

                URL = String.Format(URL, RevCoord(Coord.Trim()), MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 1)
                    {
                        Result = ReturnFormat;

                        if (Result.Contains("address") && jo.SelectToken("..formatted_address") != null)
                            Result = Result.Replace("address", jo.SelectToken("..formatted_address").ToString());

                        if (Result.Contains("remark") && jo.SelectToken("..building.name") != null)
                            Result = Result.Replace("remark", jo.SelectToken("..building.name").ToString());

                        if (Result.Contains("country") && jo.SelectToken("..addressComponent.country") != null)
                            Result = Result.Replace("country", jo.SelectToken("..addressComponent.country").ToString());

                        if (Result.Contains("province") && jo.SelectToken("..addressComponent.province") != null)
                            Result = Result.Replace("province", jo.SelectToken("..addressComponent.province").ToString());

                        if (Result.Contains("city") && jo.SelectToken("..addressComponent.city") != null)
                            Result = Result.Replace("city", jo.SelectToken("..addressComponent.city").ToString());

                        if (Result.Contains("district") && jo.SelectToken("..addressComponent.district") != null)
                            Result = Result.Replace("district", jo.SelectToken("..addressComponent.district").ToString());

                        if (Result.Contains("street") && jo.SelectToken("..addressComponent.township") != null)
                            Result = Result.Replace("street", jo.SelectToken("..addressComponent.township").ToString());
                    }
                    else
                    {
                        Result = "[Error]Code:" + jo["status"];
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["info"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        #endregion 根据坐标获取地址(百度/腾讯/高德)

        #region 根据坐标获取静态地图URL(百度/腾讯/高德)

        /// <summary>
        /// 根据坐标获取静态地图URL
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="MapCodeClient">地图机构端访问码</param>
        /// <param name="Coord">查询的坐标（纬度,经度）</param>
        /// <param name="Width">图片宽度</param>
        /// <param name="Height">图片高度</param>
        /// <param name="Zoom">放大级别</param>
        /// <param name="CoordType">输入的坐标系（bd09/gcj02/wgs84，为空表示地图提供商的默认坐标系，腾讯只支持默认坐标系，高德只支持默认坐标系）</param>
        /// <returns>静态地图的图片URL</returns>
        public static string GetStaticMapByCoord(string MapProvider, string MapCodeServer, string MapCodeClient, string Coord, int Width = 300, int Height = 200, int Zoom = 17, string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetStaticMapByCoordBaidu(MapCodeClient, Coord, Width, Height, Zoom, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetStaticMapByCoordQQ(MapCodeClient, Coord, Width, Height, Zoom);
            }
            else if (MapProvider == "a")
            {
                return GetStaticMapByCoordA(MapCodeServer, Coord, Width, Height, Zoom);
            }
            else
            {
                return "";
            }
        }

        private static string GetStaticMapByCoordBaidu(string MapCodeClient, string Coord, int Width = 300, int Height = 200, int Zoom = 17, string CoordType = "")
        {
            string URL = "https://api.map.baidu.com/staticimage/v2?markers={0}&markerStyles=l&width={1}&height={2}&zoom={3}&ak={4}";

            URL = String.Format(URL, RevCoord(Coord.Trim()), Width, Height, Zoom, MapCodeClient);

            if (CoordType == "gcj02")
                URL += "&coordtype=gcj02ll";
            else if (CoordType == "wgs84")
                URL += "&coordtype=wgs84ll";

            return URL;
        }

        private static string GetStaticMapByCoordQQ(string MapCodeClient, string Coord, int Width = 300, int Height = 200, int Zoom = 17)
        {
            string URL = "https://apis.map.qq.com/ws/staticmap/v2/?markers={0}&size={1}*{2}&zoom={3}&key={4}";

            URL = String.Format(URL, Coord.Trim(), Width, Height, Zoom, MapCodeClient);

            return URL;
        }

        private static string GetStaticMapByCoordA(string MapCodeServer, string Coord, int Width = 300, int Height = 200, int Zoom = 17)
        {
            string URL = "https://restapi.amap.com/v3/staticmap?markers=mid,0xFF0000,A:{0}&size={1}*{2}&zoom={3}&key={4}";

            URL = String.Format(URL, RevCoord(Coord.Trim()), Width, Height, Zoom, MapCodeServer);

            return URL;
        }

        #endregion 根据坐标获取静态地图URL(百度/腾讯/高德)

        #region 根据坐标获取动态(网页)地图URL(百度/腾讯/高德)

        /// <summary>
        /// 根据坐标获取动态(网页)地图URL
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="MapCodeClient">地图机构端访问码</param>
        /// <param name="Coord">查询的坐标（纬度,经度）</param>
        /// <param name="CoordType">输入的坐标系（bd09/gcj02/wgs84，为空表示地图提供商的默认坐标系，腾讯只支持gcj02/wgs84，高德只支持gcj02/wgs84）</param>
        /// <returns>动态地图的网页URL</returns>
        public static string GetDynamicMapUrlByCoord(string MapProvider, string MapCodeServer, string MapCodeClient, string Coord, string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetDynamicMapUrlByCoordBaidu(MapCodeClient, Coord, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetDynamicMapUrlByCoordQQ(MapCodeClient, Coord, CoordType);
            }
            else if (MapProvider == "a")
            {
                return GetDynamicMapUrlByCoordA(MapCodeServer, Coord, CoordType);
            }

            return "";
        }

        private static string GetDynamicMapUrlByCoordBaidu(string MapCodeClient, string Coord, string CoordType = "")
        {
            string URL = "https://api.map.baidu.com/geocoder?location={0}&output=html&src={1}";

            URL = String.Format(URL, Coord.Trim(), "webapp.shinsoft." + Guid.NewGuid().ToString());

            if (CoordType == "gcj02")
                URL += "&coord_type=gcj02";
            else if (CoordType == "wgs84")
                URL += "&coord_type=wgs84";

            return URL;
        }

        private static string GetDynamicMapUrlByCoordQQ(string MapCodeClient, string Coord, string CoordType = "")
        {
            string URL = "https://apis.map.qq.com/uri/v1/marker?marker=coord:{0}{1}&referer=shinsoft";

            URL = String.Format(URL, Coord.Trim(), GetAddressByCoordQQ(MapCodeClient, Coord.Trim(), ";title:remark;addr:address"));

            if (CoordType == "wgs84")
                URL += "&coord_type=1";

            return URL;
        }

        private static string GetDynamicMapUrlByCoordA(string MapCodeServer, string Coord, string CoordType = "")
        {
            string URL = "https://uri.amap.com/marker?position={0}&name={1}&src=shinsoft";

            URL = String.Format(URL, RevCoord(Coord.Trim()), HttpUtility.UrlEncode(GetAddressByCoordA(MapCodeServer, Coord.Trim(), "remark(address)")));

            if (CoordType == "wgs84")
                URL += "&coordinate=wgs84";

            return URL;
        }

        #endregion 根据坐标获取动态(网页)地图URL(百度/腾讯/高德)

        #region 获取导航地图URL(百度/腾讯/高德)

        /// <summary>
        /// 获取导航地图URL
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeClient">地图机构端访问码</param>
        /// <param name="Platform">使用平台（Web/Android/iOS）</param>
        /// <param name="Departure">出发地（坐标@名称/坐标/@名称）</param>
        /// <param name="DepartureCity">出发地所在城市（仅百度需要）</param>
        /// <param name="Destination">目的地（坐标@名称/坐标/@名称）</param>
        /// <param name="DestinationCity">目的地所在城市（仅百度需要）</param>
        /// <param name="WayPoint">途经地（坐标1@名称1|坐标2@名称2，百度手机端驾车支持3个+骑车/步行支持1个、腾讯不支持、高德驾车/骑车支持1个）</param>
        /// <param name="WayOptimize">是否优化途经地顺序</param>
        /// <param name="Vehicle">交通工具：driving(驾车)/transit(公交)/riding(骑车)/walking(步行)</param>
        /// <param name="CoordType">输入的坐标系（bd09/gcj02/wgs84，为空表示地图提供商的默认坐标系，腾讯只支持gcj02/wgs84，高德只支持gcj02/wgs84）</param>
        /// <returns>导航地图的URL</returns>
        public static string GetNavigationMapUrl(string MapProvider, string MapCodeClient, string Platform, string Departure, string DepartureCity = "", string Destination = "", string DestinationCity = "", string WayPoint = "", bool WayOptimize = false, string Vehicle = "driving", string CoordType = "")
        {
            if (MapProvider.ToLower() == "baidu")
            {
                return GetNavigationMapUrlBaidu(MapCodeClient, Platform, Departure, DepartureCity, Destination, DestinationCity, WayPoint, WayOptimize, Vehicle, CoordType);
            }
            else if (MapProvider.ToLower() == "qq")
            {
                string qqVehicle = Vehicle
                    .Replace("driving", "drive")
                    .Replace("transit", "bus")
                    .Replace("walking", "walk")
                    .Replace("riding", "bike");

                return GetNavigationMapUrlQQ(MapCodeClient, Platform, Departure, Destination, qqVehicle, CoordType);
            }
            else if (MapProvider.ToLower() == "a")
            {
                string aVehicle = Vehicle
                    .Replace("driving", "car")
                    .Replace("transit", "bus")
                    .Replace("walking", "walk")
                    .Replace("riding", "ride");

                return GetNavigationMapUrlA(MapCodeClient, Platform, Departure, Destination, WayPoint, aVehicle, CoordType);
            }

            return "";
        }

        private static string GetNavigationMapUrlBaidu(string MapCodeClient, string Platform, string Departure, string DepartureCity = "", string Destination = "", string DestinationCity = "", string WayPoint = "", bool WayOptimize = false, string Vehicle = "driving", string CoordType = "")
        {
            string URL;

            string urlDeparture = "";
            string urlDestination = "";
            string urlWayPoint = "";

            Dictionary<string, string> dictWayPoint = new Dictionary<string, string>();

            switch (Platform.ToLower())
            {
                case "web":
                    URL = "https://api.map.baidu.com/direction?mode={0}&origin={1}&origin_region={2}&destination={3}&destination_region={4}&viaPoints={5}&output=html&src={6}";
                    break;

                case "android":
                    URL = "bdapp://map/direction?mode={0}&origin={1}&origin_region={2}&destination={3}&destination_region={4}&viaPoints={5}&src={6}";
                    break;

                case "ios":
                    URL = "baidumap://map/direction?mode={0}&origin={1}&origin_region={2}&destination={3}&destination_region={4}&viaPoints={5}&src={6}";
                    break;

                default:
                    return "";
            }

            if (CoordType == "gcj02")
                URL += "&coord_type=gcj02";
            else if (CoordType == "wgs84")
                URL += "&coord_type=wgs84";
            else
                URL += "&coord_type=bd09ll";

            if (!string.IsNullOrEmpty(WayPoint))
            {
                dictWayPoint = WayPoint.Split('|').ToDictionary(x => x.Split('@')[0], x => x.Split('@').Length > 1 ? x.Split('@')[1] : "");

                if (WayOptimize)
                {
                    var OptWayPoint = OptimizeWayCoord(Departure.Split('@')[0], string.Join("|", dictWayPoint.Select(x => x.Key)));

                    dictWayPoint = OptWayPoint.Split('|').ToDictionary(x => x, x => dictWayPoint[x]);
                }
            }

            urlDeparture = "latlng:" + Departure.Replace("@", "|name:");

            if (string.IsNullOrEmpty(Destination))
            {
                if (dictWayPoint.Count > 0)
                {
                    urlDestination = "latlng:" + dictWayPoint.Last().Key + "|name:" + dictWayPoint.Last().Value;
                    dictWayPoint.Remove(dictWayPoint.Last().Key);
                }
                else
                {
                    return "";
                }
            }
            else
            {
                urlDestination = "latlng:" + Destination.Replace("@", "|name:");
            }

            urlWayPoint = "{\"viaPoints\":[" + string.Join(",", dictWayPoint.Select(x => "{\"name\":\"" + x.Value + "\",\"lat\":" + x.Key.Replace(",", ",\"lng\":") + "}")) + "]}";

            URL = String.Format(URL, Vehicle, urlDeparture, DepartureCity, urlDestination, DestinationCity, urlWayPoint, "webapp.shinsoft." + Guid.NewGuid().ToString());

            return URL;
        }

        private static string GetNavigationMapUrlQQ(string MapCodeClient, string Platform, string Departure, string Destination, string Vehicle = "drive", string CoordType = "")
        {
            string URL;

            switch (Platform.ToLower())
            {
                case "web":
                    URL = "https://apis.map.qq.com/uri/v1/routeplan?type={0}&fromcoord={1}&from={2}&tocoord={3}&to={4}&referer=shinsoft";
                    break;

                case "android":
                case "ios":
                    URL = "qqmap://map/routeplan?type={0}&fromcoord={1}&from={2}&tocoord={3}&to={4}&referer=shinsoft";
                    break;

                default:
                    return "";
            }

            if (CoordType == "wgs84")
                URL += "&coord_type=1";

            URL = String.Format(URL, Vehicle, Departure.Split('@')[0], Departure.Split('@').Length > 1 ? Departure.Split('@')[1] : "", Destination.Split('@')[0], Destination.Split('@').Length > 1 ? Destination.Split('@')[1] : "");

            return URL;
        }

        private static string GetNavigationMapUrlA(string MapCodeClient, string Platform, string Departure, string Destination, string WayPoint = "", string Vehicle = "car", string CoordType = "")
        {
            string URL = "https://uri.amap.com/navigation?from={0}&to={1}&via={2}&mode={3}&src=shinsoft";

            URL = String.Format(URL,
                    RevCoord(Departure.Split('@')[0]) + (Departure.Split('@').Length > 1 ? "," + HttpUtility.UrlEncode(Departure.Split('@')[1]) : ""),
                    RevCoord(Destination.Split('@')[0]) + (Destination.Split('@').Length > 1 ? "," + HttpUtility.UrlEncode(Destination.Split('@')[1]) : ""),
                    RevCoord(WayPoint.Split('|')[0].Split('@')[0]) + (WayPoint.Split('|')[0].Split('@').Length > 1 ? "," + HttpUtility.UrlEncode(WayPoint.Split('|')[0].Split('@')[1]) : ""),
                    Vehicle);

            if (CoordType == "wgs84")
                URL += "&coordinate=wgs84";

            if (Platform.ToLower() != "web")
                URL += "&callnative=1";

            return URL;
        }

        #endregion 获取导航地图URL(百度/腾讯/高德)

        #region 获取坐标点间路线规划(百度/腾讯/高德)

        /// <summary>
        /// 获取坐标点间路线规划
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="FromCoord">起点坐标（纬度,经度）</param>
        /// <param name="ToCoord">终点坐标（纬度,经度）</param>
        /// <param name="WayCoord">途经点坐标（坐标1|坐标2|坐标3，百度≤18，腾讯≤16，高德≤16）</param>
        /// <param name="Vehicle">交通工具：driving(驾车)/motorcycle(摩托，仅百度支持且需单独申请)/transit(公交)/riding(骑车)/walking(步行，仅腾讯/高德支持)</param>
        /// <param name="CoordType">输入的坐标系（bd09/gcj02/wgs84，为空表示地图提供商的默认坐标系，腾讯只支持默认坐标系，高德只支持默认坐标系）</param>
        /// <returns>路线规划描述</returns>
        public static string GetRoute(string MapProvider, string MapCodeServer, string FromCoord, string ToCoord, string WayCoord = "", string Vehicle = "driving", string CoordType = "")
        {
            if (MapProvider == "baidu")
            {
                return GetRouteBaidu(MapCodeServer, FromCoord, ToCoord, WayCoord, Vehicle, CoordType);
            }
            else if (MapProvider == "qq")
            {
                return GetRouteQQ(MapCodeServer, FromCoord, ToCoord, WayCoord.Replace('|', ';'), Vehicle.Replace("riding", "bicycling"));
            }
            else if (MapProvider == "a")
            {
                string aVehicle = Vehicle
                                .Replace("transit", "transit/integrated")
                                .Replace("riding", "bicycling");

                return GetRouteA(MapCodeServer, FromCoord, ToCoord, WayCoord, aVehicle);
            }

            return "[Error]Invalid Map Provider";
        }

        private static string GetRouteBaidu(string MapCodeServer, string FromCoord, string ToCoord, string WayCoord = "", string Vehicle = "driving", string CoordType = "")
        {
            string Result;

            try
            {
                string URL = "https://api.map.baidu.com/direction/v2/{0}?origin={1}&destination={2}&waypoints={3}&ak={4}";

                URL = String.Format(URL, Vehicle, FromCoord.Trim(), ToCoord.Trim(), WayCoord.Trim(), MapCodeServer);

                if (CoordType == "gcj02")
                    URL += "&coord_type=gcj02";
                else if (CoordType == "wgs84")
                    URL += "&coord_type=wgs84";

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = "路线长度:" + (string)jo.SelectToken("..routes[0].distance") + "米";
                        Result += ",预计时长:" + (int)jo.SelectToken("..routes[0].duration") / 60 + "分";

                        if (Vehicle == "driving" || Vehicle == "motorcycle")
                        {
                            Result += ",路线信息:起点-";

                            foreach (var step in jo.SelectToken("..routes[0].steps"))
                            {
                                Result += step["road_name"] + " 行驶" + step["distance"] + "米-";
                            }

                            Result += "终点";
                        }
                        else if (Vehicle == "transit")
                        {
                            Result += ",路线信息:起点-";

                            foreach (var step in jo.SelectToken("..routes[0].steps"))
                            {
                                if (step[0].SelectToken("vehicle_info..name") != null)
                                {
                                    Result += step.SelectToken("..name") + " ";
                                }

                                Result += step[0]["instructions"] + "-";
                            }

                            Result += "终点";

                            if (jo.SelectToken("..taxi.remark") != null)
                            {
                                Result += ",出租车信息:";

                                if (jo.SelectToken("..taxi.detail[0].total_price") != null)
                                    Result += "费用约" + jo.SelectToken("..taxi.detail[0].total_price") + "元;";

                                Result += jo.SelectToken("..taxi.remark");
                            }
                        }
                        else if (Vehicle == "riding")
                        {
                            Result += ",路线信息:起点-";

                            foreach (var step in jo.SelectToken("..routes[0].steps"))
                            {
                                Result += string.IsNullOrEmpty((string)step["name"]) ? "当前道路" : step["name"];

                                Result += " 骑行" + step["distance"] + "米";

                                if (!string.IsNullOrEmpty((string)step["turn_type"]))
                                    Result += " " + step["turn_type"];

                                Result += "-";
                            }

                            Result += "终点";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetRouteQQ(string MapCodeServer, string FromCoord, string ToCoord, string WayCoord = "", string Vehicle = "driving")
        {
            string Result;

            try
            {
                string URL = "https://apis.map.qq.com/ws/direction/v1/{0}/?from={1}&to={2}&waypoints={3}&key={4}";

                URL = String.Format(URL, Vehicle, FromCoord.Trim(), ToCoord.Trim(), WayCoord.Trim(), MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = "路线长度:" + jo.SelectToken("result.routes[0].distance").ToString() + "米";
                        Result += ",预计时长:" + jo.SelectToken("result.routes[0].duration").ToString() + "分";

                        if (Vehicle == "driving" || Vehicle == "walking" || Vehicle == "bicycling")
                        {
                            Result += ",路线信息:起点-";

                            foreach (var node in jo.SelectToken("result.routes[0].steps"))
                            {
                                Result += node["instruction"].ToString() + "-";
                            }

                            Result += "终点";
                        }
                        else if (Vehicle == "transit")
                        {
                            Result += ",路线信息:起点-";

                            foreach (var node in jo.SelectToken("result.routes[0].steps"))
                            {
                                if (node["mode"].ToString() == "WALKING")
                                {
                                    foreach (var step in node.SelectToken("steps"))
                                    {
                                        Result += "[步行]" + step["instruction"] + "-";
                                    }
                                }
                                else if (node["mode"].ToString() == "TRANSIT")
                                {
                                    foreach (var line in node.SelectToken("lines"))
                                    {
                                        string lineVehicle = line["vehicle"].ToString();

                                        if (lineVehicle == "BUS")
                                            Result += "[公交]";
                                        else if (lineVehicle == "SUBWAY")
                                            Result += "[地铁]";
                                        else if (lineVehicle == "RAIL")
                                            Result += "[火车]";

                                        Result += line["title"];
                                        Result += " [上车]" + line.SelectToken("geton.title") + (line.SelectToken("geton.exit.title") == null ? "" : line.SelectToken("geton.exit.title"));
                                        Result += " [下车]" + line.SelectToken("getoff.title") + (line.SelectToken("getoff.exit.title") == null ? "" : line.SelectToken("getoff.exit.title"));
                                        Result += "-";
                                    }
                                }
                            }

                            Result += "终点";
                        }
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string GetRouteA(string MapCodeServer, string FromCoord, string ToCoord, string WayCoord = "", string Vehicle = "driving")
        {
            string Result;

            try
            {
                string URL = "https://restapi.amap.com/v3/direction/{0}?origin={1}&destination={2}&key={3}";

                URL = String.Format(URL, Vehicle, RevCoord(FromCoord.Trim()), RevCoord(ToCoord.Trim()), MapCodeServer);

                if (Vehicle == "transit/integrated")
                {
                    URL += "&city=" + GetAddressByCoordA(MapCodeServer, RevCoord(FromCoord.Trim()), "city");
                }
                else if (Vehicle == "driving")
                {
                    URL += "&waypoints=" + string.Join(";", WayCoord.Split('|').Select(x => RevCoord(x)));
                }
                else if (Vehicle == "bicycling")
                {
                    URL = URL.Replace("/v3/", "/v4/");
                }

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (Vehicle == "bicycling")
                {
                    if (jo["errcode"] != null)
                    {
                        if ((int)jo["errcode"] == 0)
                        {
                            Result = "路线长度:" + jo.SelectToken("data.paths[0].distance").ToString() + "米";
                            Result += ",预计时长:" + ((int)jo.SelectToken("data.paths[0].duration") / 60).ToString() + "分";

                            Result += ",路线信息:起点-";

                            foreach (var node in jo.SelectToken("data.paths[0].steps"))
                            {
                                Result += node["instruction"].ToString() + "-";
                            }

                            Result += "终点";
                        }
                        else
                        {
                            Result = string.Format("[Error]Status:{0}, Message:{1}", jo["errcode"], jo["errdetail"]);
                        }
                    }
                }
                else
                {
                    if (jo["status"] != null)
                    {
                        if ((int)jo["status"] == 1)
                        {
                            if (Vehicle == "driving" || Vehicle == "walking")
                            {
                                Result = "路线长度:" + jo.SelectToken("route.paths[0].distance").ToString() + "米";
                                Result += ",预计时长:" + ((int)jo.SelectToken("route.paths[0].duration") / 60).ToString() + "分";

                                Result += ",路线信息:起点-";

                                foreach (var node in jo.SelectToken("route.paths[0].steps"))
                                {
                                    Result += node["instruction"].ToString() + "-";
                                }

                                Result += "终点";
                            }
                            else if (Vehicle == "transit/integrated")
                            {
                                Result = "路线长度:" + jo.SelectToken("route.transits[0].distance").ToString() + "米";
                                Result += ",预计时长:" + ((int)jo.SelectToken("route.transits[0].duration") / 60).ToString() + "分";
                                Result += ",预计费用:" + jo.SelectToken("route.transits[0].cost").ToString() + "元";
                                Result += ",出租车费用约:" + jo.SelectToken("route.taxi_cost").ToString() + "元";

                                Result += ",路线信息:起点-";

                                foreach (var node in jo.SelectToken("route.transits[0].segments"))
                                {
                                    foreach (var step in node.SelectToken("walking.steps"))
                                    {
                                        Result += "[步行]" + step["instruction"] + "-";
                                    }

                                    foreach (var busline in node.SelectToken("bus.buslines"))
                                    {
                                        Result += "[" + busline["type"].ToString().Substring(0, 2) + "]" + busline["name"];
                                        Result += " [上车]" + busline["departure_stop"]["name"];
                                        Result += " [下车]" + busline["arrival_stop"]["name"];
                                        Result += "-";
                                    }
                                }

                                Result += "终点";
                            }
                        }
                        else
                        {
                            Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["info"]);
                        }
                    }
                    else
                    {
                        Result = "[Error]" + Result;
                    }
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        #endregion 获取坐标点间路线规划(百度/腾讯/高德)

        #region 获取坐标点间直连距离(与地图提供商无关)

        private const double EARTH_RADIUS = 6371393;//地球平均半径

        private static double rad(double d)
        {
            return d * Math.PI / 180.0;
        }

        /// <summary>
        /// 获取坐标点间直连距离（地球表面最短距离）
        /// </summary>
        /// <param name="Coord1">查询的坐标1（纬度,经度）</param>
        /// <param name="Coord2">查询的坐标2（纬度,经度）</param>
        /// <returns>计算出来的距离（单位：米）</returns>
        public static double GetDistance(string Coord1, string Coord2)
        {
            int iIndex1 = Coord1.IndexOf(',');
            int iIndex2 = Coord2.IndexOf(',');

            if (iIndex1 < 0 || iIndex2 < 0)
                return 0;

            double radLat1 = rad(double.Parse(Coord1.Substring(0, iIndex1)));
            double radLat2 = rad(double.Parse(Coord2.Substring(0, iIndex2)));
            double a = radLat1 - radLat2;
            double b = rad(double.Parse(Coord1.Substring(iIndex1 + 1))) - rad(double.Parse(Coord2.Substring(iIndex2 + 1)));

            double s = 2 * Math.Asin(Math.Sqrt(Math.Pow(Math.Sin(a / 2), 2) + Math.Cos(radLat1) * Math.Cos(radLat2) * Math.Pow(Math.Sin(b / 2), 2))) * 6371393;

            return Math.Round(s, 3);
        }

        #endregion 获取坐标点间直连距离(与地图提供商无关)

        #region 途经点顺序优化(与地图提供商无关)

        /// <summary>
        /// 途经点顺序优化
        /// </summary>
        /// <param name="FromCoord">起点坐标（纬度,经度）</param>
        /// <param name="WayCoord">途经点坐标（坐标1|坐标2|坐标3）</param>
        /// <returns>优化顺序后的途经点（坐标2|坐标3|坐标1）</returns>
        public static string OptimizeWayCoord(string FromCoord, string WayCoord)
        {
            try
            {
                double latFromCoord = double.Parse(FromCoord.Split(',')[0]);
                double lngFromCoord = double.Parse(FromCoord.Split(',')[1]);

                Dictionary<string, double> dictCoord = new Dictionary<string, double>();

                dictCoord = WayCoord.Split('|').ToDictionary(x => x, x => Math.Abs(latFromCoord - double.Parse(x.Split(',')[0])) + Math.Abs(lngFromCoord - double.Parse(x.Split(',')[1])));

                return string.Join("|", dictCoord.OrderBy(x => x.Value).Select(x => x.Key));
            }
            catch
            {
                return WayCoord;
            }
        }

        #endregion 途经点顺序优化(与地图提供商无关)

        #region 发送HTTP请求(与地图提供商无关)

        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="url">请求地址</param>
        /// <param name="postData">请求数据，格式为：key1=value1&key2=value2&key3=value3</param>
        /// <returns></returns>
        private static string HttpPost(string url, string postData)
        {
            string result = "";

            try
            {
                ServicePointManager.Expect100Continue = false;
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.ServicePoint.Expect100Continue = false;
                request.Method = "POST";

                if (!string.IsNullOrEmpty(postData))
                {
                    var requestStream = request.GetRequestStream();
                    var streamWriter = new StreamWriter(requestStream, Encoding.UTF8);
                    streamWriter.Write(postData);
                    streamWriter.Flush();
                }

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                var responseStream = response.GetResponseStream();
                var streamReader = new StreamReader(responseStream, Encoding.UTF8);
                result = streamReader.ReadToEnd();
            }
            catch (Exception e)
            {
                return e.Message;
            }

            return result;
        }

        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="url">请求地址</param>
        /// <returns></returns>
        private static string HttpGet(string url)
        {
            string result = "";

            try
            {
                ServicePointManager.Expect100Continue = false;
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.ServicePoint.Expect100Continue = false;
                request.Method = "GET";

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                var responseStream = response.GetResponseStream();
                var streamReader = new StreamReader(responseStream, Encoding.UTF8);
                result = streamReader.ReadToEnd();
            }
            catch (Exception e)
            {
                return e.Message;
            }

            return result;
        }

        #endregion 发送HTTP请求(与地图提供商无关)

        #region 坐标系转换为地图提供商默认坐标系(百度/腾讯/高德)

        /// <summary>
        /// 坐标系转换为地图提供商默认坐标系（百度:bd09，腾讯:gcj02，高德:gcj02）
        /// </summary>
        /// <param name="MapProvider">地图提供商</param>
        /// <param name="MapCodeServer">地图服务端访问码</param>
        /// <param name="Coords">需转换的坐标（坐标1|坐标2|坐标3，百度≤100，腾讯≤HTTP GET上限，高德≤40）</param>
        /// <param name="CoordType">转换前坐标系（gcj02/bd09/wgs84）</param>
        /// <returns>转换后的坐标（坐标1|坐标2|坐标3）</returns>
        public static string ConvertCoords(string MapProvider, string MapCodeServer, string Coords, string CoordType)
        {
            if (!"|gcj02|bd09|wgs84|".Contains("|" + CoordType + "|"))
            {
                return "[Error]Invalid Coord Type";
            }

            if (MapProvider == "baidu")
            {
                if (CoordType == "bd09")
                {
                    return Coords;
                }

                return ConvertCoordsBaidu(MapCodeServer, Coords, CoordType);
            }
            else if (MapProvider == "qq")
            {
                if (CoordType == "gcj02")
                {
                    return Coords;
                }

                return ConvertCoordsQQ(MapCodeServer, Coords, CoordType);
            }
            else if (MapProvider == "a")
            {
                if (CoordType == "gcj02")
                {
                    return Coords;
                }

                return ConvertCoordsA(MapCodeServer, Coords, CoordType);
            }

            return "[Error]Invalid Map Provider";
        }

        private static string ConvertCoordsBaidu(string MapCodeServer, string Coords, string CoordType)
        {
            string Result;

            try
            {
                string URL = "https://api.map.baidu.com/geoconv/v1/?coords={0}&from={1}&ak={2}";

                URL = String.Format(URL, String.Join(";", Coords.Split('|').Select(x => RevCoord(x))), CoordType == "gcj02" ? 3 : 1, MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = String.Join("|", jo["result"].Select(x => String.Format("{0},{1}", x["y"], x["x"])));
                    }
                    else
                    {
                        Result = String.Format("[Error]Status:{0}", jo["status"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string ConvertCoordsQQ(string MapCodeServer, string Coords, string CoordType)
        {
            string Result;

            try
            {
                string URL = "https://apis.map.qq.com/ws/coord/v1/translate?locations={0}&type={1}&key={2}";

                URL = String.Format(URL, Coords.Replace('|', ';'), CoordType == "bd09" ? 3 : 1, MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 0)
                    {
                        Result = String.Join("|", jo["locations"].Select(x => String.Format("{0},{1}", x["lat"], x["lng"])));
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["message"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        private static string ConvertCoordsA(string MapCodeServer, string Coords, string CoordType)
        {
            string Result;

            try
            {
                string URL = "https://restapi.amap.com/v3/assistant/coordinate/convert?locations={0}&coordsys={1}&key={2}";

                URL = String.Format(URL, String.Join("|", Coords.Split('|').Select(x => RevCoord(x))), CoordType == "bd09" ? "baidu" : "gps", MapCodeServer);

                Result = HttpGet(URL);

                JObject jo = JObject.Parse(Result);

                if (jo["status"] != null)
                {
                    if ((int)jo["status"] == 1)
                    {
                        Result = String.Join("|", jo["locations"].ToString().Split('|').Select(x => RevCoord(x)));
                    }
                    else
                    {
                        Result = string.Format("[Error]Status:{0}, Message:{1}", jo["status"], jo["info"]);
                    }
                }
                else
                {
                    Result = "[Error]" + Result;
                }
            }
            catch (Exception e)
            {
                Result = "[Error]" + e.Message;
            }

            return Result;
        }

        #endregion 坐标系转换为地图提供商默认坐标系(百度/腾讯/高德)

        #region 经纬度反转(与地图提供商无关)

        private static string RevCoord(string Coord)
        {
            int iIndex = Coord.IndexOf(',');

            if (iIndex > 0)
                return Coord.Substring(iIndex + 1) + "," + Coord.Substring(0, iIndex);
            else
                return Coord;
        }

        #endregion 经纬度反转(与地图提供商无关)
    }
}

#pragma warning restore CS8600 // 将 null 字面量或可能为 null 的值转换为非 null 类型。
#pragma warning restore CS8602 // 解引用可能出现空引用。
#pragma warning restore CS8604 // 引用类型参数可能为 null。
#pragma warning restore SYSLIB0014 // 类型或成员已过时