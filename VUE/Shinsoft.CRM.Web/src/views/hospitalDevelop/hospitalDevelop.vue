<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="6">
          <el-date-picker
            v-model="listQuery.startDates"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width:100%"
            clearable
          >
          </el-date-picker>
        </el-col>
        <el-col :span="4">
          <el-input v-model="listQuery.keywords" clearable placeholder="标题/医院名称/产品名称" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.enumStatuses"
            class="filter-item"
            placeholder="状态"
            multiple
            collapse-tags
            clearable
          >
            <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>

        <el-col :span="7">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button class="filter-item-button" type="primary" icon="el-icon-plus" @click="handleCreateDevelop">
            新增
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="dataList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :default-sort="{ prop: 'StartDate', order: 'descending' }"
            :header-cell-class-name="'tableStyle'"
            @sort-change="sortChange"
          >
            <el-table-column fixed="left" label="序号" type="index" align="center" :index="indexMethod" />

            <el-table-column
              fixed="left"
              label="标题"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="title"
            >
              <template slot-scope="{row}">
                {{ row.title }}
              </template>
            </el-table-column>

            <el-table-column
              label="医院名称"
              sortable="custom"
              min-width="300px"
              header-align="center"
              align="left"
              prop="HospitalName"
            >
              <template slot-scope="{row}">
                {{ row.hospitalName }}
              </template>
            </el-table-column>
            <el-table-column
              label="产品"
              sortable="custom"
              width="130px"
              header-align="center"
              align="left"
              prop="Product.Name"
            >
              <template slot-scope="{row}">
                {{ row.productName }}
              </template>
            </el-table-column>
            <el-table-column
              label="负责人"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="OwnerEmployee.DisplayName"
            >
              <template slot-scope="{row}">
                {{ row.ownerEmployeeName }}
              </template>
            </el-table-column>
            <el-table-column
              label="开发类型"
              sortable="custom"
              width="120px"
              header-align="center"
              align="left"
              prop="Category.Name"
            >
              <template slot-scope="{row}">
                {{ row.hospitalDevelopCategoryName }}
              </template>
            </el-table-column>
            <el-table-column
              label="当前阶段"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="CurrPhase.Name"
            >
              <template slot-scope="{row}">
                {{ row.hospitalDevelopPhaseName }}
              </template>
            </el-table-column>
            <el-table-column
              label="开始时间"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="StartDate"
            >
              <template slot-scope="{row}">
                {{ row.startDate }}
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="EnumStatus"
            >
              <template slot-scope="{row}">
                {{ row.enumStatusDesc }}
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <el-button type="text" title="详情" @click="handleView(row)">详情</el-button>
                <el-button v-if="row.enumEventStatus === 20" type="text" title="拜访结果" @click="handleViewResult(row)">拜访结果</el-button>

              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <editHospitalDevelop ref="editHospitalDevelop" @refreshData="getList"></editHospitalDevelop>
    <viewHospitalDevelop ref="viewHospitalDevelop"></viewHospitalDevelop>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import hospitalDevelopApi from '@/api/hospitalDevelop'
import selectorApi from '@/api/selector'
import dateUtils from '@/utils/dateUtils'
import editHospitalDevelop from './components/editHospitalDevelop.vue'
import viewHospitalDevelop from '@/views/hospitalDevelop/components/viewHospitalDevelop.vue'
export default {
  name: '',
  components: {
    Pagination,
    editHospitalDevelop,
    viewHospitalDevelop
  },
  data() {
    return {
      span: 4,
      statusList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order:'-StartDate',
        enumStatuses: [1,10],
        startDates: [dateUtils.getThisMonthFirstDay(), dateUtils.getThisMonthLastDay()]
      },
      listLoading: false,
      dataList: [],
      total: 0
    }
  },
  created() {
    this.initTypeList()
    this.getList()
    // this.listQuery.startDates = [dateUtils.getThisMonthFirstDay(), dateUtils.getThisMonthLastDay()]
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'HospitalDevelopStatus' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.statusList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    getList() {
      if (!this.listQuery.startDates || this.listQuery.startDates.length === 0) {
        delete this.listQuery.startDates
      }
      this.listLoading = true
      hospitalDevelopApi
        .queryHospitalDevelop(this.listQuery)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.dataList = result.data.datas
            this.total = result.data.recordCount
            this.listQuery.pageIndex = result.data.pageIndex
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // changeDates(dates) {
    //   this.startDateTime = dates[0]
    //   this.endDateTime = dates[1]
    // },
    handleCreateDevelop() {
      this.$refs.editHospitalDevelop.init()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleView(row) {
      this.$refs.viewHospitalDevelop.init(row)
    },
    handleViewResult(row) {
      this.$refs.historyResult.init(row)
    }
  }
}
</script>
