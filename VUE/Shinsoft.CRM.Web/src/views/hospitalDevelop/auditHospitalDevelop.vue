<template>
  <div class="dialog-center">
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <!-- <el-col :span="4">
          <el-input
            v-model="listQuery.title"
            clearable
            placeholder="标题"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col> -->
        <el-col :span="4">
          <el-input
            v-model="listQuery.keywords"
            clearable
            placeholder="标题/机构名称/产品名称"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />
        </el-col>
        <el-col :span="span">
          <el-select
            v-model="listQuery.reviewStatuses"
            class="filter-item"
            placeholder="状态"
            multiple
            collapse-tags
            clearable
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.desc"
              :value="item.value"
            />
          </el-select>
        </el-col>

        <el-col :span="7">
          <el-button
            class="filter-item-button"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
          <el-button
            v-if="showBatchAuditBtn"
            class="filter-item-button"
            type="primary"
            icon="el-icon-check"
            @click="handleShowBatchAudit"
          >
            批量审批
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table
            v-loading="listLoading"
            :data="dataList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :header-cell-class-name="'tableStyle'"
            @sort-change="sortChange"
            @selection-change="selectAuditData"
          >
            <el-table-column
              v-if="showBatchAuditBtn"
              type="selection"
              width="55"
              :selectable="canSelectTable"
            />
            <el-table-column
              fixed="left"
              label="序号"
              type="index"
              align="center"
              :index="indexMethod"
            />

            <el-table-column
              fixed="left"
              label="标题"
              sortable="custom"
              min-width="200px"
              header-align="center"
              align="left"
              prop="ReviewIndex.ReviewExtInfo.Title"
            >
              <template slot-scope="{row}">
                {{ row.title }}
              </template>
            </el-table-column>

            <el-table-column
              label="医院名称"
              sortable="custom"
              min-width="300px"
              header-align="center"
              align="left"
              prop="ReviewIndex.ReviewExtInfo.Name"
            >
              <template slot-scope="{row}">
                {{ row.hospitalName }}
              </template>
            </el-table-column>
            <el-table-column
              label="产品"
              sortable="custom"
              width="130px"
              header-align="center"
              align="left"
              prop="ReviewIndex.ReviewExtInfo.Product.Name"
            >
              <template slot-scope="{row}">
                {{ row.productName }}
              </template>
            </el-table-column>
            <el-table-column
              label="负责人"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="ReviewIndex.ReviewExtInfo.OwnerEmployee.DisplayName"
            >
              <template slot-scope="{row}">
                {{ row.ownerEmployeeName }}
              </template>
            </el-table-column>
            <el-table-column
              label="提交人"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="ReviewIndex.ReviewExtInfo.OwnerEmployee.DisplayName"
            >
              <template slot-scope="{row}">
                {{ row.submitEmployeeName }}
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              sortable="custom"
              width="120px"
              header-align="center"
              align="center"
              prop="EnumReviewStatus"
            >
              <template slot-scope="{row}">
                {{ row.enumReviewStatusDesc }}
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <el-button type="text" @click="handleView(row)">{{
                  row.enableAudit == 1 ? "审批" : "详情"
                }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageIndex"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </div>
    <audit ref="audit" @refreshData="getList"></audit>
    <el-dialog
      :visible="batchAuditDialog"
      class="hide-border"
      width="25%"
      @close="closeAuditDialog"
    >
      <div class="audit-comtent">
        <el-button type="danger" icon="el-icon-close" @click="submitAudit(20)">
          审批拒绝
        </el-button>
        <el-button type="primary" icon="el-icon-check" @click="submitAudit(10)">
          审批通过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import hospitalDevelopApi from '@/api/hospitalDevelop'
import selectorApi from '@/api/selector'
import audit from './components/audit.vue'
import dateUtils from '@/utils/dateUtils'
export default {
  name: '',
  components: {
    Pagination,
    audit
  },
  data() {
    return {
      span: 4,
      statusList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        dates: [],
        order: 'CreateTime',
        reviewStatuses: [1]
      },
      listLoading: false,
      dataList: [],
      total: 0,
      batchAuditDialog: false,
      batchAuditList: []
    }
  },
  computed: {
    showBatchAuditBtn: function() {
      return this.dataList.some(s => {
        return s.enableAudit
      })
    }
  },
  created() {
    this.initTypeList()
    this.getList()
    // var weekDate = dateUtils.getThisWeekData()
    this.listQuery.dates = [dateUtils.getThisMonthFirstDay(), dateUtils.getThisMonthLastDay()]
  },
  methods: {
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    canSelectTable(row, index) {
      return row.enableAudit
    },
    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'ReviewStatus' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.statusList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    getList() {
      this.listLoading = true
      hospitalDevelopApi
        .queryHospitalDevelopReview(this.listQuery)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.dataList = result.data.datas
            this.total = result.data.recordCount
            this.listQuery.pageIndex = result.data.pageIndex
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleView(row) {
      this.$refs.audit.init(row)
    },
    handleViewResult(row) {
      this.$refs.historyResult.init(row)
    },
    handleShowBatchAudit() {
      if (this.batchAuditList.length === 0) {
        this.$message.error('请先选择要审批的数据')
        return false
      }
      this.batchAuditDialog = true
    },
    selectAuditData(selection) {
      this.batchAuditList = selection
    },
    submitAudit(reviewStatus) {
      var result = reviewStatus === 10 ? '通过' : '拒绝'
      this.$confirm('确认审批' + result, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var reviewids = this.batchAuditList.map(item => { return item.id })
        var params = {
          reviewIds: reviewids,
          enumReviewStatus: reviewStatus
        }
        hospitalDevelopApi.batchAuditHospitalDevelopReview(params).then((res) => {
          if (res) {
            this.$message.success(result + '审批成功')
            this.$emit('refreshData')
            this.closeAuditDialog()
            this.getList()
          }
        })
      }).catch(() => {

      })
    },
    closeAuditDialog() {
      this.batchAuditDialog = false
      this.batchAuditList = []
    }
  }
}
</script>
<style >
.hide-border .el-dialog__header {
  border-bottom: 0px !important;
}
.audit-comtent {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}

.dialog-center .el-dialog {
  margin-top: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
