<template>
  <div>
    <el-form
      ref="dataForm"
      :model="dataModel"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-form-item label="完成状态" prop="enumHospitalDevelopStatus">
        <el-radio-group v-model="dataModel.enumHospitalDevelopStatus" @change="changeStatus">
          <el-radio :label="20">成功</el-radio>
          <el-radio :label="30">失败</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="结果" prop="resultDictId">
        <el-select
          id="resultDictId"
          v-model="dataModel.resultDictId"
          placeholder="请选择结果"
          style="width: 100%"
        >
          <el-option
            v-for="item in developResults"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈内容">
        <el-input
          v-model="dataModel.feedback"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
          placeholder="请输入反馈内容"
        ></el-input>
      </el-form-item>
      <el-form-item label="图片">
        <imageUpload
          ref="imageUpload"
          v-model="dataModel.images"
          show-delete
        ></imageUpload>
      </el-form-item>
      <el-form-item label="附件">
        <el-col :span="12">
          <fileUpload
            ref="fileUpload"
            v-model="dataModel.attachs"
            show-delete
          ></fileUpload>
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
import imageUpload from '@/components/Upload/ImageUpload.vue'
import fileUpload from '@/components/Upload/FileUpload.vue'
export default {
  name: '',
  components: {
    imageUpload,
    fileUpload
  },
  data() {
    return {
      dataModel: {},
      developResults: [],
      rules: {
        enumHospitalDevelopStatus: [
          {
            required: true,
            type: 'number',
            message: '请选择完成状态',
            trigger: 'change'
          }
        ],
        resultDictId: [
          {
            required: true,
            type: 'string',
            message: '请选择结果',
            trigger: 'change'
          }
        ]
      }
    }
  },
  created() {
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init(row) {
      this.hospitalDevelopId = row.id
      // this.queryDevelopResult(row)
    },
    changeStatus(status) {
      this.queryDevelopResult(status)
    },
    // 结果
    queryDevelopResult(status) {
      var params = {
        status: status
      }
      hospitalDevelopApi.queryResultSelector(params).then((res) => {
        if (res) {
          this.developResults = res.data
        }
      })
    },
    chooseVisitContact(customer, contact) {
      this.dataModel.visitContactName = contact.name
      this.$forceUpdate()
    },
    submitForm() {
      this.dataModel.hospitalDevelopId = this.hospitalDevelopId
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          hospitalDevelopApi.completeHospitalDevelop(this.dataModel).then((res) => {
            if (res) {
              this.$message.success('医院开发已完成')
              this.$emit('refreshData')
              this.$refs.dataForm.resetFields()
            }
          })
        }
      })
    }
  }
}
</script>

