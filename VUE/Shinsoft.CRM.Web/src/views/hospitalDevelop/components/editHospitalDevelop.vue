<template>
  <div>
    <el-dialog
      ref="reviewDialog"
      append-to-body
      :close-on-click-modal="false"
      title="新增医院开发"
      :visible="showDialog"
      width="60%"
      @close="closeDialog()"
    >
      <el-form
        ref="dataForm"
        :model="dataModel"
        :rules="rules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            id="title"
            v-model="dataModel.title"
            maxlength="50"
            show-word-limit
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="ownerEmployeeId">
          <chooseEmployee
            v-if="showDialog"
            id="ownerEmployeeId"
            ref="chooseOwnerEmployee"
            show-type="input"
            choose-type="single"
            source-type="hospitalDevelop"
            source-mode="employee"
            :placeholder="dataModel.ownerEmployeeName"
            :title="'选择负责人'"
            @change="chooseOwnerEmployee"
          >
          </chooseEmployee>
        </el-form-item>
        <el-form-item label="参与者">
          <el-row>
            <el-col>
              <chooseEmployee
                v-if="showDialog"
                ref="selectEmployeesss"
                source-type="hospitalDevelop"
                source-mode="member"
                :placeholder="'请选参与者'"
                :title="'选择参与者'"
                :init-data="dataModel.joinMembers"
                show-select-all
                @change="chooseJoinMembers"
              ></chooseEmployee>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-tag
                v-for="(tag, index) in dataModel.joinMembers"
                :key="index"
                style="margin: 0px 5px"
                closable
                :type="tag.type"
                @close="handleCloseTag(tag)"
              >
                {{ tag.displayName }}{{ tag.name }}
              </el-tag>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="开发类型" prop="categoryId">
          <el-select
            id="categoryId"
            v-model="dataModel.categoryId"
            placeholder="请选择开发类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in categorys"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品" prop="productId">
          <el-select
            id="productId"
            v-model="dataModel.productId"
            placeholder="请选择产品"
            style="width: 100%"
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医院" prop="hospitalId">
          <chooseCustomer
            v-if="showDialog"
            id="hospitalId"
            show-type="input"
            choose-type="single"
            source-type="hospitalDevelop"
            :placeholder="'请选择医院'"
            @change="hospitalSelected"
          ></chooseCustomer>
        </el-form-item>
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            id="startDate"
            v-model="dataModel.startDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            id="endDate"
            v-model="dataModel.endDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="dataModel.remark"
            type="textarea"
            :rows="4"
            maxlength="200"
            show-word-limit
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="submitForm">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
import chooseEmployee from '@/components/ChooseEmployee/index.vue'
import chooseCustomer from '@/components/ChooseCustomer/index.vue'
export default {
  name: '',
  components: {
    chooseEmployee,
    chooseCustomer
  },
  data() {
    return {
      span: 8,
      showDialog: false,
      categorys: [],
      userInfo: {},
      dataModel: {
        joinMembers: [],
        ownerEmployeeId: '',
        hospitalId: '',
        productId: ''
      },
      rules: {
        title: [
          {
            required: true,
            type: 'string',
            message: '标题不能为空',
            trigger: 'change'
          }
        ],
        ownerEmployeeId: [
          {
            required: true,
            type: 'string',
            message: '请选择执行人',
            trigger: 'change'
          }
        ],
        productId: [
          {
            required: true,
            type: 'string',
            message: '请选择产品',
            trigger: 'change'
          }
        ],
        hospitalId: [
          {
            required: true,
            type: 'string',
            message: '请选择医院',
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            type: 'string',
            message: '请选择开始时间',
            trigger: 'change'
          }
        ],
        categoryId: [
          {
            required: true,
            type: 'string',
            message: '请选择开发类型',
            trigger: 'change'
          }
        ]
      },
      productList: []
    }
  },
  computed: {
    canChooseAddress: function () {
      return this.dataModel.enumEventMode ===
        this.$constDefinition.EventMode.Offline
    },
    showChooseAddress: function () {
      return this.dataModel.interviewees && this.dataModel.interviewees.length > 0
    }
  },
  created() {
    this.queryHospitalDevelopCategorySelector()
    this.queryProductSelector()
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init() {
      this.dataModel.ownerEmployeeId = this.userInfo ? this.userInfo.identityId : ''
      this.dataModel.ownerEmployeeName = this.userInfo ? this.userInfo.displayName : ''
      this.showDialog = true
    },
    queryHospitalDevelopCategorySelector() {
      hospitalDevelopApi.queryHospitalDevelopCategorySelector().then((res) => {
        if (res) {
          this.categorys = res.data
        }
      })
    },
    queryProductSelector() {
      hospitalDevelopApi.queryProductSelector().then((result) => {
        if (result.succeed) {
          this.productList = result.data.datas
        }
      })
    },
    // 选择负责人
    chooseOwnerEmployee(data) {
      this.dataModel.ownerEmployeeId = data.id
      this.dataModel.ownerEmployeeName = data.displayName
    },
    // 选择医院
    hospitalSelected(data) {
      this.dataModel.hospitalId = data.id
      this.dataModel.hospitalName = data.name
    },
    chooseJoinMembers(data) {
      data.forEach((prop) => {
        if (!this.dataModel.joinMembers.some(item => {
          return prop.memberId === item.memberId
        }) && prop.memberId !== this.dataModel.ownerEmployeeId) {
          // 给员工ID赋值
          prop.employeeId = prop.memberId
          this.dataModel.joinMembers.push(prop)
        }
      })
    },
    handleCloseTag(tag) {
      const index = this.dataModel.joinMembers.findIndex((item) => {
        return item.memberId === tag.memberId
      })
      this.dataModel.joinMembers.splice(index, 1)
      this.$forceUpdate()
    },
    submitForm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          hospitalDevelopApi.addHospitalDevelop(this.dataModel).then((res) => {
            if (res) {
              this.$message.success('新增成功')
              this.closeDialog()
              this.$emit('refreshData')
            }
          })
        }
      })
    },

    closeDialog() {
      this.showDialog = false
      this.$refs.dataForm.resetFields()
      this.dataModel = {
        joinMembers: [],
        ownerEmployeeId: '',
        hospitalId: '',
        productId: ''
      }
    }
  }
}
</script>
