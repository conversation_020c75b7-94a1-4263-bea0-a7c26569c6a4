<template>
  <div>
    <el-form
      ref="dataForm"
      :model="dataModel"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >

      <el-form-item label="开发阶段" prop="phaseId">
        <el-select
          id="phaseId"
          v-model="dataModel.phaseId"
          placeholder="请选择开发阶段"
          style="width: 100%"
        >
          <el-option
            v-for="item in developPhases"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结果" prop="resultDictId">
        <el-select
          id="resultDictId"
          v-model="dataModel.resultDictId"
          placeholder="请选择结果"
          style="width: 100%"
        >
          <el-option
            v-for="item in developResults"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈内容">
        <el-input
          v-model="dataModel.feedback"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
          placeholder="请输入反馈内容"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否拜访">
        <el-radio-group v-model="dataModel.hasVisit" @change="changeHasVisit">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="dataModel.hasVisit"
        label="拜访对象姓名"
        prop="visitContactName"
      >
        <el-row>
          <el-col :span="20">
            <el-input
              v-model="dataModel.visitContactName"
              placeholder="请输入或选择拜访对象姓名"
            ></el-input>
          </el-col>
          <el-col :span="2">
            <chooseContract
              ref="chooseContract"
              style="margin-left: 20px"
              choose-type="single"
              source-type="hospitalDevelop"
              show-type="icon"
              :customer-id="customerId"
              @change="chooseVisitContact"
            ></chooseContract>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item
        v-if="dataModel.hasVisit"
        label="科室/部门"
        prop="visitContactDept"
      >
        <el-input
          v-model="dataModel.visitContactDept"
          placeholder="请输入科室/部门"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="dataModel.hasVisit"
        label="职务"
        prop="visitContactTitle"
      >
        <el-input
          v-model="dataModel.visitContactTitle"
          placeholder="请输入职务"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="dataModel.hasVisit"
        label="电话"
      >
        <el-input
          v-model="dataModel.visitContactMobile"
          placeholder="请输入电话"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="dataModel.hasVisit"
        label="邮箱"
      >
        <el-input
          v-model="dataModel.visitContactEmail"
          placeholder="请输入邮箱"
        ></el-input>
      </el-form-item>
      <el-form-item label="图片">
        <imageUpload
          ref="imageUpload"
          v-model="dataModel.images"
          show-delete
        ></imageUpload>
      </el-form-item>
      <el-form-item label="附件">
        <el-col :span="12">
          <fileUpload
            ref="fileUpload"
            v-model="dataModel.attachs"
            show-delete
          ></fileUpload>
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
import imageUpload from '@/components/Upload/ImageUpload.vue'
import fileUpload from '@/components/Upload/FileUpload.vue'
import chooseContract from '@/components/ChooseContact'

export default {
  name: '',
  components: {
    imageUpload,
    chooseContract,
    fileUpload
  },
  data() {
    return {
      dataModel: {
        hasVisit: 0,
        visitContactId: '',
        visitContactName: '',
        visitContactDept: '',
        visitContactTitle: '',
        visitContactMobile: '',
        visitContactEmail: ''
      },
      developPhases: [],
      developResults: [],
      rules: {
        phaseId: [
          {
            required: true,
            type: 'string',
            message: '请选择开发阶段',
            trigger: 'change'
          }
        ],
        resultDictId: [
          {
            required: true,
            type: 'string',
            message: '请选择结果',
            trigger: 'change'
          }
        ],
        visitContactName: [
          {
            required: true,
            type: 'string',
            message: '请输入或选择拜访对象姓名',
            trigger: 'change'
          }
        ],
        visitContactDept: [
          {
            required: true,
            type: 'string',
            message: '请输入科室/部门',
            trigger: 'change'
          }
        ],
        visitContactTitle: [
          {
            required: true,
            type: 'string',
            message: '请输入职务',
            trigger: 'change'
          }
        ]
      },
      hospitalDevelopId: '',
      customerId: ''
    }
  },
  created() {
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init(row) {
      this.hospitalDevelopId = row.id
      this.customerId = row.hospitalId
      this.queryDevelopPhase(row)
      this.queryDevelopResult(row)
    },
    // 查询开发阶段
    queryDevelopPhase(row) {
      var params = {
        categoryId: row.categoryId,
        currPhaseId: row.currPhaseId
      }
      hospitalDevelopApi.getHospitalDevelopPhaseSelector(params).then((res) => {
        if (res) {
          this.developPhases = res.data
        }
      })
    },
    // 结果
    queryDevelopResult(row) {
      var params = {
        status: 10
      }
      hospitalDevelopApi.queryResultSelector(params).then((res) => {
        if (res) {
          this.developResults = res.data
        }
      })
    },
    changeHasVisit() {
      this.dataModel.visitContactName = ''
      this.dataModel.visitContactId = ''
      this.dataModel.visitContactDept = ''
      this.dataModel.visitContactTitle = ''
      this.dataModel.visitContactMobile = ''
      this.dataModel.visitContactEmail = ''
      this.$forceUpdate()
    },
    chooseVisitContact(contact) {
      console.log(contact)
      this.dataModel.visitContactName = contact.name
      this.dataModel.visitContactId = contact.id
      this.dataModel.visitContactDept = contact.dept
      this.dataModel.visitContactTitle = contact.title
      this.dataModel.visitContactMobile = contact.mobile
      this.dataModel.visitContactEmail = contact.email
      this.$forceUpdate()
    },
    submitForm() {
      this.dataModel.hospitalDevelopId = this.hospitalDevelopId
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          hospitalDevelopApi.followHospitalDevelop(this.dataModel).then((res) => {
            if (res) {
              this.$message.success('跟进成功')
              this.$refs.dataForm.resetFields()
              this.dataModel = {
                hasVisit: 0,
                visitContactId: '',
                visitContactName: '',
                visitContactDept: '',
                visitContactTitle: '',
                visitContactMobile: '',
                visitContactEmail: ''
              }
              this.$emit('refreshData')
            }
          })
        }
      })
    }
  }
}
</script>

