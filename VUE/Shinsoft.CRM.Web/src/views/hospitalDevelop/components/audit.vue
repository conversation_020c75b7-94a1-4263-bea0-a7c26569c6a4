<template>
  <div>
    <el-dialog
      ref="reviewDialog"
      append-to-body
      :close-on-click-modal="false"
      :title="dialogTitle"
      :visible="showDialog"
      width="60%"
      @close="closeDialog()"
    >
      <el-form
        ref="dataForm"
        :model="dataModel"
        label-position="right"
        label-width="100px"
      >
        <el-tabs v-model="activeName">
          <el-tab-pane label="审批信息" name="auditEmployeeInfo" class="dialog-body-height">
            <el-form-item label="类型" prop="ownerEmployeeId">
              {{ reviewModel.enumReviewTypeDesc }}
            </el-form-item>
            <el-form-item label="审批状态" prop="ownerEmployeeId">
              {{ reviewModel.enumReviewStatusDesc }}
            </el-form-item>
            <el-form-item label="提交人" prop="ownerEmployeeId">
              {{ reviewModel.submitEmployeeName }}
            </el-form-item>
            <el-form-item label="审批节点" prop="ownerEmployeeId">
              {{ reviewModel.name }}
            </el-form-item>
            <el-form-item label="审批人" prop="ownerEmployeeId">
              <span
                v-if="reviewModel.enumReviewStatus != 1"
              >{{ reviewModel.reviewEmployeeName }}</span>
              <span v-else>{{ reviewModel.auditorNames }}</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="医院开发信息" name="developInfo" class="dialog-body-height">
            <el-form
              ref="dataForm"
              :model="dataModel"
              label-position="right"
              label-width="100px"
            >
              <el-form-item label="标题">
                {{ dataModel.title }}
              </el-form-item>
              <el-form-item label="负责人" prop="ownerEmployeeId">
                {{ dataModel.ownerEmployeeName }}
              </el-form-item>
              <el-form-item label="参与者">
                <el-row>
                  <el-col>
                    <el-tag
                      v-for="(tag, index) in dataModel.joinMembers"
                      :key="index"
                      style="margin: 0px 5px"
                      :type="tag.type"
                      @close="handleCloseTag(tag)"
                    >
                      {{ tag.displayName }}{{ tag.name }}
                    </el-tag>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="开发类型" prop="categoryId">
                {{ dataModel.hospitalDevelopCategoryName }}
              </el-form-item>
              <el-form-item label="当前阶段" prop="categoryId">
                {{ dataModel.hospitalDevelopPhaseName }}
              </el-form-item>
              <el-form-item label="产品" prop="productId">
                <el-tag style="margin: 0px 5px">
                  {{ dataModel.productName }}
                </el-tag>
              </el-form-item>
              <el-form-item label="医院">
                {{ dataModel.hospitalName }}
                <i
                  v-if="dataModel.isTargetHospital"
                  class="el-icon-star-on"
                  style="color: red"
                ></i>
              </el-form-item>
              <el-form-item label="医院简称">
                {{ dataModel.hospitalShortName }}
              </el-form-item>
              <el-form-item label="目标医院">
                {{ dataModel.isTargetHospital ? "是" : "否" }}
              </el-form-item>
              <el-form-item label="开始时间">
                {{ dataModel.startDate }}
              </el-form-item>
              <el-form-item label="结束时间">
                {{ dataModel.endDate }}
              </el-form-item>
              <el-form-item v-if="dataModel.stepName" label="开发步骤">
                {{ dataModel.stepName }}
              </el-form-item>
              <el-form-item v-if="dataModel.resultName" label="开发结果">
                {{ dataModel.resultName }}
              </el-form-item>
              <el-form-item label="状态">
                {{ dataModel.enumStatusDesc }}
              </el-form-item>
              <el-form-item label="备注">
                {{ dataModel.remark }}
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
        <el-button
          v-if="canAudit"
          type="danger"
          icon="el-icon-close"
          @click="submitAudit(20)"
        >
          审批拒绝
        </el-button>
        <el-button
          v-if="canAudit"
          type="primary"
          icon="el-icon-check"
          @click="submitAudit(10)"
        >
          审批通过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
import masterDataApi from '@/api/masterData'
export default {
  name: '',
  data() {
    return {
      span: 8,
      disabled: true,
      showDialog: false,
      dialogTitle: '医院开发审批',
      action: '',
      visitType: '',
      userInfo: {},
      contactErrorMsg: '',
      dataModel: {},
      reviewModel: {},
      rules: {
        resultDictId: [
          {
            required: true,
            type: 'string',
            message: '请选择反馈结果',
            trigger: 'change'
          }
        ]
      },
      activeName: 'auditEmployeeInfo',
      eventId: ''
    }
  },
  computed: {
    canAudit: function () {
      return this.reviewModel.enableAudit
    }
  },
  created() {
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init(row) {
      this.eventId = row.id
      hospitalDevelopApi.getHospitalDevelopReview({ id: row.id }).then((result) => {
        if (result.succeed) {
          this.reviewModel = result.data
          this.dataModel = result.data.newData
          this.showDialog = true
          if (this.dataModel.isOwnerDoctor) {
            masterDataApi.getDoctorOutCalls({ doctorId: this.dataModel.ownerContactId }).then((res) => {
              if (res) {
                this.dataModel.outCalls = res.data.outCalls
                this.$forceUpdate()
              }
            })
          }
        }
      })
    },
    submitAudit(reviewStatus) {
      var result = reviewStatus === 10 ? '通过' : '拒绝'
      this.$confirm('确认审批' + result, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reviewModel.enumReviewStatus = reviewStatus
        hospitalDevelopApi.auditHospitalDevelopReview(this.reviewModel).then((res) => {
          if (res) {
            this.$message.success(result + '审批成功')
            this.$emit('refreshData')
            this.closeDialog()
          }
        })
      }).catch(() => {

      })
    },
    closeDialog() {
      this.showDialog = false
      this.dataModel = {
        interviewees: [],
        ownerEmployeeId: '',
        eventProductIds: [],
        modeDictId: '',
        enumEventImportance: 10,
        contents: [],
        cooperators: []
      }
      this.activeName = 'auditEmployeeInfo'
      this.feedBackModel = {}
    }
  }
}
</script>
<style>
.hide-table-header .el-table__header-wrapper {
  display: none;
}
.data-emtpy {
  width: 100%;
  text-align: center;
  line-height: 300%;
  color: #868686;
}
</style>
