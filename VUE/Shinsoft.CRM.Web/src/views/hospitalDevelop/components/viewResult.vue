<template>
  <div>
    <el-dialog
      ref="reviewDialog"
      append-to-body
      :close-on-click-modal="false"
      title="医院开发结果"
      :visible="showDialog"
      width="60%"
      @close="closeDialog()"
    >
      <el-form
        ref="feedBackForm"
        :model="feedBackModel"
        label-position="right"
        label-width="100px"
        style="max-height:60vh;overflow-y:scroll"
      >
        <el-form-item
          v-if="
            feedBackModel.enumHospitalDevelopStatus == 20 ||
              feedBackModel.enumHospitalDevelopStatus == 30
          "

          name="enumHospitalDevelopStatus"
          label="完成状态"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.enumHospitalDevelopStatusDesc }}
        </el-form-item>
        <el-form-item
          v-if="
            feedBackModel.enumHospitalDevelopStatus != 20 &&
              feedBackModel.enumHospitalDevelopStatus != 30
          "
          label="开发阶段"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.hospitalDevelopPhaseName }}
        </el-form-item>
        <el-form-item label="拜访结果" label-align="right" label-width="105px">
          {{ feedBackModel.resultName }}
        </el-form-item>
        <el-form-item label="反馈" label-align="right" label-width="105px">
          {{ feedBackModel.feedback }}
        </el-form-item>

        <el-form-item
          v-if="
            feedBackModel.enumHospitalDevelopStatus != 20 &&
              feedBackModel.enumHospitalDevelopStatus != 30
          "
          label="是否拜访"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.hasVisit ? "是" : "否" }}
        </el-form-item>
        <el-form-item
          v-if="feedBackModel.hasVisit"
          label="拜访对象姓名"
          name="visitContactName"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.visitContactName }}
        </el-form-item>

        <el-form-item
          v-if="feedBackModel.hasVisit"
          label="科室/部门"
          name="visitContactDept"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.visitContactDept }}
        </el-form-item>
        <el-form-item
          v-if="feedBackModel.hasVisit"
          label="职务"
          name="visitContactTitle"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.visitContactTitle }}
        </el-form-item>
        <el-form-item
          v-if="feedBackModel.hasVisit"
          label="电话"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.visitContactMobile }}
        </el-form-item>
        <el-form-item
          v-if="feedBackModel.hasVisit"
          label="邮箱"
          label-align="right"
          label-width="105px"
        >
          {{ feedBackModel.visitContactEmail }}
        </el-form-item>
        <el-form-item label="图片">
          <imageUpload
            ref="imageUpload"
            v-model="feedBackModel.images"
            show-delete
            is-view
          ></imageUpload>
        </el-form-item>
        <el-form-item label="附件">
          <el-col :span="12">
            <fileUpload
              ref="fileUpload"
              v-model="feedBackModel.attachs"
              show-delete
              is-view
            ></fileUpload>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import imageUpload from '@/components/Upload/ImageUpload.vue'
import fileUpload from '@/components/Upload/FileUpload.vue'
import hospitalDevelopApi from '@/api/hospitalDevelop'
export default {
  components: {
    fileUpload,
    imageUpload
  },
  data() {
    return {
      feedBackModel: {},
      showDialog: false
    }
  },
  methods: {
    init(row) {
      hospitalDevelopApi.getHospitalDevelopFeedback({ id: row.id }).then(res => {
        if (res) {
          this.feedBackModel = res.data
          this.showDialog = true
          this.$forceUpdate()
        }
      })
    },
    closeDialog() {
      this.showDialog = false
      this.feedBackModel = {}
    }
  }
}
</script>
