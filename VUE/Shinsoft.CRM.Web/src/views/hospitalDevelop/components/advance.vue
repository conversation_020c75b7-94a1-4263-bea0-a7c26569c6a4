<template>
  <div>
    <el-form
      ref="dataForm"
      :model="dataModel"
      :rules="rules"
      label-position="right"
      label-width="100px"
    >
      <el-form-item label="开发阶段" prop="currPhaseId">
        <el-select
          id="currPhaseId"
          v-model="dataModel.currPhaseId"
          placeholder="请选择开发阶段"
          style="width: 100%"
        >
          <el-option
            v-for="item in developPhases"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="dataModel.remark"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
export default {
  name: '',
  data() {
    return {
      dataModel: {},
      developPhases: [],
      rules: {
        currPhaseId: [
          {
            required: true,
            type: 'string',
            message: '请选择完成状态',
            trigger: 'change'
          }
        ]
      }
    }
  },
  created() {
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init(row) {
      this.dataModel = row
      this.queryDevelopPhase(row)
    },
    // 查询开发阶段
    queryDevelopPhase(row) {
      var params = {
        categoryId: row.categoryId
      }
      hospitalDevelopApi.getHospitalDevelopPhaseSelector(params).then((res) => {
        if (res) {
          this.developPhases = res.data
        }
      })
    },
    submitForm() {
      this.dataModel.hospitalDevelopId = this.hospitalDevelopId
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          hospitalDevelopApi.advanceHospitalDevelopPhase(this.dataModel).then((res) => {
            if (res) {
              this.$message.success('推进成功')
              this.dataModel = {}
              this.$emit('refreshData')
            }
          })
        }
      })
    }
  }
}
</script>

