<template>
  <div>
    <el-dialog
      ref="reviewDialog"
      append-to-body
      :close-on-click-modal="false"
      title="查看医院开发"
      :visible="showDialog"
      width="60%"
      @close="closeDialog()"
    >
      <el-tabs v-model="activeName" @tab-click="activeTabs">
        <el-tab-pane label="医院开发信息" name="developInfo" class="dialog-body-height">
          <el-form
            ref="dataForm"
            :model="dataModel"
            label-position="right"
            label-width="100px"
          >
            <el-form-item label="标题">
              {{ dataModel.title }}
            </el-form-item>
            <el-form-item label="负责人" prop="ownerEmployeeId">
              {{ dataModel.ownerEmployeeName }}
            </el-form-item>
            <el-form-item label="参与者">
              <el-row>
                <el-col>
                  <el-tag
                    v-for="(tag, index) in dataModel.joinMembers"
                    :key="index"
                    style="margin: 0px 5px"
                    :type="tag.type"
                    @close="handleCloseTag(tag)"
                  >
                    {{ tag.displayName }}{{ tag.name }}
                  </el-tag>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="开发类型" prop="categoryId">
              {{ dataModel.hospitalDevelopCategoryName }}
            </el-form-item>
            <el-form-item label="当前阶段" prop="categoryId">
              {{ dataModel.hospitalDevelopPhaseName }}
            </el-form-item>
            <el-form-item label="产品" prop="productId">
              <el-tag style="margin: 0px 5px">
                {{ dataModel.productName }}
              </el-tag>
            </el-form-item>
            <el-form-item label="医院">
              {{ dataModel.hospitalName }}
              <i
                v-if="dataModel.isTargetHospital"
                class="el-icon-star-on"
                style="color: red"
              ></i>
            </el-form-item>
            <el-form-item label="医院简称">
              {{ dataModel.hospitalShortName }}
            </el-form-item>
            <el-form-item label="目标医院">
              {{ dataModel.isTargetHospital ? "是" : "否" }}
            </el-form-item>
            <el-form-item label="开始时间">
              {{ dataModel.startDate }}
            </el-form-item>
            <el-form-item label="结束时间">
              {{ dataModel.endDate }}
            </el-form-item>
            <el-form-item v-if="dataModel.stepName" label="开发步骤">
              {{ dataModel.stepName }}
            </el-form-item>
            <el-form-item v-if="dataModel.resultName" label="开发结果">
              {{ dataModel.resultName }}
            </el-form-item>
            <el-form-item label="状态">
              {{ dataModel.enumStatusDesc }}
            </el-form-item>
            <el-form-item label="数据状态">
              {{ dataModel.enumReviewStateDesc }}
            </el-form-item>
            <el-form-item label="备注">
              {{ dataModel.remark }}
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="跟进历史" name="flowingResult" class="dialog-body-height">
          <el-table
            v-if="resultList.length > 0"
            :data="resultList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :header-cell-class-name="'tableStyle'"
          >
            <el-table-column label="序号" type="index" align="center" />
            <el-table-column
              label="反馈结果"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.resultName }}
              </template>
            </el-table-column>
            <el-table-column
              label="反馈人"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.feedbackEmployeeName }}
              </template>
            </el-table-column>
            <el-table-column
              label="反馈时间"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.feedbackTime }}
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              header-align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="{ row }">
                <el-button
                  type="text"
                  title="详情"
                  @click="handleView(row)"
                >详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="data-emtpy">暂无跟进记录</div>
        </el-tab-pane>
        <el-tab-pane label="推进历史" name="advanceResult" class="dialog-body-height">
          <el-table
            v-if="advanceList.length > 0"
            :data="advanceList"
            stripe
            border
            fit
            highlight-current-row
            style="width: 100%"
            :header-cell-class-name="'tableStyle'"
          >
            <el-table-column label="序号" type="index" align="center" />
            <el-table-column
              label="原阶段"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.oirPhaseName }}
              </template>
            </el-table-column>
            <el-table-column
              label="推进阶段"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.newPhaseName }}
              </template>
            </el-table-column>
            <el-table-column
              label="推进时间"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.createTime }}
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              header-align="center"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ row.newRemark }}
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="data-emtpy">暂无推进记录</div>
        </el-tab-pane>
        <el-tab-pane v-if="dataModel.enableFollowing" label="跟进" name="flowingInfo" class="dialog-body-height">
          <flowing ref="flowing" @refreshData="childRefreshData(1)"></flowing>
        </el-tab-pane>
        <el-tab-pane v-if="dataModel.enableAdvance" label="推进" name="advanceInfo" class="dialog-body-height">
          <advance ref="advance" @refreshData="childRefreshData(2)"></advance>
        </el-tab-pane>
        <el-tab-pane v-if="dataModel.enableComplete" label="完成" name="finishInfo" class="dialog-body-height">
          <finish ref="finish" @refreshData="childRefreshData(3)"></finish>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
        <el-button
          v-if="activeName === 'flowingInfo' && dataModel.enableFollowing"
          type="primary"
          icon="el-icon-check"
          @click="submitComponents(1)"
        >
          提交跟进
        </el-button>
        <el-button
          v-if="activeName === 'advanceInfo' && dataModel.enableAdvance"
          type="primary"
          icon="el-icon-check"
          @click="submitComponents(2)"
        >
          提交推进
        </el-button>
        <el-button
          v-if="activeName === 'finishInfo' && dataModel.enableComplete"
          type="primary"
          icon="el-icon-check"
          @click="submitComponents(3)"
        >
          完成
        </el-button>
      </div>
    </el-dialog>
    <viewResultVue ref="viewResultVue"></viewResultVue>
  </div>
</template>
<script>
import hospitalDevelopApi from '@/api/hospitalDevelop'
import viewResultVue from './viewResult.vue'
import advance from './advance.vue'
import finish from './finish.vue'
import flowing from './flowing.vue'
export default {
  name: '',
  components: {
    viewResultVue,
    flowing,
    finish,
    advance
  },
  data() {
    return {
      span: 8,
      showDialog: false,
      categorys: [],
      userInfo: {},
      dataModel: {
        joinMembers: [],
        ownerEmployeeId: '',
        hospitalId: '',
        productId: ''
      },
      activeName: 'developInfo',
      resultList: [],
      advanceList: []
    }
  },
  computed: {
    canChooseAddress: function () {
      return this.dataModel.enumEventMode ===
        this.$constDefinition.EventMode.Offline
    },
    showChooseAddress: function () {
      return this.dataModel.interviewees && this.dataModel.interviewees.length > 0
    }
  },
  created() {
    this.userInfo = this.$store.getters.user
  },
  methods: {
    // 初始化
    init(row) {
      this.showDialog = true
      this.getHospitalDevelop(row)
    },
    getHospitalDevelop(row) {
      hospitalDevelopApi.getHospitalDevelop({ id: row.id }).then((res) => {
        if (res) {
          this.dataModel = res.data
        }
      })
    },
    // 跟进历史
    queryHospitalDevelopFeedback() {
      hospitalDevelopApi.queryHospitalDevelopFeedback({ hospitalDevelopId: this.dataModel.id, order: '-CreateTime' }).then((res) => {
        if (res) {
          this.resultList = res.data.datas
        }
      })
    },
    // 推进历史
    queryAdvanceHospitalDevelopPhaseLog() {
      hospitalDevelopApi.queryAdvanceHospitalDevelopPhaseLog({ hospitalDevelopId: this.dataModel.id, order: '-CreateTime' }).then((res) => {
        if (res) {
          this.advanceList = res.data.datas
        }
      })
    },
    handleView(row) {
      this.$refs.viewResultVue.init(row)
    },
    activeTabs(tab) {
      switch (tab.name) {
        case 'flowingInfo':
          this.$refs.flowing.init(this.dataModel)
          break
        case 'advanceInfo':
          this.$refs.advance.init(this.dataModel)
          break
        case 'finishInfo':
          this.$refs.finish.init(this.dataModel)
          break
        case 'flowingResult':
          this.queryHospitalDevelopFeedback()
        break
        case 'advanceResult':
          this.queryAdvanceHospitalDevelopPhaseLog()
        break
      }
    },
    submitComponents(module) {
      switch (module) {
        case 1:
          this.$refs.flowing.submitForm()
          break
        case 2:
          this.$refs.advance.submitForm()
          break
        case 3:
          this.$refs.finish.submitForm()
          break
      }
    },
    childRefreshData(module) {
      switch (module) {
        case 1:
          this.activeName = 'flowingResult'
          this.queryHospitalDevelopFeedback()
          break
        case 2:
          this.activeName = 'advanceResult'
          this.queryAdvanceHospitalDevelopPhaseLog()
          break
        case 3:
          this.activeName = 'developInfo'
          this.getHospitalDevelop(this.dataModel)
          break
      }
    },
    closeDialog() {
      this.showDialog = false
      this.dataModel = {
        joinMembers: [],
        ownerEmployeeId: '',
        hospitalId: '',
        productId: ''
      }
      this.activeName = 'developInfo'
      this.$emit('refreshData')
    }
  }
}
</script>
<style>
.data-emtpy {
  width: 100%;
  text-align: center;
  line-height: 300%;
  color: #868686;
}
</style>
