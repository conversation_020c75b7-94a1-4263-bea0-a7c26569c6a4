<template>
  <div>
    <el-dialog ref="dataForm" append-to-body :close-on-click-modal="false" :title="textMap[dialogStatus]"
      :visible="showDialog" width="60%" @close="closeDialog()">
      <el-form :model="editModel" :rules="editRules" ref="editForm" label-position="right" label-width="100px">
        <el-form-item label="类型" prop="enumType" v-show="showType">
          <el-select v-model="editModel.enumType" class="filter-item" placeholder="类型" collapse-tags>
            <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="编码" prop="code">
          <el-input v-model="editModel.code" clearable placeholder="编码" maxlength="50" />
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="editModel.name" clearable placeholder="名称" maxlength="200" @change="$forceUpdate()" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="editModel.remark" type="textarea" clearable placeholder="备注" maxlength="500" />
        </el-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveEdit">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";


export default {
  name: '',
  components: {
  },
  props: {

  },
  data() {
    return {
      span: 8,
      showDialog: false,
      showType: false,
      typeList: [],

      dialogStatus: "",
      textMap: {
        view: "查看",
        eidt: "编辑",
        add: "新增",
        delete: "删除"
      },

      editModel: {},


      editRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
        ],

      },

    }
  },
  watch: {

  },
  mounted() {

  },
  created() {
    this.initTypeList();

  },
  methods: {
    //初始化
    init(data, action) {
      this.showDialog = true
      this.showType = !data.id
      if (action == "edit") {
        this.dialogStatus = action
        masterAPI
          .getCustomerCategory({ id: data.id })
          .then((result) => {
            if (result.succeed) {
              this.editModel = result.data;
            }
          });
      }
      else if (action == "add") {
        this.dialogStatus = action
        this.editModel.id = null
        this.editModel.parentId = data.id
        this.editModel.enumCustomerType = data.enumCustomerType
        if (this.$refs['editForm'] != undefined) {
          this.$refs['editForm'].resetFields()
        }

      }


    },

    //获取机构类型枚举
    initTypeList() {
      var param = { enumType: "CustomerType" };
      selectorAPI
        .getEnumInfos(param)
        .then((result) => {
          this.typeList = result.data.datas;
        })
        .catch((error) => {
          console.log(error);
        });
    },



    cancle() {
      this.showDialog = false
    },

    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },

    closeDialog() {
      this.showDialog = false
    },


    saveEdit() {
      this.$refs.editForm.validate((validItem) => {
        if (validItem) {
          if (this.dialogStatus == "add") {
            let operateText = "新增"
            masterAPI
              .addCustomerCategory(this.editModel)
              .then((result) => {
                this.closeDialog()
                this.$emit("refreshList")

                this.$message({
                  type: 'success',
                  message: operateText + '成功!'
                });
              })
              .catch((e) => {
                console.log(e)
                this.$message({
                  type: 'info',
                  message: '已取消' + operateText
                });
              });
          }

          if (this.dialogStatus == "edit") {
            let operateText = "编辑"
            masterAPI
              .updateCustomerCategory(this.editModel)
              .then((result) => {
                this.closeDialog()
                this.$emit("refreshList")

                this.$message({
                  type: 'success',
                  message: operateText + '成功!'
                });
              })
              .catch((e) => {
                console.log(e)
                this.$message({
                  type: 'info',
                  message: '已取消' + operateText
                });
              });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },



  }

}
</script>
