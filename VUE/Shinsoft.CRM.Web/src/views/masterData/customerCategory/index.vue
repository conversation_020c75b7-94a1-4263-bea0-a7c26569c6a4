// 机构分类
<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-input :span="span" placeholder="输入关键字进行过滤" v-model="filterText" clearable style="width: 25%!important;">
        </el-input>
      </el-row>
    </div>

    <el-tree class="filter-tree" :data="data" :props="defaultProps" :default-expand-all="expandAll" accordion :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>{{ node.label }}</span>
        <span>
          <!-- 暂时只允许3级，不允许继续添加子级分类 -->
          <!-- <el-button type="text" size="mini" @click="() => appendNode(node, data)" v-if="node.level < 3">
            添加
          </el-button>
          <el-button type="text" size="mini" @click="() => editNode(node, data)">
            修改
          </el-button>
          <el-button type="text" size="mini" @click="() => removeNode(node, data)">
            删除
          </el-button> -->
        </span>
      </span>
    </el-tree>

    <EditCustomerCategory ref="editPage" @refreshList="getCategoryData"></EditCustomerCategory>
  </div>
</template>
<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.show-hide:hover :nth-child(2) {
  display: inline-block !important;
}
</style>
<script>
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

import masterAPI from "@/api/masterData";
import SelectorApi from "@/api/selector";
import EditCustomerCategory from "./components/editCustomerCategory.vue";

export default {
  name: "CustomerCategory",
  components: {
    Pagination,
    EditCustomerCategory
  },
  data() {
    return {
      span: 4,
      filterText: "",
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: "+EnumCustomerType",
        parentId: null
      },
      expandAll: false,
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {

  },
  created() {
    this.getCategoryData()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getCategoryData() {
      masterAPI
        .queryCustomerCategory(this.listQuery)
        .then((result) => {
          if (result.succeed) {
            console.log(result)
            this.data = JSON.parse(JSON.stringify(this.formatData(result.data.datas)))
          }
        })

    },

    formatData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].deleted == true) {
          data.splice(i, 1)
          i--
        }
        else {
          this.formatData(data[i].children)
        }
      }
      return data;
    },

    // 新增树节点
    appendNode(node, data) {
      this.$refs.editPage.init(data, "add")

    },
    // 删除树节点
    removeNode(node, data) {
      var that = this;
      // 判断该节点是否有子节点
      if (node.childNodes.length != 0) {
        this.$message({
          message: '该节点下存在子节点，不允许直接删除',
          type: 'warning'
        });
        return;
      }
      this.$confirm("是否确认删除此节点?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          masterAPI
            .deleteCustomerCategory({ id: data.id })
            .then(result => {
              that.getCategoryData()
            })
            .catch(function () { })
        });
    },
    // 编辑树节点
    editNode(node, data) {
      this.$refs.editPage.init(data, "edit")
    }




  },
};
</script>
