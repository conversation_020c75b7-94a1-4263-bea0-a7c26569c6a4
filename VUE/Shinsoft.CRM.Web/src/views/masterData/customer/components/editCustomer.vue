<template>
  <div>
    <el-dialog append-to-body :title="title" width="75%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-card class="box-card" style="margin-top:10px">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-form ref="dataForm" :rules="rules" :model="tempFormModel" label-position="right" label-width="100px" class="el-dialogform">
          <el-row :gutter="10">
            <el-col :span="span">
              <el-form-item label="性质" prop="enumType">
                <el-select v-model="tempFormModel.enumType" @change="changeType" class="filter-item" style="width:100%" placeholder="类型" collapse-tags>
                  <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item prop="customerCategoryIds" label="类型">
                <el-cascader v-model="tempFormModel.customerCategoryIds" :options="customerCategorys" :props="{
                    value: 'id',
                    label: 'name',
                    checkStrictly: false,
                    expandTrigger: 'hover',
                  }" placeholder="类型" clearable style="width: 100%" :key="cascaderKey" />
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="名称" prop="name">
                <el-input v-model="tempFormModel.name" clearable show-word-limit placeholder="名称" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="简称" prop="shortName">
                <el-input v-model="tempFormModel.shortName" clearable show-word-limit placeholder="简称" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="false && tempFormModel.enumType != 1 && !this.tempFormModel.id">
              <el-form-item label="添加客户">
                <el-checkbox v-model="tempFormModel.isAddDefaultContact"></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="span" v-show="tempFormModel.enumType == 1">
              <el-form-item label="医院等级" prop="gradeDictId" :rules="tempFormModel.enumType == 1 ? rules.gradeDictId : []">
                <el-select :key="32342222" v-model="tempFormModel.gradeDictId" class="filter-item" style="width:100%" placeholder="请选择医院等级" collapse-tags>
                  <el-option v-for="item in grades" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="span" v-show="tempFormModel.enumType == 1">
              <el-form-item label="医院等次" prop="rankDictId" :rules="tempFormModel.enumType == 1 ? rules.rankDictId : []">
                <el-select :key="32342" v-model="tempFormModel.rankDictId" class="filter-item" style="width:100%" placeholder="请选择医院等次" collapse-tags>
                  <el-option v-for="item in ranks" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址" prop="address">
                <SelectMapDialog @addressSelected="addressSelected" :placeholder="'请先点击文本框后面的按钮用地图定位地址，然后在进行编辑'" :showText="true" :address="tempFormModel.address" :addressLocX="tempFormModel.addressLocX" :addressLocY="tempFormModel.addressLocY" :value.sync="tempFormModel.address"></SelectMapDialog>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card v-if="tempFormModel.isAddDefaultContact && !this.tempFormModel.id" class="cardMargin box-card" style="margin-top:10px">
        <div slot="header" class="clearfix">
          <span>客户</span>
        </div>
        <el-row class="list-container">
          <el-col :span="24">
            <el-form ref="dataFormContact" :rules="rulesContact" :model="formContact" label-position="right" label-width="100px" class="el-dialogform">
              <el-row :gutter="10">
                <el-col :span="span">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="formContact.name" clearable show-word-limit placeholder="请输入客户姓名" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="span">
                  <el-form-item label="性别" prop="enumGender">
                    <el-radio-group v-model="formContact.enumGender">
                      <el-radio label="1" key="1">男</el-radio>
                      <el-radio label="2" key="2">女</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="contactFlags && contactFlags.length > 0">
                  <el-form-item label="标签">
                    <el-checkbox-group v-model="formContact.selectFlags" @change="contactFlagChange">
                      <el-checkbox v-for="item in contactFlags" :label="item.value" :key="item.value">{{ item.desc }}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="span">
                  <el-form-item label="科室/部门" prop="dept">
                    <el-input v-model="formContact.dept" clearable show-word-limit placeholder="请输入科室/部门" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="span" :key="10003" v-if="!formContact.selectFlags.find(v=>v == 1)">
                  <el-form-item label="职务" prop="title" :rules="!formContact.selectFlags.find(v=>v == 1) ? rulesContact.title : []">
                    <el-input v-model="formContact.title" clearable show-word-limit placeholder="请输入职务" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="span" :key="10004" v-if="formContact.selectFlags.find(v=>v == 1)">
                  <el-form-item label="职称" prop="titleDictId" :rules="formContact.selectFlags.find(v=>v == 1) ? rulesContact.titleDictId : []">
                    <el-select :key="300000004" v-model="formContact.titleDictId" class="filter-item" style="width:100%" placeholder="请选择职称" collapse-tags>
                      <el-option v-for="item in titleLevels" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="span" v-if="formContact.selectFlags.find(v=>v == 1)">
                  <el-form-item label="职务" prop="positionDictId" :rules="formContact.selectFlags.find(v=>v == 1) ? rulesContact.positionDictId : []">
                    <el-select :key="3234222233" v-model="formContact.positionDictId" class="filter-item" style="width:100%" placeholder="请选择职务" collapse-tags>
                      <el-option v-for="item in positions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="span" v-if="formContact.selectFlags.find(v=>v == 1)">
                  <el-form-item label="医生级别" prop="levelDictId" :rules="formContact.selectFlags.find(v=>v == 1) ? rulesContact.levelDictId : []">
                    <el-select :key="3234222222" v-model="formContact.levelDictId" class="filter-item" style="width:100%" placeholder="请选择医生级别" collapse-tags>
                      <el-option v-for="item in doctorLevels" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="span" :key="10008" v-if="formContact.selectFlags.find(v=>v == 2)">
                  <el-form-item label="讲者级别" prop="speakerLevelDictId" :rules="formContact.selectFlags.find(v=>v == 2) ? rulesContact.speakerLevelDictId : []">
                    <el-select :key="300000008" v-model="formContact.speakerLevelDictId" class="filter-item" style="width:100%" placeholder="请选择讲者级别" collapse-tags>
                      <el-option v-for="item in speakerLevels" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="span" :key="10010">
                  <el-form-item label="产品线" prop="productLineIds">
                    <el-checkbox-group :key="300000010" v-model="formContact.productLineIds">
                      <el-checkbox v-for="item in contactProductLines" :label="item.productLineId" :key="item.productLineId">{{ item.productLineName }}</el-checkbox>
                    </el-checkbox-group>
                    <span v-if="!contactProductLines || contactProductLines.length == 0" style="color:red">当前用户没有产品线权限</span>
                  </el-form-item>
                </el-col>
                <el-col :span="span">
                  <el-form-item label="电话" prop="mobile">
                    <el-input v-model="formContact.mobile" clearable show-word-limit placeholder="请输入电话" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="span">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="formContact.email" clearable show-word-limit placeholder="请输入邮箱" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" :rows="3" v-model="formContact.remark" clearable show-word-limit placeholder="请输入备注" maxlength="500" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
      </el-card>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancle()"> 关闭 </el-button>
        <el-button :loading="btnSaveLoading" type="primary" icon="el-icon-check" @click="save()">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";

export default {
  name: '',
  components: {

  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
  },
  data() {
    const ruleIdsValidator = (rule, value, callback) => {
      if (this.tempFormModel.customerCategoryIds == null || this.tempFormModel.customerCategoryIds.length === 0) {
        callback(new Error('必须选择类型'))
      }
      callback()
    }
    return {
      span: 12,
      rules: {
        enumType: [
          { required: true, message: '请选择机构性质', trigger: 'change' },
        ],
        customerCategoryIds: [
          { required: true, type: 'array', validator: ruleIdsValidator, trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入机构名称', trigger: 'blur' }
        ],
        gradeDictId: [
          { required: true, message: '必须选择医院等级', trigger: 'change' }
        ],
        rankDictId: [
          { required: true, message: '必须选择医院等次', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请先点击文本框后面的按钮用地图定位地址，然后在进行编辑', trigger: 'blur' },
          { required: true, message: '请先点击文本框后面的按钮用地图定位地址，然后在进行编辑', trigger: 'change' }
        ],
      },
      rulesContact: {
        name: [
          { required: true, message: '请输入客户姓名', trigger: 'blur' },
        ],
        dept: [
          { required: true, message: '请输入科室/部门', trigger: 'blur' },
        ],
        enumGender: [
          { required: true, message: '请选择性别', trigger: 'change' },
        ],
        title: [
          { required: true, message: '请输入职务', trigger: 'blur' },
        ],
        titleDictId: [
          { required: true, message: '必须选择职称', trigger: 'change' },
        ],
        positionDictId: [
          { required: true, message: '必须选择职务', trigger: 'change' },
        ],
        productLineIds: [
          { type: 'array', required: true, message: '请至少选择一个产品线', trigger: 'change' }
        ],
        levelDictId: [
          { required: true, message: '必须选择医生级别', trigger: 'change' },
        ],
        speakerLevelDictId: [
          { required: true, message: '必须选择讲者级别', trigger: 'change' },
        ],
      },
      tempFormModel: { id: '', customerCategoryIds: [], defaultContact: {}, isAddDefaultContact: false, gradeDictId: null, rankDictId: null, },
      formContact: { selectFlags: [], titleDictId: '', positionDictId: '', levelDictId: '', productLineIds: [] },
      btnSaveLoading: false,

      typeList: [],
      customerCategorys: [],
      cascaderKey: 1,

      grades: [],
      ranks: [],

      contactFlags: [],
      titleLevels: [],
      positions: [],
      doctorLevels: [],
      speakerLevels: [],

      contactProductLines: [],

      isBrowserSupport: false,
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    }
  },
  mounted() {
  },
  created() {
    this.checkBrowser()

    this.init()
    this.initTypeList()
    this.initGrades()
    this.initRanks()
    this.initContactProductLines()

    if (!this.tempFormModel.id) {
      this.initDoctorLevels()
      this.initSpeakerLevels()
      this.initTitleLevels()
      this.initPositions()
      this.initContactFlags()
    }
  },
  methods: {
    contactFlagChange(value) {
      if (!this.tempFormModel.id) {
        if (this.formContact.selectFlags && this.formContact.selectFlags.length > 1) {
          this.formContact.selectFlags.splice(0, 1)
        }
      }
    },
    //获取性质
    initTypeList() {
      var param = { enumType: "CustomerType" };
      selectorAPI.getEnumInfos(param).then((result) => {
        this.typeList = result.data.datas;
      }).catch((error) => {
        console.log(error);
      });
    },
    //获取机构分类
    initCustomerCategorys() {
      if (this.tempFormModel.enumType) {
        selectorAPI.queryCustomerCategorySelector({ type: this.tempFormModel.enumType })
          .then((result) => {
            this.customerCategorys = result.data;
            this.customerCategorys.forEach(function (item1) {
              if (item1.children.length === 0) {
                delete item1.children
              } else {
                item1.children.forEach(function (item3) {
                  delete item3.children
                })
              }
            })

          })
          .catch((error) => {
            console.log(error);
          });
      }
      else {
        this.customerCategorys = []
      }
    },
    changeType() {
      if (this.tempFormModel.enumType == 1) {
        this.tempFormModel.isAddDefaultContact = false
      }

      ++this.cascaderKey
      this.tempFormModel.customerCategoryIds = []
      this.customerCategorys = []
      this.initCustomerCategorys();

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    initGrades() {
      var param = { parentCode: "Customer_Hospital_Grade" };
      selectorAPI.getDicts(param).then((result) => {
        this.grades = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initRanks() {
      var param = { parentCode: "Customer_Hospital_Rank" };
      selectorAPI.getDicts(param).then((result) => {
        this.ranks = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initContactFlags() {
      var param = { enumType: "ContactFlag", group: 'edit' };
      selectorAPI.getEnumInfos(param).then((result) => {
        this.contactFlags = result.data.datas;
      }).catch((error) => {
        console.log(error);
      });
    },
    initTitleLevels() {
      var param = { parentCode: "Contact_Doctor_Title" };
      selectorAPI.getDicts(param).then((result) => {
        this.titleLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initPositions() {
      var param = { parentCode: "Contact_Doctor_Position" };
      selectorAPI.getDicts(param).then((result) => {
        this.positions = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initDoctorLevels() {
      var param = { parentCode: "Contact_Doctor_Level" };
      selectorAPI.getDicts(param).then((result) => {
        this.doctorLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initSpeakerLevels() {
      var param = { parentCode: "Contact_Speaker_Level" };
      selectorAPI.getDicts(param).then((result) => {
        this.speakerLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initContactProductLines(isUpdate) {
      masterAPI.queryContactProductLineSelector().then((result) => {
        this.contactProductLines = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    addressSelected(val) {
      this.$set(this.tempFormModel, 'addressLocX', val.addressLocX)
      this.$set(this.tempFormModel, 'addressLocY', val.addressLocY)
      this.$set(this.tempFormModel, 'regions', val.regions)
    },
    init() {
      if (!this.id) {
        this.clear()
        return
      }
      if (this.id) {
        this.get(this.id)
      }
    },
    get(id) {
      this.btnSaveLoading = true
      masterAPI.getCustomer({ id: this.id }).then(result => {
        this.tempFormModel = result.data

        this.initCustomerCategorys()

        this.btnSaveLoading = false
      }).catch(error => {
        this.btnSaveLoading = false
      })
    },
    save() {
      if (!this.tempFormModel.addressLocX || !this.tempFormModel.addressLocY) {
        this.$notice.message('请点击地址文本框后的按钮用地图定位地址，然后在根据需要编辑', 'error')

        return false
      }

      if (this.tempFormModel.isAddDefaultContact) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.validateContact(true)
          }
          else {
            this.validateContact(false)
          }
        })
      } else {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.tempFormModel.customerCategoryId = this.tempFormModel.customerCategoryIds[this.tempFormModel.customerCategoryIds.length - 1]

            if (!this.tempFormModel.id) {
              this.addNew()
            } else {
              this.update()
            }
          }
        })
      }
    },
    validateContact(res) {
      this.$refs['dataFormContact'].validate((valid) => {
        if (valid && res) {
          this.tempFormModel.customerCategoryId = this.tempFormModel.customerCategoryIds[this.tempFormModel.customerCategoryIds.length - 1]
          this.formContact.selectFlags.forEach(e => {
            this.formContact.enumFlags = this.formContact.enumFlags | e
          })
          if (!!!this.formContact.selectFlags.find(p => p == 1)) {
            this.formContact.titleDictId = ''
            this.formContact.positionDictId = ''
            this.formContact.levelDictId = ''
          } else {
            this.formContact.title = ''
          }

          this.formContact.isDefault = true
          this.tempFormModel.defaultContact = this.formContact
          if (!this.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      // 产品线赋值
      if (this.tempFormModel.isAddDefaultContact) {
        var pl = this.contactProductLines.filter(item => {
          return this.formContact.productLineIds && this.formContact.productLineIds.find(i => i == item.productLineId)
        })
        this.formContact.productLines = pl
      }

      this.btnSaveLoading = true
      masterAPI.addCustomer(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      masterAPI.updateCustomer(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
      this.tempFormModel = { id: this.id, customerCategoryIds: [], defaultContact: {}, isAddDefaultContact: false, gradeDictId: null, rankDictId: null, }

      this.formContact = { selectFlags: [], titleDictId: '', positionDictId: '', levelDictId: '', productLineIds: [] }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    checkBrowser() {
      const userAgent = window.navigator.userAgent.toLowerCase()
      let isChrome = userAgent.indexOf('chrome') > -1 && userAgent.indexOf('edg') < 0

      if (isChrome) {
        this.isBrowserSupport = false
      }
      else {
        this.isBrowserSupport = true
      }
    },
  }

}
</script>