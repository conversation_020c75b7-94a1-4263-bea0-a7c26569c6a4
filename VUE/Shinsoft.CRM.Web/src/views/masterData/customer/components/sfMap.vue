<template>
  <div>
    <el-dialog :append-to-body="true" width="60%" height="600" :close-on-click-modal="true" title="选择地点" :visible="showDialog" @close="cancle()">

      <iframe frameborder="0" border="0" :src="mapUrl" width="100%" height="600px"></iframe>

    </el-dialog>
  </div>
</template>
<script>

import mapApi from '@/api/map'
import sysApi from '@/api/sys'

export default {
  name: '',
  components: {
    mapApi,
    sysApi
  },
  props: {

  },
  data() {
    return {
      showDialog: false,
      mapUrl: '',
      location: { X: 0, Y: 0 },
      mapBaseUrl: '',
    }
  },
  watch: {

  },
  beforeDestroy() {
    window.removeEventListener("message", this.messageData);
  },
  mounted() {
    window.addEventListener("message", this.messageData, false);
  },
  created() {

  },
  methods: {
    init(item) {
      this.mapBaseUrl = window.sessionStorage.getItem('MapBaseUrl')
      if (!this.mapBaseUrl) {
        sysApi.getMapBaseUrlForWeb().then((result) => {
          window.sessionStorage.setItem('MapBaseUrl', result.data)
          this.mapBaseUrl = result.data

          this.showDialog = true

          if (!item.addressLocX || !item.addressLocY || item.addressLocX === 0.0 || item.addressLocY === 0.0) {
            item.addressLocX = parseFloat(31).toFixed(2)
            item.addressLocY = parseFloat(121).toFixed(2)
          }
          this.$nextTick(() => {
            this.mapUrl = this.mapBaseUrl + 'flag=3&Method=2&Coord=' + item.addressLocX + ',' + item.addressLocY
          })
        })
          .catch((error) => {
            console.log(error)
          })
      }
      else {
        this.showDialog = true

        if (!item.addressLocX || !item.addressLocY || item.addressLocX === 0.0 || item.addressLocY === 0.0) {
          item.addressLocX = parseFloat(31).toFixed(2)
          item.addressLocY = parseFloat(121).toFixed(2)
        }
        this.$nextTick(() => {
          this.mapUrl = this.mapBaseUrl + 'flag=3&Method=2&Coord=' + item.addressLocX + ',' + item.addressLocY
        })
      }
    },

    cancle() {
      this.showDialog = false
    },
    messageData(e) {
      this.$emit("locationSelected", e)
      this.showDialog = false
    },
  }

}
</script>