<template>
  <div>
    <el-card class="box-card" style="margin-top:10px">
      <div slot="header" class="clearfix">
        <span>基本信息</span>
      </div>
      <el-form ref="dataForm" :model="data" label-position="right" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="名称" prop="alias">
              {{ data.name }}
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="data.mergeToName">
            <el-form-item>
              <span slot="label">
                已合并至
              </span>
              <span style="color:#66A9FE;text-decoration:underline;cursor:pointer;" @click="handleViewMergeTo(data)">{{ data.mergeToName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="简称" prop="alias">
              {{ data.shortName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性质" prop="alias">
              {{ data.enumTypeDesc }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类型" prop="alias">
              {{data.customerCategoryParentName}}&nbsp;/&nbsp;{{data.customerCategoryName}}
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.enumType == 1">
            <el-form-item label="医院等级" prop="alias">
              {{data.gradeName}}
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.enumType == 1">
            <el-form-item label="医院等次" prop="alias">
              {{data.rankName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人" prop="alias">
              {{data.creatorEmployeeName}}
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.enumStatus == 20">
            <el-form-item label="冻结人" prop="alias">
              {{data.freezeEmployeeName}}
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.enumStatus == 20">
            <el-form-item label="冻结时间" prop="alias">
              {{data.freezeTime}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="alias">
              {{data.enumStatusDesc}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审批状态" prop="alias">
              {{data.enumReviewStateDesc}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="地址" prop="alias">
              {{data.address}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <SelectMapDialog :showText="false" :addressLocX="data.addressLocX" :addressLocY="data.addressLocY"></SelectMapDialog>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="cardMargin box-card" style="margin-top:10px">
      <div slot="header" class="clearfix">
        <span>客户</span>
      </div>
      <el-row class="list-container">
        <el-col :span="24">
          <el-table :data="contacts" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'createTime', order: 'descending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
            <el-table-column type="index" width="50" label="序号" header-align="center" align="center">
            </el-table-column>
            <el-table-column label="姓名" min-width="120px" header-align="center" align="right">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="性别" width="75px" header-align="center" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.enumGenderDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="标签" min-width="120px" header-align="center" align="right">
              <template slot-scope="{ row }">
                {{!!row.enumFlagsDesc ? row.enumFlagsDesc : '无'}}
              </template>
            </el-table-column>
            <el-table-column label="科室/部门" min-width="120px" header-align="center" align="right">
              <template slot-scope="{ row }">
                {{row.dept}}
              </template>
            </el-table-column>
            <el-table-column label="职称" min-width="120px" header-align="center" align="right">
              <template slot-scope="{ row }">
                {{row.title}}
              </template>
            </el-table-column>
            <el-table-column label="职务" min-width="120px" header-align="center" align="right">
              <template slot-scope="{ row }">
                {{row.title}}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="146" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <el-button type="text" title="查看" @click="handleViewContact(row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>

    <el-dialog append-to-body :title="viewDialogTitle" :close-on-click-modal="false" :visible="dialogViewFormVisible" width="75%" @close="onHidden">
      <viewContact ref="refContact" :id="itemId" :show-dialog="dialogViewFormVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="onHidden"> 关闭 </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import masterAPI from "@/api/masterData";
import viewContact from "../../contact/components/viewContact";

export default {
  name: '',
  components: {
    viewContact
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 8,
      data: {},
      dialogEditFormVisible: false,
      dialogViewFormVisible: false,
      modifyDialogTitle: '',
      viewDialogTitle: '',
      itemId: null,
      customerId: null,

      contacts: [],
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function (val) {
        if (val === true) {
          this.data.id = this.id
          this.get()
          this.queryContact()
        }
      }
    }
  },
  mounted() {
  },
  created() {

  },
  methods: {
    get() {
      this.$set(this.data, 'addressLocX', 31.228725)
      this.$set(this.data, 'addressLocY', 121.475186)
      masterAPI.getCustomer({ id: this.data.id }).then(result => {
        if (result.succeed) {
          this.data = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    queryContact() {
      var parquery = { customerId: this.data.id, order: '+Name', pageIndex: 0, }
      masterAPI.queryContact(parquery).then((result) => {
        if (result.succeed) {
          this.contacts = result.data.datas
        } else {
          this.$notice.resultTip(result)
        }
      })
        .catch((error) => {
          console.log(error)
        })
    },
    cancle() {
      this.$emit('close')
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleViewContact(row) {
      this.itemId = row.id
      this.dialogViewFormVisible = true
      this.viewDialogTitle = '查看客户'
    },
    handleViewMergeTo() {
      this.data.id = this.data.mergeToId
      this.get()
      this.queryContact()
    },
    onHidden() {
      this.dialogEditFormVisible = false
      this.dialogViewFormVisible = false
    },
    onRefresh() {
      this.get()
      this.dialogEditFormVisible = false
    },
  }

}
</script>
