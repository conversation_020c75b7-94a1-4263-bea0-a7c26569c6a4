<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-input v-model="listQuery.Keywords" clearable placeholder="关键词" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.Types" class="filter-item" placeholder="性质" multiple collapse-tags clearable>
            <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.statuses" class="filter-item" placeholder="状态" multiple collapse-tags clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button class="filter-item-button" type="primary" icon="el-icon-plus" @click="handleCreate">
            新增
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" :gutter="10" v-if="$isPermitted($store.getters.user, 'Customer_Audit_All')">
        <el-col :span="24">
          <el-button class="filter-item-button" type="primary" icon="el-icon-refresh" @click="syncCustomer" style="float: right; margin-right: 1%">
            同步CDMS数据
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'Name', order: 'ascending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass" @sort-change="sortChange">
            <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column fixed label="机构名称" sortable="custom" min-width="180px" header-align="center" align="left" prop="Name">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="简称" sortable="custom" min-width="120px" header-align="center" align="left" prop="ShortName">
              <template slot-scope="{ row }">
                <span>{{ row.shortName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="性质" sortable="custom" min-width="65px" header-align="center" align="center" prop="EnumType">
              <template slot-scope="{ row }">
                <span>{{ row.enumTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" sortable="custom" min-width="120px" header-align="center" align="left" prop="CustomerCategory.Name">
              <template slot-scope="{ row }">
                <span>{{row.customerCategoryParentName}}&nbsp;/&nbsp;{{row.customerCategoryName}}</span>
              </template>
            </el-table-column>
            <el-table-column label="地址" sortable="custom" min-width="180px" header-align="center" align="left" prop="Address">
              <template slot-scope="{ row }">
                <span>{{ row.address }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" sortable="custom" min-width="65px" header-align="center" align="center" prop="EnumStatus">
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数据状态" sortable="custom" width="94px" header-align="center" align="center" prop="EnumReviewState">
              <template slot-scope="{ row }">
                <span>{{ row.enumReviewStateDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="146" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <el-button type="text" title="查看" @click="handleView(row)">查看</el-button>
                <el-button v-if="row.enableEdit" type="text" title="编辑" @click="handleEdit(row)">编辑</el-button>
                <el-button v-if="row.enableFreeze" type="text" title="冻结" @click="handleFreeze(row)">冻结</el-button>
                <el-button v-if="row.enableUnfreeze" type="text" title="解冻" @click="handleUnFreeze(row)">解冻</el-button>
                <el-button v-if="row.enableDelete" type="text" title="删除" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <editCustomer v-if="dialogEditFormVisible" :id="itemId" :title="modifyDialogTitle" @hidden="onHidden()" @refresh="onRefresh()" />

    <el-dialog append-to-body :title="viewDialogTitle" :close-on-click-modal="false" :visible="dialogViewFormVisible" width="80%" @close="onHidden">
      <viewCustomer ref="refCustomer" :id="itemId" :show-dialog="dialogViewFormVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="onHidden"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import editCustomer from './components/editCustomer'
import viewCustomer from './components/viewCustomer'
import masterAPI from '@/api/masterData'
import selectorApi from '@/api/selector'
import sysApi from '@/api/sys'
import syncApi from '@/api/sync'

export default {
  name: 'Customer',
  components: {
    syncApi,
    Pagination,
    editCustomer,
    viewCustomer,
  },
  data() {
    return {
      span: 4,
      total: 0,

      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '+Name',
        statuses: [1, 10]
      },
      listLoading: false,
      list: [],
      statusList: [],
      typeList: [],
      dialogEditFormVisible: false,
      dialogViewFormVisible: false,
      modifyDialogTitle: '',
      viewDialogTitle: '',
      itemId: null,
    }
  },
  created() {
    // this.getMapBaseUrlForWeb()
    this.initTypeList()
    this.initStatusList()

    this.getList()
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      masterAPI.queryCustomer(this.listQuery)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.list = result.data.datas
            this.total = result.data.recordCount
            this.listQuery.pageIndex = result.data.pageIndex
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    // getMapBaseUrlForWeb() {
    //   if (!!!window.sessionStorage.getItem('MapBaseUrl')) {
    //     sysApi.getMapBaseUrlForWeb().then((result) => {
    //       window.sessionStorage.setItem('MapBaseUrl', result.data)
    //     })
    //       .catch((error) => {
    //         console.log(error)
    //       })
    //   }
    // },
    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'CustomerType' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.typeList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取机构状态枚举
    initStatusList() {
      var param = { enumType: 'CustomerStatus' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.statusList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },

    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop

      this.getList()
    },
    handleCreate() {
      this.itemId = null
      this.dialogEditFormVisible = true
      this.modifyDialogTitle = '新建机构'
    },
    handleEdit(row) {
      this.itemId = row.id
      this.dialogEditFormVisible = true
      this.modifyDialogTitle = '编辑机构'
    },
    handleView(row) {
      this.itemId = row.id
      this.dialogViewFormVisible = true
      this.viewDialogTitle = '查看机构'
    },
    handleDelete(row) {
      this.$confirm('确定删除机构?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        masterAPI.deleteCustomer(row).then(result => {
          if (result.succeed) {
            // 重新加载列表
            this.getList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (result.type !== -3) {
              this.$notice.resultTip(result)
            }
          }
        })
          .catch(error => {
            if (!error.processed) {
              this.$notice.message('删除失败', 'error')
            }
          })

      }).catch(error => {
        if (!error.succeed) {
        }
      })
    },
    handleFreeze(row) {
      this.$confirm('是否确定冻结?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        masterAPI.freezeCustomer(row).then((result) => {
          this.$message({ type: 'success', message: '冻结成功!' })

          this.getList()
        })
      }).catch((e) => {
        console.log(e)
      })
    },
    handleUnFreeze(row) {
      this.$confirm('是否确定解冻?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        masterAPI.unFreezeCustomer(row).then((result) => {
          this.$message({ type: 'success', message: '解冻成功!' })

          this.getList()
        })
      }).catch((e) => {
        console.log(e)
      })
    },
    syncCustomer() {
      syncApi.syncCustomer().then(res => {
        if (res.succeed) {
          this.$notice.message('同步成功！', 'success')
          this.handleFilter();
        }
      }).catch(error => {
        console.log(error)
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
      this.dialogViewFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
  }
}
</script>
