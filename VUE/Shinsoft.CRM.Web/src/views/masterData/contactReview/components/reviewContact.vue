<template>
  <div>
    <el-dialog ref="reviewCustomerForm" append-to-body :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible="showDialog" width="75%" @close="closeDialog()">
      <el-form :model="formData" ref="reviewForm" label-position="right" label-width="100px">
        <div style="text-align:center" v-if="dialogStatus == 'review'">
          <el-button size="small" icon="el-icon-check" style="background:#60db2c;color:#FFFFFF;margin-top:-20px" @click="submit(10)">
            通过
          </el-button>
          <el-button size="small" icon="el-icon-close" style="background:#fa0505;color:#FFFFFF" @click="submit(20)">拒绝
          </el-button>
        </div>
        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span>审批信息</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="span">
              <el-form-item label="类型">
                {{formData.enumReviewTypeDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="审批状态">
                {{formData.enumReviewStatusDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="提交人">
                {{formData.submitEmployeeName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="审批人">
                <span v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">{{formData.reviewEmployeeName}}</span>
                <span v-else>{{formData.auditorNames}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span" v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">
              <el-form-item label="审批时间">
                {{formData.reviewTime}}
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">
              <el-form-item label="审批意见">
                {{formData.reviewRemark}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span>客户基本信息</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="24" v-if="formData.newData.customerNames">
              <el-form-item label="关联机构" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.customerNames != formData.oriData.customerNames" :content="!!formData.oriData.customerNames ? formData.oriData.customerNames : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.customerNames ? formData.newData.customerNames : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.customerNames ? formData.newData.customerNames : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="默认关联机构" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.customerName != formData.oriData.customerName" :content="!!formData.oriData.customerName ? formData.oriData.customerName : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.customerName ? formData.newData.customerName : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.customerName ? formData.newData.customerName : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="姓名" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.name != formData.oriData.name" :content="formData.oriData.name">
                  <span style="color:#e43d33;">{{formData.newData.name}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.name}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="性别" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.enumGenderDesc != formData.oriData.enumGenderDesc" :content="formData.oriData.enumGenderDesc">
                  <span style="color:#e43d33;">{{formData.newData.enumGenderDesc}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.enumGenderDesc}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="标签" name="enumType" v-if="!!formData.newData.enumFlagsDesc || (formData.oriData && !!formData.oriData.enumFlagsDesc)">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.enumFlagsDesc != formData.oriData.enumFlagsDesc" :content="!!formData.oriData.enumFlagsDesc ? formData.oriData.enumFlagsDesc : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.enumFlagsDesc ? formData.newData.enumFlagsDesc : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.enumFlagsDesc ? formData.newData.enumFlagsDesc : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="科室/部门" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.dept != formData.oriData.dept" :content="formData.oriData.dept">
                  <span style="color:#e43d33;">{{formData.newData.dept}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.dept}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职称" v-if="(formData.newData.enumFlags & 1) == 0 || (formData.oriData && (formData.oriData.enumFlags & 1) == 0)" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.title != formData.oriData.title" :content="!!formData.oriData.title ? formData.oriData.title : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.title ? formData.newData.title : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.title ? formData.newData.title : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职务" v-if="(formData.newData.enumFlags & 1) == 0 || (formData.oriData && (formData.oriData.enumFlags & 1) == 0)" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.position != formData.oriData.position" :content="!!formData.oriData.position ? formData.oriData.position : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.position ? formData.newData.position : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.position ? formData.newData.position : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职称" v-if="(formData.newData.enumFlags & 1) > 0 || (formData.oriData && (formData.oriData.enumFlags & 1) > 0)" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.titleName != formData.oriData.titleName" :content="!!formData.oriData.titleName ? formData.oriData.titleName : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.titleName ? formData.newData.titleName : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.titleName ? formData.newData.titleName : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职务" v-if="(formData.newData.enumFlags & 1) > 0 || (formData.oriData && (formData.oriData.enumFlags & 1) > 0)" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.positionName != formData.oriData.positionName" :content="!!formData.oriData.positionName ? formData.oriData.positionName : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.positionName ? formData.newData.positionName : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.positionName ? formData.newData.positionName : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="医生阶段" v-if="(formData.newData.enumFlags & 1) > 0 || (formData.oriData && (formData.oriData.enumFlags & 1) > 0)" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.doctorStageName != formData.oriData.doctorStageName" :content="!!formData.oriData.doctorStageName ? formData.oriData.doctorStageName : '无'">
                  <span style="color:#e43d33;">{{!!formData.newData.doctorStageName ? formData.newData.doctorStageName : '无'}}</span>
                </el-tooltip>
                <span v-else>{{!!formData.newData.doctorStageName ? formData.newData.doctorStageName : '无'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="电话" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.mobile != formData.oriData.mobile" :content="formData.oriData.mobile">
                  <span style="color:#e43d33;">{{formData.newData.mobile}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.mobile}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="邮箱" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.email != formData.oriData.email" :content="formData.oriData.email">
                  <span style="color:#e43d33;">{{formData.newData.email}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.email}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="创建人">
                {{formData.newData.creatorEmployeeName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="状态">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.enumStatusDesc != formData.oriData.enumStatusDesc" :content="formData.oriData.enumStatusDesc">
                  <span style="color:#e43d33;">{{formData.newData.enumStatusDesc}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.enumStatusDesc}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.remark != formData.oriData.remark" :content="formData.oriData.remark">
                  <span style="color:#e43d33;">{{formData.newData.remark}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.remark}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span>产品线</span>
          </div>
          <el-row>
            <el-col :span="24">
              <el-table :data="formData.newData.productLines" stripe border fit highlight-current-row style="width: 100%" :header-cell-class-name="'tableStyle'">
                <el-table-column fixed type="index" width="80" label="序号" align="center"></el-table-column>
                <el-table-column label="产品线名称" min-width="150px" header-align="center" align="left">
                  <template slot-scope="{ row }">
                    <span>{{ row.productLineName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="标签" min-width="150px" header-align="center" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.enumContactFlagsDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="讲者级别" min-width="150px" header-align="center" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.speakerLevelName }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-if="(formData.newData.enumFlags & 1) > 0" label="医生级别" min-width="150px" header-align="center" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.doctorLevelName }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-card>

        <el-card v-if="dialogStatus == 'review'">
          <el-form-item label="审批意见">
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" v-model="formData.reviewRemark" placholder="审批意见"></el-input>
          </el-form-item>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer" :style="dialogStatus == 'review' ? 'text-align:center':''">
        <el-button v-if="dialogStatus == 'review'" size="small" icon="el-icon-check" style="background:#60db2c;color:#FFFFFF;margin-top:-20px" @click="submit(10)">
          通过
        </el-button>
        <el-button v-if="dialogStatus == 'review'" size="small" icon="el-icon-close" style="background:#fa0505;color:#FFFFFF" @click="submit(20)">拒绝
        </el-button>
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";


export default {
  name: '',
  components: {
  },
  props: {

  },
  data() {
    return {
      span: 8,
      showDialog: false,
      dialogStatus: "",
      textMap: {
        view: "查看",
        review: "审批"
      },

      formData: { newData: { defaultContact: {}, }, oriData: {} },
      activeName: 'customerInfo',
    }
  },
  watch: {

  },
  mounted() {

  },
  created() {

  },
  methods: {
    //初始化
    init(item, action) {
      this.showDialog = true
      this.dialogStatus = action
      if (item) {
        masterAPI.getContactReview({ id: item.id }).then((result) => {
          if (result.succeed) {
            this.formData = result.data;
          }
        });
      }
    },
    submit(status) {
      var that = this;
      if (!this.formData.reviewRemark && status == 20) {
        this.$notice.message('审批拒绝时必须在页面底部输入审批意见', 'error')
        return;
      }

      var msg = status == 10 ? '确定审批通过此客户?' : '确定审批拒绝此客户?'

      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      }).then(() => {
        var par = {
          id: this.formData.id,
          enumReviewStatus: status,
          reviewRemark: this.formData.reviewRemark
        }

        masterAPI.auditContactReview(par).then(result => {
          this.$emit("refreshList")
          this.showDialog = false
        }).catch((error) => { })
      }).catch((error) => {
      })
    },

    cancle() {
      this.showDialog = false
    },
    closeDialog() {
      this.showDialog = false
    },
  }
}
</script>
