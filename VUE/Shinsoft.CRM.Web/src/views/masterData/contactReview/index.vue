<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <el-input v-model="listQuery.contactName" clearable placeholder="客户名称" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-input v-model="listQuery.ownerCustomerName" clearable placeholder="默认关联机构名称" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.reviewStatuses" class="filter-item" placeholder="状态" multiple collapse-tags clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <SelectEmployeeDialog :title="'选择提交人'" :placeholder="'选择提交人'" :value.sync="listQuery.submitEmployeeId"></SelectEmployeeDialog>
        </el-col>
        <el-col :span="5">
          <el-date-picker style="width: 100%" clearable value-format="yyyy-MM-dd" v-model="listQuery.reviewTimes" type="daterange" range-separator="至" start-placeholder="审批开始日期" end-placeholder="审批结束日期">
          </el-date-picker>
        </el-col>
        <el-col :span="3">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'CreateTime', order: 'ascending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass" @sort-change="sortChange">
            <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column fixed label="默认关联机构" sortable="custom" min-width="180px" header-align="center" align="left" prop="ReviewIndex.ReviewExtInfo.OwnerCustomer.Name">
              <template slot-scope="{ row }">
                <span>{{ row.ownerCustomerName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="客户名称" sortable="custom" min-width="100px" header-align="center" align="left" prop="ReviewIndex.ReviewExtInfo.Name">
              <template slot-scope="{ row }">
                <span>{{ row.contactName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" sortable="custom" min-width="60px" header-align="center" align="center" prop="EnumReviewStatus">
              <template slot-scope="{ row }">
                <span>{{ row.enumReviewStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" sortable="custom" min-width="60px" header-align="center" align="center" prop="ReviewIndex.EnumReviewType">
              <template slot-scope="{ row }">
                <span>{{ row.enumReviewTypeDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="提交人" sortable="custom" min-width="60px" header-align="center" align="left" prop="ReviewIndex.SubmitEmployee.DisplayName">
              <template slot-scope="{ row }">
                <span>{{ row.submitEmployeeName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="提交时间" sortable="custom" width="120px" header-align="center" align="center" prop="CreateTime">
              <template slot-scope="{ row }">
                <span>{{ row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审批人" sortable="custom" min-width="60px" header-align="center" align="left" prop="auditorNames">
              <template slot-scope="{ row }">
                <span>{{ row.auditorNames }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审批时间" sortable="custom" width="120px" header-align="center" align="center" prop="ReviewTime">
              <template slot-scope="{ row }">
                <span>{{ row.reviewTime }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="90px" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <el-button type="text" title="详情" @click="handleView(row)">详情</el-button>
                <el-button v-if="row.enableAudit" type="text" title="审批" @click="handleReview(row)">审批</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <reviewContact ref="reviewPage" @refreshList="getList"></reviewContact>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import reviewContact from './components/reviewContact.vue'
import masterAPI from '@/api/masterData'
import SelectorApi from '@/api/selector'

export default {
  computed: {
    user() {
      return this.$store.getters.user
    }
  },
  name: 'ContactReviewIndex',
  components: {
    Pagination,
    reviewContact
  },
  data() {
    return {
      span: 4,
      total: 0,

      statusList: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: "+CreateTime",
        reviewStatuses: [1, 2],
        reviewTimes: []
      },
      listLoading: false,
      list: [],
      itemId: null

    }
  },
  created() {
    this.initStatusList()

    this.getList()
  },
  methods: {
    // 获取审批状态枚举
    initStatusList() {
      var param = { enumType: 'ReviewStatus' }
      SelectorApi.getEnumInfos(param)
        .then((result) => {
          this.statusList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },

    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      if (this.listQuery.reviewTimes == null) {
        this.listQuery.reviewTimes = []
      }
      masterAPI.queryContactReview(this.listQuery).then((result) => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    sizeChange(val) {
      this.listQuery.pageSize = val
      this.handleFilter()
    },
    handleView(row) {
      this.$refs.reviewPage.init(row, 'view')
    },
    onRefresh() {
      this.getList()
    },

    handleReview(row) {
      this.$refs.reviewPage.init(row, 'review')
    }

  }
}
</script>
