<template>
  <div>
    <el-dialog ref="reviewCustomerForm" append-to-body :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible="showDialog" width="80%" @close="closeDialog()">
      <el-form :model="formData" ref="reviewForm" label-position="right" label-width="80px">
        <div style="text-align:center" v-if="dialogStatus == 'review'">
          <el-button size="small" icon="el-icon-check" style="background:#60db2c;color:#FFFFFF;margin-top:-20px" @click="submit(10)">
            通过
          </el-button>
          <el-button size="small" icon="el-icon-close" style="background:#fa0505;color:#FFFFFF" @click="submit(20)">拒绝
          </el-button>
        </div>
        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span>审批信息</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="span">
              <el-form-item label="类型">
                {{formData.enumReviewTypeDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="审批状态">
                {{formData.enumReviewStatusDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="提交人">
                {{formData.submitEmployeeName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="审批人">
                <span v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">{{formData.reviewEmployeeName}}</span>
                <span v-else>{{formData.auditorNames}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span" v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">
              <el-form-item label="审批时间">
                {{formData.reviewTime}}
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="formData.enumReviewStatus != 1 && formData.enumReviewStatus != 2">
              <el-form-item label="审批意见">
                {{formData.reviewRemark}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span>机构信息</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="机构名称" name="name">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.name != formData.oriData.name" :content="formData.oriData.name">
                  <span style="color:#e43d33;">{{formData.newData.name}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.name}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="机构简称">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.shortName != formData.oriData.shortName" :content="formData.oriData.shortName">
                  <span style="color:#e43d33;">{{formData.newData.shortName}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.shortName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="机构性质" name="enumType">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.enumTypeDesc != formData.oriData.enumTypeDesc" :content="formData.oriData.enumTypeDesc">
                  <span style="color:#e43d33;">{{formData.newData.enumTypeDesc}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.enumTypeDesc}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="机构类型" name="customerCategoryId">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.customerCategoryName != formData.oriData.customerCategoryName" :content="formData.oriData.customerCategoryParentName+'/'+formData.oriData.customerCategoryName">
                  <span style="color:#e43d33;">{{formData.newData.customerCategoryParentName}}/{{formData.newData.customerCategoryName}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.customerCategoryParentName}}/{{formData.newData.customerCategoryName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item v-if="formData.newData.enumType == 1" label="医院等级" name="gradeDictId">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.gradeName != formData.oriData.gradeName" :content="formData.oriData.gradeName">
                  <span style="color:#e43d33;">{{formData.newData.gradeName}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.gradeName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item v-if="formData.newData.enumType == 1" label="医院等次" name="rankDictId">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.rankName != formData.oriData.rankName" :content="formData.oriData.rankName">
                  <span style="color:#e43d33;">{{formData.newData.rankName}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.rankName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="创建人">
                {{formData.newData.creatorEmployeeName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="机构状态">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.enumStatusDesc != formData.oriData.enumStatusDesc" :content="formData.oriData.enumStatusDesc">
                  <span style="color:#e43d33;">{{formData.newData.enumStatusDesc}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.enumStatusDesc}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址">
                <el-tooltip effect="light" v-if="formData.enumReviewType == 2 && formData.newData.address != formData.oriData.address" :content="formData.oriData.address">
                  <span style="color:#e43d33;">{{formData.newData.address}}</span>
                </el-tooltip>
                <span v-else>{{formData.newData.address}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <SelectMapDialog :showText="false" :addressLocX="formData.newData.addressLocX" :addressLocY="formData.newData.addressLocY"></SelectMapDialog>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px" v-if="formData.enumReviewType == 1 && formData.newData.defaultContact && formData.newData.defaultContact.id">
          <div slot="header" class="clearfix">
            <span>客户</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="span">
              <el-form-item label="姓名">
                {{formData.newData.defaultContact.name}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="性别">
                {{formData.newData.defaultContact.enumGenderDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="标签">
                {{formData.newData.defaultContact.enumFlagsDesc}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="科室/部门">
                {{formData.newData.defaultContact.dept}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职务" v-if="(formData.newData.defaultContact.enumFlags & 1) == 0">
                {{formData.newData.defaultContact.title}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职称" v-if="(formData.newData.defaultContact.enumFlags & 1) > 0">
                {{formData.newData.defaultContact.titleName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="职务" v-if="(formData.newData.defaultContact.enumFlags & 1) > 0">
                {{formData.newData.defaultContact.positionName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="医生级别" v-if="(formData.newData.defaultContact.enumFlags & 1) > 0">
                {{formData.newData.defaultContact.levelName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="讲者级别" v-if="(formData.newData.defaultContact.enumFlags & 2) > 0">
                {{formData.newData.defaultContact.speakerLevelName}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="产品线">
                {{formData.newData.defaultContact.productLineNames}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="电话">
                {{formData.newData.defaultContact.mobile}}
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="邮箱">
                {{formData.newData.defaultContact.email}}
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注">
                {{formData.newData.defaultContact.remark}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card v-if="dialogStatus == 'review'">
          <el-form-item label="审批意见">
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" v-model="formData.reviewRemark" placholder="审批意见"></el-input>
          </el-form-item>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer" :style="dialogStatus == 'review' ? 'text-align:center':''">
        <el-button v-if="dialogStatus == 'review'" size="small" icon="el-icon-check" style="background:#60db2c;color:#FFFFFF;margin-top:-20px" @click="submit(10)">
          通过
        </el-button>
        <el-button v-if="dialogStatus == 'review'" size="small" icon="el-icon-close" style="background:#fa0505;color:#FFFFFF" @click="submit(20)">拒绝
        </el-button>
        <el-button icon="el-icon-close" @click="closeDialog"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";


export default {
  name: '',
  components: {
  },
  props: {

  },
  data() {
    return {
      span: 8,
      showDialog: false,
      dialogStatus: "",
      textMap: {
        view: "查看",
        review: "审批"
      },

      formData: { newData: { defaultContact: {}, }, oriData: {} },
      activeName: 'customerInfo',
    }
  },
  watch: {

  },
  mounted() {

  },
  created() {

  },
  methods: {
    //初始化
    init(item, action) {
      this.formData = { newData: { defaultContact: {}, }, oriData: {} }
      this.showDialog = true
      this.dialogStatus = action
      if (item) {
        masterAPI.getCustomerReview({ id: item.id }).then((result) => {
          if (result.succeed) {
            this.formData = result.data;
            this.$forceUpdate()
          }
        });
      }
    },
    submit(status) {
      var that = this;
      if (!this.formData.reviewRemark && status == 20) {
        this.$notice.message('审批拒绝时必须在页面底部输入审批意见', 'error')
        return;
      }

      var msg = status == 10 ? '确定审批通过此机构?' : '确定审批拒绝此机构?'

      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      }).then(() => {
        var par = {
          id: this.formData.id,
          enumReviewStatus: status,
          reviewRemark: this.formData.reviewRemark
        }

        masterAPI.auditCustomerReview(par).then(result => {
          this.$emit("refreshList")
          this.showDialog = false
        }).catch((error) => { })
      }).catch((error) => {
      })
    },

    cancle() {
      this.showDialog = false
    },
    closeDialog() {
      this.formData = { newData: { defaultContact: {}, }, oriData: {} }
      this.showDialog = false
    },
  }
}
</script>
