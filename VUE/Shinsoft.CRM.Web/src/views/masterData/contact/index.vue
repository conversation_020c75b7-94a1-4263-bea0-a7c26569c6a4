// 客户
<template>
  <div>
    <div class="search-container-bg">
      <el-row :gutter="10" class="filter-container" type="flex">
        <el-col :span="span">
          <SelectCustomerDialog :title="'选择机构'" :placeholder="'选择机构'" :value.sync="listQuery.customerId"></SelectCustomerDialog>
        </el-col>
        <el-col :span="span">
          <el-input v-model="listQuery.Name" clearable placeholder="姓名" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-input v-model="listQuery.Mobile" clearable placeholder="电话" class="filter-item" @keyup.enter.native="handleFilter" />
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.titleDictIds" class="filter-item" style="width:100%" placeholder="请选择职称" multiple collapse-tags clearable>
            <el-option v-for="item in titleLevels" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col :span="span">
          <el-select v-model="listQuery.Statuses" class="filter-item" placeholder="状态" multiple collapse-tags clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button v-if="isEditPermission" class="filter-item-button" type="primary" icon="el-icon-plus" @click="handleCreate">
            新增
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" :gutter="10" v-if="$isPermitted($store.getters.user, 'Contact_Audit_All')">
        <el-col :span="24">
          <el-button class="filter-item-button" type="primary" icon="el-icon-refresh" @click="syncContact" style="float: right; margin-right: 1%">
            同步CDMS数据
          </el-button>
        </el-col>
      </el-row>
    </div>
    <div class="list-container">
      <el-row>
        <el-col :span="24">
          <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'Name', order: 'ascending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass" @sort-change="sortChange">
            <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
            <el-table-column fixed label="默认关联机构" sortable="custom" min-width="180px" header-align="center" align="left" prop="Customer.Name">
              <template slot-scope="{ row }">
                <span>{{ row.customerName }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed label="姓名" sortable="custom" min-width="90px" header-align="center" align="left" prop="Name">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="性别" sortable="custom" min-width="80px" header-align="center" align="center" prop="EnumGender">
              <template slot-scope="{ row }">
                <span>{{ row.enumGender == "1" ? "男" : "女" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" sortable="custom" min-width="100px" header-align="center" align="left" prop="Dept">
              <template slot-scope="{ row }">
                <span>{{ row.dept }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职称" sortable="custom" min-width="100px" header-align="center" align="left" prop="Title">
              <template slot-scope="{ row }">
                <span>{{ row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务" sortable="custom" min-width="100px" header-align="center" align="left" prop="Title">
              <template slot-scope="{ row }">
                <span>{{ row.position }}</span>
              </template>
            </el-table-column>
            <el-table-column label="电话" sortable="custom" min-width="100px" header-align="center" align="left" prop="Mobile">
              <template slot-scope="{ row }">
                <span>{{ row.mobile }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Email" sortable="custom" min-width="100px" header-align="center" align="left" prop="Email">
              <template slot-scope="{ row }">
                <span>{{ row.email }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" sortable="custom" min-width="80px" header-align="center" align="center" prop="EnumStatus">
              <template slot-scope="{ row }">
                <span>{{ row.enumStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数据状态" sortable="custom" width="100px" header-align="center" align="center" prop="EnumReviewState">
              <template slot-scope="{ row }">
                <span>{{ row.enumReviewStateDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="{ row }">
                <el-button type="text" title="详细信息" @click="handleView(row)">详情</el-button>
                <el-button v-if="row.enableEdit" type="text" title="编辑" @click="handleEdit(row)">编辑</el-button>
                <el-button v-if="row.enableUpgradeSpeaker" type="text" title="升级讲者" @click="handleUpgradeSpeaker(row)">升级讲者</el-button>
                <el-button v-if="row.enableEdit && !row.cdmsKey" type="text" title="删除" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col class="el-colRight">
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
        </el-col>
      </el-row>
    </div>

    <editContact v-if="dialogEditFormVisible" :id="itemId" :title="modifyDialogTitle" @hidden="onHidden()" @refresh="onRefresh()" />

    <upgradeSpeaker v-if="dialogUpgradeSpeakerFormVisible" :id="itemId" :name="itemName" :title="'升级讲者'" @hidden="onHidden()" @refresh="onRefresh()" />

    <el-dialog append-to-body :title="viewDialogTitle" :close-on-click-modal="false" :visible="dialogViewFormVisible" width="75%" @close="onHidden">
      <viewContact ref="refContact" :id="itemId" :show-dialog="dialogViewFormVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="onHidden"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import masterAPI from '@/api/masterData'
import selectorApi from '@/api/selector'
import editContact from './components/editContact.vue'
import viewContact from './components/viewContact.vue'
import upgradeSpeaker from './components/upgradeSpeaker'
import syncApi from '@/api/sync'

export default {
  computed: {
    user() {
      return this.$store.getters.user
    }
  },
  name: 'Contact',
  components: {
    syncApi,
    Pagination,
    editContact,
    viewContact,
    upgradeSpeaker,
  },
  data() {
    return {
      span: 4,
      total: 0,
      statusList: [],
      titleLevels: [],
      typeList: [],

      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '+Name',
      },
      listLoading: false,
      list: [],
      typeList: [],
      dialogEditFormVisible: false,
      dialogViewFormVisible: false,
      dialogUpgradeSpeakerFormVisible: false,
      modifyDialogTitle: '',
      viewDialogTitle: '',
      itemId: null,
      itemName: '',
      isEditPermission: false,
    }
  },
  created() {
    this.isEditPermission = this.$isPermitted(this.$store.getters.user, 'Contact_Maintain_All')
      || this.$isPermitted(this.$store.getters.user, 'Contact_Maintain_Owner');
    this.initTypeList()
    this.initStatusList()
    this.initTitleLevels()

    this.getList()
  },
  methods: {
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      masterAPI.queryContact(this.listQuery).then((result) => {
        this.listLoading = false
        if (result.succeed) {
          this.list = result.data.datas
          this.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    // 获取客户类型枚举
    initTypeList() {
      var param = { enumType: 'ContactType' }
      selectorApi
        .getEnumInfos(param)
        .then((result) => {
          this.typeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取客户状态枚举
    initStatusList() {
      var param = { enumType: 'ContactStatus' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.statusList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    initTitleLevels() {
      var param = { parentCode: "Contact_Doctor_Title" };
      selectorApi.getDicts(param).then((result) => {
        this.titleLevels = result.data;
      })
        .catch((error) => {
          console.log(error)
        })
    },

    indexMethod(index) {
      return (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop

      this.getList()
    },
    handleCreate() {
      this.itemId = null
      this.dialogEditFormVisible = true
      this.modifyDialogTitle = '新建客户'
    },
    handleEdit(row) {
      this.itemId = row.id
      this.dialogEditFormVisible = true
      this.modifyDialogTitle = '编辑客户'
    },
    handleUpgradeSpeaker(row) {
      this.itemId = row.id
      this.itemName = row.name
      this.dialogUpgradeSpeakerFormVisible = true
    },
    handleView(row) {
      this.itemId = row.id
      this.dialogViewFormVisible = true
      this.viewDialogTitle = '查看客户'
    },
    handleDelete(row) {
      this.$confirm('确定删除客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        masterAPI.deleteContact(row).then(result => {
          if (result.succeed) {
            // 重新加载列表
            this.getList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (result.type !== -3) {
              this.$notice.resultTip(result)
            }
          }
        })
          .catch(error => {
            if (!error.processed) {
              this.$notice.message('删除失败', 'error')
            }
          })

      }).catch(error => {
        if (!error.succeed) {
        }
      })
    },
    syncContact() {
      syncApi.syncContact().then(res => {
        if (res.succeed) {
          this.$notice.message('同步成功！', 'success')
          this.handleFilter();
        }
      }).catch(error => {
        console.log(error)
      })
    },
    onHidden() {
      this.dialogEditFormVisible = false
      this.dialogViewFormVisible = false
      this.dialogUpgradeSpeakerFormVisible = false
    },
    onRefresh() {
      this.getList()
      this.dialogEditFormVisible = false
      this.dialogUpgradeSpeakerFormVisible = false
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
  }
}
</script>
