<template>
  <div>
    <el-dialog ref="dataCustomerForm" append-to-body :close-on-click-modal="true" :title="'选择产品线'" :visible="showDialog" width="40%" @close="closeDialog()">

      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'Name', order: 'ascending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
              <el-table-column label="序号" type="index" align="center" :index="indexMethod" />
              <el-table-column label="产品线名称" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.productLineName }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="60" min-width="90" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
                </template>
              </el-table-column>
            </el-table>

          </el-col>
        </el-row>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import SelectorApi from '@/api/selector'
import masterAPI from '@/api/masterData'

export default {
  components: {
  },
  props: {

  },
  data() {
    return {
      span: 5,
      listLoading: true,
      list: [],
      showDialog: false,
    }
  },
  watch: {

  },
  created() {
    this.handleFilter()
  },
  methods: {
    init() {
      this.showDialog = true
    },

    handleFilter() {
      this.getList()
    },
    getList() {
      this.listLoading = true
      masterAPI.queryContactProductLineSelector()
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.list = result.data;
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    indexMethod(index) {
      return (
        index + 1
      )
    },
    handleCheck(row) {
      this.$emit('itemSelected', row)
    },
    closeDialog() {
      this.showDialog = false
    }

  }
}
</script>

