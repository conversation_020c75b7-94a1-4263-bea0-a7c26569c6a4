<template>
  <div>
    <el-dialog append-to-body :title="title" width="400px" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form :model="tempFormModel" :rules="rules" ref="dataForm" label-position="right" label-width="100px">
        <el-form-item label="姓名">
          {{name}}
        </el-form-item>
        <el-form-item label="讲者级别" prop="speakerLevelDictId">
          <el-select :key="300000008" v-model="tempFormModel.speakerLevelDictId" class="filter-item" style="width:100%" placeholder="请选择讲者级别" collapse-tags>
            <el-option v-for="item in speakerLevels" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close " @click="cancle() "> 关闭 </el-button>
        <el-button :loading="btnSaveLoading " type="primary " icon="el-icon-check " @click="save() ">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";

export default {
  name: '',
  components: {

  },
  props: {
    id: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      span: 12,
      rules: {
        speakerLevelDictId: [
          { required: true, message: '必须选择讲者级别', trigger: 'change' },
        ],
      },
      tempFormModel: { id: '' },
      btnSaveLoading: false,

      speakerLevels: [],
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    },
  },
  mounted() {
  },
  created() {
    this.initSpeakerLevels()
  },
  methods: {
    initSpeakerLevels() {
      var param = { parentCode: "Contact_Speaker_Level" };
      selectorAPI.getDicts(param).then((result) => {
        this.speakerLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.upgradeSpeaker()
        }
      })
    },
    upgradeSpeaker() {
      this.btnSaveLoading = true
      this.tempFormModel.id = this.id
      masterAPI.upgradeSpeaker(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('升级讲者成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('升级讲者失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
  }

}
</script>
