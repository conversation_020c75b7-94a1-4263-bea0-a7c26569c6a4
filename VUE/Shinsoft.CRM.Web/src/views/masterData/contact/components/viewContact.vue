<template>
  <div>
    <el-form ref="dataForm" :model="formData" label-position="right" label-width="80px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="姓名" name="name">
              {{formData.name}}
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formData.mergeToName">
            <el-form-item>
              <span slot="label">
                已合并至
              </span>
              <span style="color:#66A9FE;text-decoration:underline;cursor:pointer;" @click="handleViewMergeTo(formData)">{{ formData.mergeToName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" name="enumGender">
              {{formData.enumGenderDesc}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标签">
              {{!!formData.enumFlagsDesc ? formData.enumFlagsDesc : '无'}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科室/部门" name="dept">
              {{formData.dept}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职称" name="title" v-if="(formData.enumFlags & 1) == 0">
              {{formData.title}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职务" name="position" v-if="(formData.enumFlags & 1) == 0">
              {{formData.position}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职称" v-if="(formData.enumFlags & 1) > 0">
              {{formData.titleName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职务" v-if="(formData.enumFlags & 1) > 0">
              {{formData.positionName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="医生阶段" v-if="(formData.enumFlags & 1) > 0">
              {{formData.doctorStageName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              {{formData.enumStatusDesc}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据状态">
              {{formData.enumReviewStateDesc}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电话">
              {{formData.mobile}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮箱">
              {{formData.email}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              {{formData.remark}}
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top:10px">
        <div slot="header" class="clearfix">
          <span>产品线</span>
        </div>
        <el-row>
          <el-col :span="24">
            <el-table :data="formData.productLines" stripe border fit highlight-current-row style="width: 100%" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
              <el-table-column fixed type="index" width="80" label="序号" align="center"></el-table-column>
              <el-table-column label="产品线名称" min-width="150px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.productLineName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签" min-width="150px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumContactFlagsDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="讲者级别" min-width="150px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.speakerLevelName }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="(formData.enumFlags & 1) > 0" label="医生级别" min-width="150px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.doctorLevelName }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top:10px">
        <div slot="header" class="clearfix">
          <span>关联机构</span>
        </div>
        <el-row>
          <el-col :span="24">
            <el-table :data="formData.customers" stripe border fit highlight-current-row style="width: 100%" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
              <el-table-column fixed type="index" width="80" label="序号" align="center"></el-table-column>
              <el-table-column label="机构名称" min-width="150px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.customerName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否默认" min-width="150px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.customerId == formData.customerId ? '是':'否' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

  </div>
</template>
<script>
import masterAPI from "@/api/masterData";

export default {
  name: '',
  components: {
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 8,
      formData: {}
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function (val) {
        if (val === true) {
          this.formData.id = this.id
          this.get()
        }
      }
    }
  },
  mounted() {
  },
  created() {

  },
  methods: {
    get() {
      masterAPI.getContact({ id: this.formData.id }).then(result => {
        if (result.succeed) {
          this.formData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    cancle() {
      this.$emit('close')
    },
    handleViewMergeTo() {
      this.formData.id = this.formData.mergeToId
      this.get()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
  }
}
</script>

