<template>
  <div>
    <el-dialog append-to-body :title="title" width="75%" :close-on-click-modal="false" :visible="true" @close="cancle()">
      <el-form :model="tempFormModel" :rules="rules" ref="dataForm" label-position="right" label-width="100px">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <el-row :gutter="10">
            <el-col :span="span">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="tempFormModel.name" clearable show-word-limit placeholder="请输入客户姓名" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="性别" prop="enumGender">
                <el-radio-group v-model="tempFormModel.enumGender">
                  <el-radio :label="1" :key="1">男</el-radio>
                  <el-radio :label="2" :key="2">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="contactFlags && contactFlags.length > 0">
              <el-form-item label="标签">
                <el-checkbox-group v-model="tempFormModel.selectFlags">
                  <el-checkbox v-for="item in contactFlags" :label="item.value" :key="item.value" :disabled="item.disabled">{{ item.desc }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="span" :key="10002">
              <el-form-item label="科室/部门" prop="dept">
                <el-input v-model="tempFormModel.dept" clearable show-word-limit placeholder="请输入科室/部门" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span" :key="10003" v-if="!tempFormModel.selectFlags.find(v=>v == 1)">
              <el-form-item label="职称">
                <el-input v-model="tempFormModel.title" clearable show-word-limit placeholder="请输入职称" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span" :key="100031" v-if="!tempFormModel.selectFlags.find(v=>v == 1)">
              <el-form-item label="职务">
                <el-input v-model="tempFormModel.position" clearable show-word-limit placeholder="请输入职务" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span" :key="10004" v-if="tempFormModel.selectFlags.find(v=>v == 1)">
              <el-form-item label="职称" prop="titleDictId" :rules="tempFormModel.selectFlags.find(v=>v == 1) ? rules.titleDictId : []">
                <el-select :key="300000004" v-model="tempFormModel.titleDictId" class="filter-item" style="width:100%" placeholder="请选择职称" collapse-tags>
                  <el-option v-for="item in titleLevels" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="span" :key="10005" v-if="tempFormModel.selectFlags.find(v=>v == 1)">
              <el-form-item label="职务" prop="positionDictId" :rules="tempFormModel.selectFlags.find(v=>v == 1) ? rules.positionDictId : []">
                <el-select :key="300000005" v-model="tempFormModel.positionDictId" class="filter-item" style="width:100%" placeholder="请选择职务" collapse-tags>
                  <el-option v-for="item in positions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="span" :key="10008" v-if="tempFormModel.selectFlags.find(v=>v == 1)">
              <el-form-item label="医生阶段" prop="doctorStageDictId" :rules="tempFormModel.selectFlags.find(v=>v == 1) ? rules.doctorStageDictId : []">
                <el-select :key="300000008" v-model="tempFormModel.doctorStageDictId" class="filter-item" style="width:100%" placeholder="请选择医生阶段" collapse-tags>
                  <el-option v-for="item in doctorStages" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="span">
              <el-form-item label="电话" prop="mobile">
                <el-input v-model="tempFormModel.mobile" clearable show-word-limit placeholder="请输入电话" maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="span">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="tempFormModel.email" clearable show-word-limit placeholder="请输入邮箱" maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input type="textarea" :rows="3" v-model="tempFormModel.remark" clearable show-word-limit placeholder="请输入备注" maxlength="500" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span style="color:red">*</span> &nbsp;
            <span>产品线</span>
            <el-button type="primary" icon="el-icon-plus eltablei" style="float: right; margin-top: -5px; margin-right: 40px" @click="showAddProductLine">添加产品线</el-button>
          </div>
          <el-row>
            <el-col :span="24">
              <el-table :data="tempFormModel.productLines" stripe border fit highlight-current-row style="width: 100%" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
                <el-table-column fixed type="index" width="80" label="序号" align="center"></el-table-column>
                <el-table-column label="产品线名称" min-width="150px" header-align="center" align="left">
                  <template slot-scope="{ row }">
                    <span>{{ row.productLineName }}</span>
                  </template>
                </el-table-column>
                <el-table-column :key="3000000062222" v-if="tempFormModel.selectFlags.find(v=>v == 1)" label="医生级别" min-width="150px" header-align="center" align="left" :render-header="addRedStar">
                  <template slot-scope="{ row }">
                    <el-select v-model="row.doctorLevelDictId" class="filter-item" style="width:100%" placeholder="请选择医生级别" collapse-tags>
                      <el-option v-for="item in doctorLevels" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" header-align="center" width="150" class-name="small-padding fixed-width">
                  <template slot-scope="{ row }">
                    <el-button type="text" title="删除" @click="handleDeleteProductLine(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card" style="margin-top:10px">
          <div slot="header" class="clearfix">
            <span style="color:red">*</span> &nbsp;
            <span>关联机构</span>
            <el-button type="primary" icon="el-icon-plus eltablei" style="float: right; margin-top: -5px; margin-right: 40px" @click="showAddContactCustomer">添加关联机构</el-button>
          </div>
          <el-row>
            <el-col :span="24">
              <el-table :data="tempFormModel.customers" stripe border fit highlight-current-row style="width: 100%" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
                <el-table-column fixed type="index" width="80" label="序号" align="center"></el-table-column>
                <el-table-column label="机构名称" min-width="150px" header-align="center" align="left">
                  <template slot-scope="{ row }">
                    <span>{{ row.customerName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="是否默认" min-width="150px" header-align="center" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.isDefault ? '是':'否' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" header-align="center" width="150" class-name="small-padding fixed-width">
                  <template slot-scope="{ row }">
                    <el-button type="text" title="删除" @click="handleDeleteContactCustomer(row)">删除</el-button>
                    <el-button v-if="!row.isDefault" type="text" title="设置为默认机构" @click="handleSetDefaultCustomer(row)">设置为默认机构</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-card>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close " @click="cancle() "> 关闭 </el-button>
        <el-button :loading="btnSaveLoading " type="primary " icon="el-icon-check " @click="save() ">
          保存
        </el-button>
      </div>
    </el-dialog>

    <SelectCustomer ref="selectCustomerPage" @itemSelected="customerSelected"></SelectCustomer>
    <SelectProductLine ref="selectProductLinePage" @itemSelected="productLineSelected"></SelectProductLine>
  </div>
</template>

<script>
import masterAPI from "@/api/masterData";
import selectorAPI from "@/api/selector";
import SelectCustomer from "./selectCustomer.vue";
import SelectProductLine from "./selectProductLine.vue";

export default {
  name: '',
  components: {
    SelectCustomer,
    SelectProductLine
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      span: 12,
      rules: {
        customerId: [
          { required: true, message: '请选择所属机构', trigger: 'change' },
        ],
        name: [
          { required: true, message: '请输入客户姓名', trigger: 'blur' },
        ],
        dept: [
          { required: true, message: '请输入科室/部门', trigger: 'blur' },
        ],
        enumGender: [
          { required: true, message: '请选择性别', trigger: 'change' },
        ],
        titleDictId: [
          { required: true, message: '必须选择职称', trigger: 'change' },
        ],
        positionDictId: [
          { required: true, message: '必须选择职务', trigger: 'change' },
        ],
         doctorStageDictId: [
          { required: true, message: '必须选择医生阶段', trigger: 'change' },
        ],
      },
      tempFormModel: { id: '', selectFlags: [], customers: [], productLineIds: [], productLines: [] },
      btnSaveLoading: false,
      isAddContact: false,

      contactFlags: [],
      titleLevels: [],
      positions: [],
      doctorStages: [],
      doctorLevels: [],
    }
  },
  watch: {
    id(val) {
      this.tempFormModel.id = val
    },
  },
  mounted() {
  },
  created() {
    this.init()

    this.initDoctorLevels()
    this.initTitleLevels()
    this.initPositions()
    this.initDoctorStages()
  },
  methods: {
    showAddContactCustomer() {
      this.$refs.selectCustomerPage.init()
    },
    customerSelected(contactCustomer) {
      if (this.tempFormModel.customers.find(p => p.customerId == contactCustomer.customerId)) {
        // 数据已存在
        this.$notice.message(contactCustomer.customerName + '已存在，不能重复添加', 'warning')
      }
      else {
        this.tempFormModel.customers.push(contactCustomer)
        this.$notice.message(contactCustomer.customerName + '添加成功', 'success')
      }
    },
    handleSetDefaultCustomer(row) {
      this.tempFormModel.customers.map((item) => {
        this.$set(item, 'isDefault', false)
        return item
      })

      this.$set(row, 'isDefault', true)
    },
    handleDeleteContactCustomer(row) {
      this.$confirm('确定删除关联机构?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempFormModel.customers = this.tempFormModel.customers.filter(function (item) {
          return item.customerId != row.customerId
        })
      }).catch(error => {
        if (!error.succeed) {
        }
      })
    },
    showAddProductLine() {
      this.$refs.selectProductLinePage.init()
    },
    productLineSelected(productLine) {
      if (this.tempFormModel.productLines.find(p => p.productLineId == productLine.productLineId)) {
        // 数据已存在
        this.$notice.message(productLine.productLineName + '已存在，不能重复添加', 'warning')
      }
      else {
        this.tempFormModel.productLines.push(productLine)
        this.$notice.message(productLine.productLineName + '添加成功', 'success')
      }
    },
    handleDeleteProductLine(row) {
      this.$confirm('确定删除产品线?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempFormModel.productLines = this.tempFormModel.productLines.filter(function (item) {
          return item.productLineId != row.productLineId
        })
      }).catch(error => {
        if (!error.succeed) {
        }
      })
    },
    initContactFlags() {
      var param = {
        enumType: 'ContactFlag',
        group: 'Contact'
      }
      this.contactFlags = []
      this.$set(this.tempFormModel, 'selectFlags', [])
      selectorAPI.getEnumInfos(param).then((result) => {
        this.contactFlags = result.data.datas;

        if (this.tempFormModel.enumFlags && this.tempFormModel.enumFlags > 0) {
          if (this.contactFlags && this.contactFlags.length > 0) {
            this.contactFlags.forEach(e => {
              if ((e.value & this.tempFormModel.enumFlags) > 0) {
                this.tempFormModel.selectFlags.push(e.value)
              }
            })
          }
        }

        if (!this.id) {
          this.tempFormModel.selectFlags = [1]
        }
      }).catch((error) => {
        console.log(error);
      });
    },
    initTitleLevels() {
      var param = { parentCode: "Contact_Doctor_Title" };
      selectorAPI.getDicts(param).then((result) => {
        this.titleLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initPositions() {
      var param = { parentCode: "Contact_Doctor_Position" };
      selectorAPI.getDicts(param).then((result) => {
        this.positions = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initDoctorStages() {
      var param = { parentCode: "Contact_Doctor_Stage" };
      selectorAPI.getDicts(param).then((result) => {
        this.doctorStages = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    initDoctorLevels() {
      var param = { parentCode: "Contact_Doctor_Level" };
      selectorAPI.getDicts(param).then((result) => {
        this.doctorLevels = result.data;
      }).catch((error) => {
        console.log(error);
      });
    },
    init() {
      this.isAddContact = false
      if (!this.id) {
        this.isAddContact = true
        this.initContactFlags()
        this.tempFormModel = { id: '', selectFlags: [], customers: [], productLineIds: [], productLines: [] }
        this.clear()
        return
      }
      if (this.id) {
        this.get()
      }
    },
    get() {
      masterAPI.getContact({ id: this.id }).then(result => {
        this.tempFormModel = result.data

        // 设置默认机构
        var defaultCustoner = this.tempFormModel.customers.find(p => p.customerId == this.tempFormModel.customerId)
        if (defaultCustoner) {
          defaultCustoner.isDefault = true
        }

        // 产品线取值
        this.initContactFlags()
      }).catch(error => {
      })
    },
    save() {
      if (!this.tempFormModel.customers || this.tempFormModel.customers.length == 0) {
        this.$confirm('请选择关联机构', '提示', {
          confirmButtonText: '',
          showConfirmButton: false,
          cancelButtonText: '关闭',
          type: 'error'
        }).catch(error => { })
        return false;
      }

      var defaultCustoner = this.tempFormModel.customers.find(p => p.isDefault)
      if (!defaultCustoner) {
        this.$confirm('请在关联机构列表设置默认关联机构', '提示', {
          confirmButtonText: '',
          showConfirmButton: false,
          cancelButtonText: '关闭',
          type: 'error'
        }).catch(error => { })
        return false;
      }
      else {
        this.tempFormModel.customerId = defaultCustoner.customerId
      }

      if (!this.tempFormModel.productLines || this.tempFormModel.productLines.length == 0) {
        this.$confirm('请选择产品线', '提示', {
          confirmButtonText: '',
          showConfirmButton: false,
          cancelButtonText: '关闭',
          type: 'error'
        }).catch(error => { })
        return false;
      }
      else if (this.tempFormModel.selectFlags.find(v => v == 1) && this.tempFormModel.productLines.find(p => !!!p.doctorLevelDictId)) {
        this.$confirm('请选择产品线的医生级别', '提示', {
          confirmButtonText: '',
          showConfirmButton: false,
          cancelButtonText: '关闭',
          type: 'error'
        }).catch(error => { })
        return false;
      }

      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.tempFormModel.enumFlags = 0
          this.tempFormModel.selectFlags.forEach(e => {
            this.tempFormModel.enumFlags = this.tempFormModel.enumFlags | e
          })
          if (!!!this.tempFormModel.selectFlags.find(p => p == 1)) {
            this.tempFormModel.titleDictId = ''
            this.tempFormModel.positionDictId = ''
            this.tempFormModel.doctorStageDictId = ''
          } else {
            this.tempFormModel.title = ''
            this.tempFormModel.position = ''
          }

          if (!this.tempFormModel.id) {
            this.addNew()
          } else {
            this.update()
          }
        }
      })
    },
    addNew() {
      this.btnSaveLoading = true
      masterAPI.addContact(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('新增成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('新增失败。', 'error')
          }
        })
    },
    update() {
      this.btnSaveLoading = true
      masterAPI.updateContact(this.tempFormModel).then(result => {
        this.btnSaveLoading = false
        if (result.succeed) {
          this.tempFormModel = result.data
          this.$notice.message('修改成功', 'success')
          this.close()
        } else {
          if (result.type !== -3) {
            this.$notice.resultTip(result)
          }
        }
      })
        .catch(error => {
          this.btnSaveLoading = false
          if (!error.processed) {
            this.$notice.message('修改失败。', 'error')
          }
        })
    },
    clear() {
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].resetFields()
      }
    },
    close() {
      this.clear()
      this.$emit('refresh')
    },
    cancle() {
      this.clear()
      this.$emit('hidden')
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    addRedStar(h, { column }) {
      return [
        h("span", { style: "color: red" }, "*"),
        h("span", " " + column.label)
      ]
    }
  }

}
</script>
