<template>
  <div>
    <el-dialog ref="dataCustomerForm" append-to-body :close-on-click-modal="true" :title="'选择机构'" :visible="showDialog" width="75%" @close="closeDialog()">

      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="span">
            <el-input v-model="listQuery.keywords" clearable placeholder="关键词" class="filter-item" @keyup.enter.native="handleFilter" />
          </el-col>
          <el-col :span="span">
            <el-select v-model="listQuery.Types" class="filter-item" placeholder="类型" multiple collapse-tags clearable>
              <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select v-model="listQuery.Statuses" class="filter-item" placeholder="状态" multiple collapse-tags clearable>
              <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>

          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>

      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'Name', order: 'ascending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass" @sort-change="sortChange">
              <el-table-column label="序号" type="index" align="center" :index="indexMethod" />
              <el-table-column label="机构名称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.customerName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="简称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.customerShortName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="性质" min-width="60px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumTypeDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="90px" header-align="center" align="center" prop="EnumStatus">
                <template slot-scope="{ row }">
                  <span>{{ row.enumStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数据状态" width="94px" header-align="center" align="center" prop="EnumReviewState">
                <template slot-scope="{ row }">
                  <span>{{ row.enumReviewStateDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="60" min-width="90" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
                </template>
              </el-table-column>
            </el-table>

          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import SelectorApi from '@/api/selector'
import masterAPI from '@/api/masterData'

export default {
  components: {
    Pagination
  },
  props: {

  },
  data() {
    return {
      span: 5,
      listLoading: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '+Name'
      },
      list: [],
      statusList: [],
      typeList: [],
      dialogStatus: '',
      showDialog: false,
    }
  },
  watch: {

  },
  created() {
    this.initStatusList()
    this.initTypeList()
    this.handleFilter()
  },
  methods: {
    init() {
      this.showDialog = true
    },

    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'CustomerType' }
      SelectorApi
        .getEnumInfos(param)
        .then((result) => {
          this.typeList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取机构状态枚举
    initStatusList() {
      var param = { enumType: 'CustomerStatus' }
      SelectorApi.getEnumInfos(param)
        .then((result) => {
          this.statusList = result.data.datas
        })
        .catch((error) => {
          console.log(error)
        })
    },

    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      masterAPI.queryContactCustomerSelector(this.listQuery)
        .then((result) => {
          this.listLoading = false
          if (result.succeed) {
            this.list = result.data.datas
            this.total = result.data.recordCount
            this.listQuery.pageIndex = result.data.pageIndex
          } else {
            this.$notice.resultTip(result)
          }
        })
        .catch((error) => {
          console.log(error)
          this.listLoading = false
        })
    },

    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },

    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit('itemSelected', row)
    },
    closeDialog() {
      this.showDialog = false
    }

  }
}
</script>

