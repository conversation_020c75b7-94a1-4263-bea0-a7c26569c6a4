<template>
  <div class="login-container">
    <div class="login-form-container">
      <img src="../../assets/logo/logo3.png" />

      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">
        <div style="background-color: #022140; padding: 40px">
          <div class="title-container">
            <h3 class="title">{{ title }}</h3>
          </div>

          <el-form-item prop="loginName">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input ref="loginName" v-model="loginForm.loginName" placeholder="用户名" type="text" tabindex="1" />
          </el-form-item>

          <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
            <el-form-item prop="password" style="margin-bottom: 0px !important">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType" placeholder="密码" tabindex="2" @keyup.native="checkCapslock" @blur="capsTooltip = false" />
              <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span>
            </el-form-item>
          </el-tooltip>
          <el-checkbox v-model="loginForm.autoLogin" style="margin-top: 15px;">两周内自动登录</el-checkbox>
          <Verify style="margin-top: 10px;" @success="verify(true)" @error="verify(false)" :vOffset="30" :type="3" :barSize="{width:'100%',height:'40px'}"></Verify>
          <el-button :disabled="!this.isCheckPass" type="primary" style="width: 100%;  margin-top: 17px;height:40px" @click.native.prevent="handleLogin">登 录</el-button>
        </div>
      </el-form>

    </div>
  </div>
</template>

<script>
import cfg from '@cfg'
import Verify from 'vue2-verify'
import {
  getToken,
  setToken,
  removeToken,
  getAutoLoginToken,
  setAutoLoginToken,
  removeAutoLoginToken,
  isUserPermitted,
} from "@/utils/auth";

export default {
  components: {
    Verify
  },
  name: 'Login',
  data() {
    const validateLoginName = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请输入密码'))
      } else if (value.trim() === '') {
        callback(new Error('请输入正确的密码'))
      } else {
        callback()
      }
    }
    return {
      isCheckPass: false,
      title: cfg.title,
      loginForm: {
        loginName: process.env.NODE_ENV === 'development' ? 'admin' : '',
        password: process.env.NODE_ENV === 'development' ? '123456' : ''
      },
      loginRules: {
        loginName: [{
          required: true,
          trigger: 'blur',
          validator: validateLoginName
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},


    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)

    this.init()
  },
  mounted() {
    if (this.loginForm.loginName === '') {
      this.$refs.loginName.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    verify(val) {
      this.isCheckPass = val;
    },
    init() {
      var autoLoginToken = getAutoLoginToken()
      console.log(autoLoginToken)
      if (autoLoginToken) {
        this.autoLogin()
      }
    },
    checkCapslock(e) {
      const {
        key
      } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$store
            .dispatch('user/login', this.loginForm)
            .then((data) => {
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery
              })
            })
            .catch((error) => {
              console.log(error)
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },

    autoLogin() {
      this.$store
        .dispatch('user/autoLogin')
        .then((data) => {
          this.$router.push({
            path: this.redirect || '/',
            query: this.otherQuery
          })
        })
    },
  }
}

</script>

<style lang="scss">
.verify-btn {
  display: none !important;
}

.verify-bar-area .verify-move-block:hover {
  background-color: rgb(252, 202, 32) !important;
  color: #ffffff;
}

/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-form-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-form-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background: url("~@/assets/login/loginBackground.png");
  background-position: center;
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 450px;
    max-width: 100%;
    padding: 130px 35px 0;
    margin: 0px 60px;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 22px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .alert {
    background-color: #f5f6f8;
    color: #409eff;
    margin-bottom: 15px;
  }
}
</style>
