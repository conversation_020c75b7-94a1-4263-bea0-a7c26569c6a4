<template>
  <div class="errPage-container">
    <div class="errText">抱歉!您访问的页面未找到......</div>
    <el-button icon="el-icon-arrow-left" class="back-btn" @click="back">
      返回
    </el-button>
  </div>
</template>

<script>

export default {
  name: 'Page404',
  data() {
    return {
    }
  },
  methods: {
    back() {
      if (this.$route.query.noGoBack) {
        this.$router.push({ path: '/home' })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .errPage-container {
    position:absolute;
    width:800px;
    height:450px;
    top:50%;
    left:50%;
    margin:-225px 0 0 -400px;
    background-image:url('~@/assets/404_images/404_nfind.png');
    background-repeat:no-repeat ;
    background-position: center center;
    background-size:contain;
    text-align: center;
  }

  .errText {
    position: relative;
    top:350px;
    color: #036eb8;
    font-size: 30px;
    font-weight: 400;
    line-height: 40px;
  }

  .back-btn {
    position: absolute;
    left: 10%;
    top:5%;
    background-color:#1482f0;
    color: #fff;
    border-radius: 100px;
    text-align: center;
    cursor: pointer;
    border: none!important;
  }
</style>
