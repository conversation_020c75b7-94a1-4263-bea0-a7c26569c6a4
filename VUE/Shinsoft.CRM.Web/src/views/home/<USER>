<template>
  <div class="dashboard-editor-container search-container-bg">
    <el-row type="flex" :gutter="10" class="home-flex-container">
      <el-col :span="8"> </el-col>
      <el-col :span="16"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12"> </el-col>
    </el-row>
    <div>{{ resultLocation }}</div>
    <!-- <txMap
      ref="txMap"
      :center="'31.230355,121.47371'"
      style="width: 500px; height: 300px"
      :locations="locations"
      @changeLocation="changeLocation"
    ></txMap>
    <el-button @click="showSearch"> 搜索</el-button> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
// import HomeService from '@/api/home'

export default {
  name: 'Home',
  components: {
  },
  data() {
    return {
      locations: [],
      resultLocation: ''
    }
  },
  computed: {
    ...mapState({
      downloadFileUrl: state => state.settings.downloadFileUrl
    })
  },
  created() {
    // this.locations = [{

    // }]
    this.locations = [
      {
        latitude: 31.230355,
        'longitude': 121.47371,
        'height': 0
      }
      // {
      //   'latitude': 31.228725,
      //   'longitude': 121.475186,
      //   'height': 0
      // }
      // {
      //   'latitude': 31.228753,
      //   'longitude': 121.473346,
      //   'height': 0
      // }
    ]
  },
  mounted() {

  },
  methods: {
    showSearch() {
      this.$refs.txMap.showChoose()
    },
    changeLocation(location) {
      this.resultLocation = JSON.stringify(location)
    }
  }
}
</script>
<style scoped>
.box ul {
  display: grid;
  justify-content: space-evenly;
  padding: 10px;
  grid-template-columns: repeat(auto-fill, 160px);
  grid-gap: 10px;
  text-align: center;
}

.box li {
  width: 160px;
  list-style: none;
  margin-bottom: 10px;
}

.appImgContainer {
  height: 120px;
  padding: 5px 0px;
}

.appImgContainer img {
  width: 100px;
  height: 100px;
}

.appName {
  font-size: 18px;
  font-weight: bold;
}

.home-flex-container {
  flex-wrap: wrap;
}

.home-flex-container .el-col {
  margin-bottom: 10px;
}
</style>
