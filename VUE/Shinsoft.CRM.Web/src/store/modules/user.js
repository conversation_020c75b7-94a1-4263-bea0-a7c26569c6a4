// import store from '@/store'
import sysApi from "@/api/sys";
import {
  getToken,
  setToken,
  removeToken,
  getAutoLoginToken,
  setAutoLoginToken,
  removeAutoLoginToken,
  isUserPermitted,
} from "@/utils/auth";
import { resetRouter } from "@/router";
import { constantRoutes, asyncRoutes, endRoutes } from "@/router/routes";

/**
 * Use meta.permissions to determine if the current user has permission
 * @param user
 * @param route
 */
function hasPermission(user, route) {
  if (
    route.meta &&
    route.meta.permissions &&
    route.meta.permissions instanceof Array &&
    route.meta.permissions.length > 0
  ) {
    return (
      user &&
      user.permission &&
      route.meta.permissions.some((permission) =>
        isUserPermitted(user, permission)
      )
    );
  } else {
    return true;
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param user
 * @param routes asyncRoutes
 */
export function filterAsyncRoutes(user, routes) {
  const res = [];

  routes.forEach((route) => {
    const tmp = { ...route };
    if (hasPermission(user, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(user, tmp.children);
      }
      res.push(tmp);
    }
  });

  return res;
}

const state = {
  token: getToken(),
  user: false,
  routes: [],
  addRoutes: [],
  autoLoginToken: getAutoLoginToken(),
};

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token;
    if (token === "") {
      removeToken();
    } else {
      setToken(token);
    }
  },
  SET_AUTOLOGINTOKEN(state, autoLoginToken) {
    state.autoLoginToken = autoLoginToken;
    if (autoLoginToken === "") {
      removeAutoLoginToken();
    } else {
      setAutoLoginToken(autoLoginToken);
    }
  },
  SET_USER(state, user) {
    state.user = user;
    if (!user) {
      resetRouter();
    } else {
      const accessedRoutes = filterAsyncRoutes(user, asyncRoutes);
      this.commit("user/SET_ROUTES", accessedRoutes);
    }
  },
  SET_ROUTES(state, routes) {
    resetRouter(routes);
    state.addRoutes = routes;
    state.routes = constantRoutes.concat(routes).concat(endRoutes);
  },
};

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { loginName, password, autoLogin } = userInfo;
    return new Promise((resolve, reject) => {
      sysApi
        .userLogin({
          loginName: loginName.trim(),
          password: password,
          program: 1,
          autoLogin: autoLogin,
        })
        .then((res) => {
          if (!res.succeed) {
            const message = res.messages.toString().replace(/,/g, "<br/>");
            alert(message);
          } else {
            const { data } = res;
            commit("SET_TOKEN", data.token);
            if (data.autoLoginToken) {
              commit("SET_AUTOLOGINTOKEN", data.autoLoginToken);
            }

            delete data.token;
            commit("SET_USER", data);
            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  autoLogin({ commit }) {
    return new Promise((resolve, reject) => {
      sysApi
        .autoLogin({
          token: getAutoLoginToken(),
          program: 1,
        })
        .then((res) => {
          if (!res.succeed) {
            const message = res.messages.toString().replace(/,/g, "<br/>");
            alert(message);
          } else {
            const { data } = res;
            commit("SET_TOKEN", data.token);
            if (data.autoLoginToken) {
              commit("SET_AUTOLOGINTOKEN", data.autoLoginToken);
            }
            delete data.token;
            commit("SET_USER", data);
            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  //切换个人身份
  switchMyIdentity({ commit }, params) {
    return new Promise((resolve, reject) => {
      sysApi
        .switchMyIdentity(params)
        .then((res) => {
          if (!res.succeed) {
            const message = res.messages.toString().replace(/,/g, "<br/>");
            alert(message);
          } else {
            const { data } = res;
            commit("SET_TOKEN", data.token);
            delete data.token;
            commit("SET_USER", data);
            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  //切换代理身份
  switchToAgent({ commit }, params) {
    return new Promise((resolve, reject) => {
      sysApi
        .switchToAgent(params)
        .then((res) => {
          if (!res.succeed) {
            const message = res.messages.toString().replace(/,/g, "<br/>");
            alert(message);
          } else {
            const { data } = res;
            commit("SET_TOKEN", data.token);
            delete data.token;
            commit("SET_USER", data);
            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  tokenlogin({ commit }, logintoken) {
    return new Promise((resolve, reject) => {
      sysApi
        .userLoginBySsoToken({ Token: logintoken })
        .then((res) => {
          if (!res.succeed) {
            const message = res.messages.toString().replace(/,/g, "<br/>");
            alert(message);
          } else {
            const { data } = res;
            commit("SET_TOKEN", data.token);
            delete data.token;
            commit("SET_USER", data);
            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      dispatch("resetToken");

      // reset visited views and cached views
      // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
      dispatch("tagsView/delAllViews", null, { root: true });

      resolve();
    });
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      sysApi
        .getCurrentUser()
        .then((res) => {
          const { data } = res;

          if (!data) {
            reject("Verification failed, please Login again.");
          }
          delete data.token;
          commit("SET_USER", data);
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      commit("SET_TOKEN", "");
      commit("SET_AUTOLOGINTOKEN", "");
      commit("SET_USER", false);
      resolve();
    });
  },

  isPermitted({ commit, state }, permission) {
    return isUserPermitted(state.user, permission);
  },

  firstLoginChangePwd({ commit }, userInfo) {
    const { loginName, newPwd } = userInfo;
    return new Promise((resolve, reject) => {
      sysApi
        .firstLoginChangePwd({ loginName: loginName.trim(), newPwd: newPwd })
        .then((res) => {
          const { data } = res;
          commit("SET_TOKEN", data.token);
          delete data.token;
          commit("SET_USER", data);
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
