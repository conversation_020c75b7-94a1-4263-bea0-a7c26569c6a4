// cover some element-ui styles 全局自定义 element-ui 样式

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }

  .auto-upload {
    .el-upload-list {
      .is-ready {
        display: none;
      }
    }
  }


  .limited {
    .el-upload {
      display: none;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
.el-input-number .el-input__inner  {
  text-align: right !important;
}

.el-table__fixed{
  height: 100% !important;
 }

 .el-table__fixed-right{
  height: 100% !important;
 }


//Begin弹出框标题***************************************//
.el-dialog__header{
  padding: 15px;
  //padding-bottom: 5px;
  border-bottom: #DBDFE6 solid 1px;
}
.el-dialog__title{
  font-size: 14px;
  font-weight: bold;
}
.el-dialog__body{
  padding: 20px 20px;
}
.el-dialog__footer{
  border-top: #DBDFE6 solid 1px;
  padding: 15px;
}
//End弹出框标题***************************************//