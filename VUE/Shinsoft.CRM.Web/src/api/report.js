import HttpApi from "./libs/api.request";

const controller = "Report";

const api = new HttpApi(controller);

export default {
  NewGuid() {
    return api.get("NewGuid");
  },
  GetEnumInfos(params) {
    return api.get("GetEnumInfos", params);
  },

  // 查询拜访明细报表
  queryVisitDetail(params) {
    return api.get("QueryVisitDetail", params);
  },
  // 导出拜访明细报表
  exportVisitDetail(params) {
    return api.post('ExportVisitDetail', {
    data: params,
    responseType: 'arraybuffer'
    })
  },

  queryTrainingCompleteReport(params) {
    return api.get("QueryTrainingCompleteReport", params);
  },
  exportTrainingCompleteReport(params) {
    return api.post('exportTrainingCompleteReport', {
    data: params,
    responseType: 'arraybuffer'
    })
  },
  queryTrainingReport(params) {
    return api.get("QueryTrainingReport", params);
  },
  exportTrainingReport(params) {
    return api.post('exportTrainingReport', {
    data: params,
    responseType: 'arraybuffer'
    })
  },
  queryMeetingReport(params) {
    return api.get("QueryMeetingReport", params);
  },
  exportMeetingReport(params) {
    return api.post('exportMeetingReport', {
    data: params,
    responseType: 'arraybuffer'
    })
  },
  //
  //
  //end of api
};
