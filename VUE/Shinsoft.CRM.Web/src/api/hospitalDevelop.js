import HttpApi from './libs/api.request'

const controller = 'hospitalDevelop'

const api = new HttpApi(controller)

export default {
	// 执行人
	queryOwnerEmployeeSelector(data) {
		return api.get('QueryOwnerEmployeeSelector', data)
	},
	queryEmployeeSelector(data) {
		return api.get('QueryEmployeeSelector', data)
	},
	// 查询客户
	queryEmployeeMemberSelector(data) {
		return api.get('QueryEmployeeMemberSelector', data)
	},
  queryContactCustomerSelector(params) {
    return api.get('QueryContactCustomerSelector', params)
  },
	// 查询选择客户列表
	queryCustomerSelector(params) {
		return api.get('QueryCustomerSelector', params)
	},
	// 查询医院开发
	queryHospitalDevelop(data) {
		return api.get('QueryHospitalDevelop', data)
	},
	// 查询医院开发
	getHospitalDevelop(data) {
		return api.get('GetHospitalDevelop', data)
	},
	addHospitalDevelop(data) {
		return api.post('AddHospitalDevelop', data)
	},

	// 查询医院开发参与者
	getHospitalDevelopMember(data) {
		return api.get('GetHospitalDevelopMember', data)
	},
	addHospitalDevelopMember(data) {
		return api.post('AddHospitalDevelopMember', data)
	},
	deleteHospitalDevelopMember(data) {
		return api.post('DeleteHospitalDevelopMember', data)
	},

	//* ********************************审批
	// 查询医院开发计划审核数据
	queryHospitalDevelopReview(data) {
		return api.get('QueryHospitalDevelopReview', data)
	},
	// 获取医院开发项目审核数据
	getHospitalDevelopReview(data) {
		return api.get('GetHospitalDevelopReview', data)
	},
	// 获取医院开发项目审核数据
	auditHospitalDevelopReview(data) {
		return api.post('AuditHospitalDevelopReview', data)
	},
	// 批量审核医院开发数据
	batchAuditHospitalDevelopReview(data) {
		return api.post('BatchAuditHospitalDevelopReview', data)
	},

	//* ********************************反馈
	// 查询医院开发反馈
	queryHospitalDevelopFeedback(data) {
		return api.get('QueryHospitalDevelopFeedback', data)
	},
	// 获取医院开发反馈
	getHospitalDevelopFeedback(data) {
		return api.get('GetHospitalDevelopFeedback', data)
	},
	// 跟进医院开发反馈
	followHospitalDevelop(data) {
		return api.post('FollowHospitalDevelop', data)
	},
	// 完成医院开发
	completeHospitalDevelop(data) {
		return api.post('CompleteHospitalDevelop', data)
	},

	//* ********************************历史
	// 查询医院开发反馈
	queryHospitalDevelopHistory(data) {
		return api.get('QueryHospitalDevelopHistory', data)
	},
	// 查询医院开发步骤选择项
	queryStepSelector(data) {
		return api.get('QueryStepSelector', data, true)
	},
	// 查询医院开发结果选择项
	queryResultSelector(data) {
		return api.get('QueryResultSelector', data, true)
	},
	// 查询产品选择项
	queryProductSelector(data) {
		return api.get('QueryProductSelector', data)
	},

	// 查询客户选择项
	queryContactSelector(data) {
		return api.get('QueryContactSelector', data)
	},
	// 查询医院开发类型
	queryHospitalDevelopCategorySelector(data) {
		return api.get('QueryHospitalDevelopCategorySelector', data)
	},
	// 获取医院开发类型阶段
	getHospitalDevelopPhaseSelector(data) {
		return api.get('GetHospitalDevelopPhaseSelector', data)
	},
	// 推进
	advanceHospitalDevelopPhase(data) {
		return api.post('AdvanceHospitalDevelopPhase', data)
	},
	// 查询医院开发推进历史
	queryAdvanceHospitalDevelopPhaseLog(data) {
		return api.get('QueryAdvanceHospitalDevelopPhaseLog', data)
	}

}
