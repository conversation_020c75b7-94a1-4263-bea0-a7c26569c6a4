import HttpApi from './libs/api.request'

const controller = 'selector'

const api = new HttpApi(controller)

export default {
  getEnumInfos(params) {
    return api.get('GetEnumInfos', params)
  },
  queryDepartmentSelector(params) {
    return api.get('QueryDepartmentSelector', params)
  },
  getDicts(params) {
    return api.get('GetDicts', params)
  },
  queryUserSelector(params) {
    return api.get('QueryUserSelector', params)
  },
  queryEmployeeSelector(params) {
    return api.get('QueryEmployeeSelector', params)
  },
  queryCustomerCategorySelector(params) {
    return api.get('QueryCustomerCategorySelector', params)
  },
  queryCustomerSelector(params) {
    return api.get('QueryCustomerSelector', params)
  },
  queryDocResourceTree(params) {
    return api.get('QueryDocResourceTree', params)
  },
  queryBusinessUnitSelector(params) {
    return api.get('QueryBusinessUnitSelector', params)
  },
  queryMeetingCategorySelector(params) {
    return api.get('QueryMeetingCategorySelector', params)
  }
}
