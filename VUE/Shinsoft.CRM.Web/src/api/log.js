import HttpApi from './libs/api.request'

const controller = 'log'

const api = new HttpApi(controller)

export default {
  queryLog(data) {
    return api.get('QueryLog', data)
  },
  QueryExceptionLog(data) {
    return api.get('QueryExceptionLog', data)
  },
  GetLog(data) {
    return api.get('GetLog', data)
  },
  // 获取所有日志操作选择器
  getlogsSelector(params) {
    return api.get('GetlogsSelector', params)
  },
  // 导出日志
  exportLogData(params) {
    return api.post('ExportLogData',
      {
        data: params,
        responseType: 'arraybuffer'
      })
  }
}
