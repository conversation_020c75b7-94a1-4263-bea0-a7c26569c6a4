import HttpApi from './libs/api.request'

const controller = 'meeting'

const api = new HttpApi(controller)

export default {
  queryEmployeeSelector(data) {
    return api.get('QueryEmployeeSelector', data)
  },
  queryEventProductSelector() {
    return api.get('QueryEventProductSelector')
  },
  // 查询客户
  queryEmployeeMemberSelector(data) {
    return api.get('QueryEmployeeMemberSelector', data)
  },
  // 查询会议形式
  queryModeSelector(data) {
    return api.get('QueryModeSelector', data)
  },
  // 新增会议
  addMeetingPlan(data) {
    return api.post('AddMeetingPlan', data)
  },
  // 查询会议类型
  queryTypeSelector(data) {
    return api.get('QueryTypeSelector', data)
  },
  // 查询主讲人列表
  querySpeakerSelector(data) {
    return api.get('QuerySpeakerSelector', data)
  },
  // 查询讲者列表
  querySpeakerMemberSelector(data) {
    return api.get('QuerySpeakerMemberSelector', data)
  },
  // 执行人
  queryOwnerEmployeeSelector(data) {
    return api.get('QueryOwnerEmployeeSelector', data)
  },
  // 查询参会人列表
  queryContactMemberSelector(data) {
    return api.get('QueryContactMemberSelector', data)
  },
  // 查询会议列表
  queryDailyMeeting(data) {
    return api.get('QueryDailyMeeting', data)
  },
  // 查询我的会议列表
  queryMyDailyMeeting(data) {
    return api.get('QueryMyDailyMeeting', data)
  },
  queryMeeting(data) {
    return api.get('QueryMeeting', data)
  },
  // 获取会议详情
  getDailyMeeting(data) {
    return api.get('GetDailyMeeting', data)
  },
  // 查询会议计划审核数据
  queryMeetingReview(data) {
    return api.get('QueryMeetingReview', data)
  },
  // 获取会议计划审核数据
  getMeetingReview(data) {
    return api.get('GetMeetingReview', data)
  },
  // 审核会议数据
  auditMeetingReview(data) {
    return api.post('AuditMeetingReview', data)
  },
  // 审核会议数据
  batchAuditMeetingReview(data) {
    return api.post('BatchAuditMeetingReview', data)
  },
  // 查询客户下拉框
  queryCustomerSelector(data) {
    return api.get('QueryCustomerSelector', data)
  },
  // 查询客户
  queryContactSelector(data) {
    return api.get('QueryContactSelector', data)
  },
  queryResultSelector(data) {
    return api.get('QueryResultSelector', data)
  },
  // 历史会议
  queryMeetingHistory(data) {
    return api.get('QueryMeetingHistory', data)
  },
  // 新增反馈
  addMeetingFeedback(data) {
    return api.post('AddMeetingFeedback', data)
  },
  // 完成反馈
  completedMeetingFeedback(data) {
    return api.post('CompletedMeetingFeedback', data)
  },
  // 查询会议反馈
  queryMeetingFeedback(data) {
    return api.get('QueryMeetingFeedback', data)
  },
  // 获取拜访反馈
  getMeetingFeedback(data) {
    return api.get('GetMeetingFeedback', data)
  },
  // 查询下属会议
  querySubMeeting(data) {
    return api.get('QuerySubMeeting', data)
  },

  // 会议类型
  queryMeetingCategorySelector(data) {
    return api.get('QueryMeetingCategorySelector', data)
  },
  queryEventProductSelector(data) {
    return api.get('QueryEventProductSelector', data)
  },
  // 查询会议文档资料树
  queryDocResourceTree(data) {
    return api.post('QueryDocResourceTree', data)
  },
  // 变更会议计划
  changeMeetingPlan(data) {
    return api.post('ChangeMeetingPlan', data)
  },

  // 查询会议签到
  queryEventMeetingSign(data) {
    return api.get('QueryEventMeetingSign', data)
  },
  // 获取日会议
  getMeeting(data) {
    return api.get('GetMeeting', data)
  },
  // 会议签到
  eventMeetingSignIn(data) {
    return api.post('EventMeetingSignIn', data)
  },
  // 会议签退
  eventMeetingSignOut(data) {
    return api.post('EventMeetingSignOut', data)
  },
  // 二维码
  getMeetingSignQrCode(data) {
    return api.get('GetMeetingSignQrCode', data)
  },
  // 已完成反馈
  getNeedFeedbackMeetingCategoryResult(data) {
    return api.get('GetNeedFeedbackMeetingCategoryResult', data)
  }
}
