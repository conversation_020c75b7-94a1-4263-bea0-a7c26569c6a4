import apiApi from './libs/api.request'

const controller = 'training'

const api = new apiApi(controller)

export default {
  queryEmployeeSelector(data) {
    return api.get('QueryEmployeeSelector', data)
  },
  queryContactCustomerSelector(params) {
    return api.get('QueryContactCustomerSelector', params)
  },
  // 查询日培训
  queryDailyTraining(data) {
    return api.get("QueryDailyTraining", data)
  },
  //查询下属培训
  querySubTraining(data) {
    return api.get("QuerySubTraining", data)
  },
  queryMyDailyTraining(data) {
    return api.get("QueryMyDailyTraining", data)
  },
  // 查询日培训数量
  getDailyTrainingCount(data) {
    return api.get("GetDailyTrainingCount", data)
  },
  // 获取日培训
  getDailyTraining(data) {
    return api.get("GetDailyTraining", data)
  },

  //新增培训计划
  addTrainingPlan(data) {
    return api.post("AddTrainingPlan", data)
  },
  //新增临时培训
  addExtraTraining(data) {
    return api.post("AddExtraTraining", data)
  },
  changeTraining(data) {
    return api.post("ChangeTraining", data)
  },

  addTrainingMember(data) {
    return api.post("AddTrainingMember", data)
  },
  deleteTrainingMember(data) {
    return api.post("DeleteTrainingMember", data)
  },
  // 查询培训计划审核数据
  queryTrainingReview(data) {
    return api.get("QueryTrainingReview", data)
  },
  //获取培训计划审核数据
  getTrainingReview(data) {
    return api.get("GetTrainingReview", data)
  },
  //审核培训数据
  auditTrainingReview(data) {
    return api.post("AuditTrainingReview", data)
  },
  //批量审核培训数据
  batchAuditTrainingReview(data) {
    return api.post("BatchAuditTrainingReview", data)
  },

  //查询培训反馈
  queryTrainingFeedback(data) {
    return api.get("QueryTrainingFeedback", data)
  },
  //获取培训反馈
  getTrainingFeedback(data) {
    return api.get("GetTrainingFeedback", data)
  },
  //新增培训反馈
  addTrainingFeedback(data) {
    return api.post("AddTrainingFeedback", data)
  },

  //查询培训历史
  queryTrainingHistory(data) {
    return api.get("QueryTrainingHistory", data)
  },


  // 查询类型选择项
  queryTypeSelector(data) {
    return api.get("QueryTypeSelector", data)
  },
  // 查询结果选择项
  queryResultSelector(data) {
    return api.get("QueryResultSelector", data)
  },

  // 查询客户下拉框
  queryCustomerSelector(data) {
    return api.get("QueryCustomerSelector", data)
  },
  // 查询客户
  queryContactSelector(data) {
    return api.get("QueryContactSelector", data)
  },
  // 查询客户
  queryEmployeeMemberSelector(data) {
    return api.get("QueryEmployeeMemberSelector", data)
  },
  //执行人
  queryOwnerEmployeeSelector(data) {
    return api.get("QueryOwnerEmployeeSelector", data)
  },

  //产品bu
  queryBusinessUnitSelector(data) {
    return api.get("QueryBusinessUnitSelector", data)
  },

  //查询题库
  queryQuestionSelector(data) {
    return api.get("QueryQuestionSelector", data)
  },
  //获取题库
  getQuestion(data) {
    return api.get("GetQuestion", data)
  },
  //查询问卷
  querySurvey(data) {
    return api.get("QuerySurvey", data)
  },
  //获取问卷
  getSurvey(data) {
    return api.get("GetSurvey", data)
  },
  //删除问卷
  deleteSurvey(data) {
    return api.post("DelectSurvey", data)
  },
  //删除问卷的题目
  deleteSurveyQuestion(data) {
    return api.post("DeleteSurveyQuestion", data)
  },
  addSurvey(data) {
    return api.post("AddSurvey", data)
  },
  updateSurvey(data) {
    return api.post("UpdateSurvey", data)
  },
  releaseSurvey(data) {
    return api.post("ReleaseSurvey", data)
  },
  addSurveyQuestion(data) {
    return api.post("AddSurveyQuestion", data)
  },
  updateSurveyQuestion(data) {
    return api.post("UpdateSurveyQuestion", data)
  },
  getTrainingAnswerQrCode(data) {
    return api.get("GetTrainingAnswerQrCode", data)
  },
  submitAnswer(data) {
    return api.post("SubmitAnswer", data)
  },
  queryAnswer(data) {
    return api.get("QueryAnswer", data)
  },
  getAnswer(data) {
    return api.get("GetAnswer", data)
  },
  queryTrainingSelector(data) {
    return api.get("QueryTrainingSelector", data)
  },
  queryFailJoinMember(data) {
    return api.get("QueryFailJoinMember", data)
  },
  queryPassJoinMember(data) {
    return api.get("QueryPassJoinMember", data)
  },

  exporAnswer(params) {
    return api.post('ExporAnswer', {
    data: params,
    responseType: 'arraybuffer'
    })
  },
  exporFailJoinMember(params) {
    return api.post('ExporFailJoinMember', {
    data: params,
    responseType: 'arraybuffer'
    })
  },
  exporPassJoinMember(params) {
    return api.post('exporPassJoinMember', {
    data: params,
    responseType: 'arraybuffer'
    })
  },



}
