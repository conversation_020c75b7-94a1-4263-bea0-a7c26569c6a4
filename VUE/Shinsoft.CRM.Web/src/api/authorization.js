import HttpApi from "./libs/api.request";

const controller = "Authorization";

const api = new HttpApi(controller);

export default {
  QueryRole(params) {
    return api.get("QueryRole", params);
  },
  AddRole(params) {
    return api.post("AddRole", params);
  },
  UpdateRole(params) {
    return api.post("UpdateRole", params);
  },
  DeleteRole(params) {
    return api.post("DeleteRole", params);
  },
  GetRolePermissions(params) {
    return api.get("GetRolePermissions", params);
  },
  QueryPermissions(params) {
    return api.get("QueryPermissions", params);
  },
  SetRolePermissions(params) {
    return api.post("SetRolePermissions", params);
  },
  QueryRoleMember(params) {
    return api.get("QueryRoleMember", params);
  },
  QueryMember(params) {
    return api.get("QueryMember", params);
  },
  AddRoleMember(params) {
    return api.post("AddRoleMember", params);
  },
  DeleteRoleMember(params) {
    return api.post("DeleteRoleMember", params);
  },

  QueryRoleEmployee(params) {
    return api.get("QueryRoleEmployee", params);
  },
  QueryEmployeePermissions(params) {
    return api.get("QueryEmployeePermissions", params);
  },
  QueryUserSelect(params) {
    return api.get("QueryUserSelector", params);
  },
  QueryEmployeeSelect(params) {
    return api.get("QueryEmployeeSelect", params);
  },
  QueryRoleSelector() {
    return api.get("QueryRoleSelector");
  },
  QueryVwEmployeeRoles(params) {
    return api.get("QueryVwEmployeeRoles", params);
  },
  AddRoleMemberList(params) {
    return api.post("AddRoleMemberList", params);
  },
  UpdateRoleMemberList(params) {
    return api.post("UpdateRoleMemberList", params);
  },
  QueryRoleMemberByMemId(params) {
    return api.get("QueryRoleMemberByMemId", params);
  },
  DeleteRoleMemberList(params) {
    return api.post("DeleteRoleMemberList", params);
  },
  GetRoleReportPC(params) {
    return api.get("GetRoleReportPC", params);
  },
  SetRoleReportPC(params) {
    return api.post("SetRoleReportPC", params);
  },
  GetRoleReportMobile(params) {
    return api.get("GetRoleReportMobile", params);
  },
  SetRoleReportMobile(params) {
    return api.post("SetRoleReportMobile", params);
  },

  //查询员工
  queryEmployee(params) {
    return api.get("QueryEmployee", params);
  },
  // 获取员工信息
  getEmployee(params) {
    return api.get("GetEmployee", params);
  },
};
