import { param } from "@/utils";
import HttpApi from "./libs/api.request";

const controller = "home";

const api = new HttpApi(controller);

export default {
  //查询消息
  queryMsg(params) {
    return api.get("QueryMsg", params);
  },
  //获取消息
  getMsg(params) {
    return api.get("GetMsg", params);
  },
  //发送消息
  sendMsg(params) {
    return api.post("SendMsg", params);
  },
  //删除消息
  deleteMsg(params) {
    return api.post("DeleteMsg", params);
  },
};
