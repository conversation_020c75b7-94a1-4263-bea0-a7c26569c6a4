import HttpApi from './libs/api.request'

const controller = 'MasterData'

const api = new HttpApi(controller)

export default {
  NewGuid() {
    return api.get('NewGuid')
  },
  GetEnumInfos(params) {
    return api.get('GetEnumInfos', params)
  },

  GetDicts(params) {
    return api.get('GetDictsByParent', params)
  },
  QueryDict(params) {
    return api.get('QueryDict', params)
  },
  DeleteDict(params) {
    return api.post('DeleteDict', params)
  },
  // 查询排班
	getDoctorOutCalls(data) {
		return api.get('GetDoctorOutCalls', data, true)
	},
  AddDict(params) {
    return api.post('AddDict', params)
  },
  UpdateDict(params) {
    return api.post('UpdateDict', params)
  },
  GetDict(params) {
    return api.get('GetDict', params)
  },

  // 查询业务字典
  queryBizDict(params) {
    return api.get('QueryBizDict', params)
  },
  // 获取业务字典
  getBizDict(params) {
    return api.get('GetBizDict', params)
  },
  // 删除业务字典
  delectBizDict(params) {
    return api.post('DelectBizDict', params)
  },
  // 更新业务字典
  updateBizDict(params) {
    return api.post('UpdateBizDict', params)
  },
  // 新增业务字典
  addBizDict(params) {
    return api.post('AddBizDict', params)
  },

  // 查询机构
  queryCustomer(params) {
    return api.get('QueryCustomer', params)
  },
  // 获取机构
  getCustomer(params) {
    return api.get('GetCustomer', params)
  },
  // 删除机构
  deleteCustomer(params) {
    return api.post('DeleteCustomer', params)
  },
  // 更新机构
  updateCustomer(params) {
    return api.post('UpdateCustomer', params)
  },
  // 新增机构
  addCustomer(params) {
    return api.post('AddCustomer', params)
  },
  // 审核机构
  auditCustomer(params) {
    return api.post('AuditCustomer', params)
  },
  // 冻结机构
  freezeCustomer(params) {
    return api.post('FreezeCustomer', params)
  },
  // 解冻机构
  unFreezeCustomer(params) {
    return api.post('UnFreezeCustomer', params)
  },

  // 查询机构分类
  queryCustomerCategory(params) {
    return api.get('QueryCustomerCategory', params)
  },
  // 获取机构分类
  getCustomerCategory(params) {
    return api.get('GetCustomerCategory', params)
  },
  // 删除机构分类
  deleteCustomerCategory(params) {
    return api.post('DeleteCustomerCategory', params)
  },
  // 更新机构分类
  updateCustomerCategory(params) {
    return api.post('UpdateCustomerCategory', params)
  },
  // 新增机构分类
  addCustomerCategory(params) {
    return api.post('AddCustomerCategory', params)
  },

  // 查询机构审核
  queryCustomerReview(params) {
    return api.get('QueryCustomerReview', params)
  },
  // 获取机构审核
  getCustomerReview(params) {
    return api.get('GetCustomerReview', params)
  },
  // 机构审核
  auditCustomerReview(params) {
    return api.post('AuditCustomerReview', params)
  },

  // 查询客户
  queryContact(params) {
    return api.get('QueryContact', params)
  },
  // 新增客户
  addContact(params) {
    return api.post('AddContact', params)
  },
  // 编辑客户
  updateContact(params) {
    return api.post('UpdateContact', params)
  },
  // 删除客户
  deleteContact(params) {
    return api.post('DeleteContact', params)
  },
  // 升级讲者
  upgradeSpeaker(params) {
    return api.post('UpgradeSpeaker', params)
  },

  // 获取客户
  getContact(params) {
    return api.get('GetContact', params)
  },

  // 查询客户审核
  queryContactReview(params) {
    return api.get('QueryContactReview', params)
  },
  // 获取客户审核
  getContactReview(params) {
    return api.get('GetContactReview', params)
  },
  // 客户审核
  auditContactReview(params) {
    return api.post('AuditContactReview', params)
  },

  // 查询产品
  queryProduct(params) {
    return api.get('QueryProduct', params)
  },
  // 获取产品
  getProduct(params) {
    return api.get('GetProduct', params)
  },

  // 查询文档资料
  queryDocResource(params) {
    return api.get('QueryDocResource', params)
  },
  // 新增文档资料
  addDocResource(params) {
    return api.post('AddDocResource', params)
  },
  // 批量上传
  uploadDocResourceAttachs(params) {
    return api.post('UploadDocResourceAttachs', params)
  },
  // 删除文档资料
  deleteDocResource(params) {
    return api.post('DeleteDocResource', params)
  },
  // 编辑文档资料
  updateDocResource(params) {
    return api.post('UpdateDocResource', params)
  },
  // 获取文档资料树
  queryDocResourceTree(params) {
    return api.get('QueryDocResourceTree', params)
  },
  // 获取文档资料
  getDocResource(params) {
    return api.get('GetDocResource', params)
  },
  // 获取会议类型
  queryMeetingCategory(params) {
    return api.get('QueryMeetingCategory', params)
  },
  getMeetingCategory(params) {
    return api.get('GetMeetingCategory', params)
  },
  // 新增会议类型
  addMeetingCategory(params) {
    return api.post('AddMeetingCategory', params)
  },
  // 删除会议类型
  deleteMeetingCategory(params) {
    return api.post('DeleteMeetingCategory', params)
  },
  // 编辑会议类型
  updateMeetingCategory(params) {
    return api.post('UpdateMeetingCategory', params)
  },
  // 获取医院开发类型
  queryHospitalDevelopCategory(params) {
    return api.get('QueryHospitalDevelopCategory', params)
  },
  // 查询医院开发类型
  getHospitalDevelopCategory(params) {
    return api.get('GetHospitalDevelopCategory', params)
  },
  // 新增医院开发类型
  addHospitalDevelopCategory(params) {
    return api.post('AddHospitalDevelopCategory', params)
  },
  // 删除医院开发类型
  deleteHospitalDevelopCategory(params) {
    return api.post('DeleteHospitalDevelopCategory', params)
  },
  // 编辑医院开发类型
  updateHospitalDevelopCategory(params) {
    return api.post('UpdateHospitalDevelopCategory', params)
  },
  // 获取医院开发阶段
  queryHospitalDevelopPhase(params) {
    return api.get('QueryHospitalDevelopPhase', params)
  },
  // 查询医院开发阶段
  getHospitalDevelopPhase(params) {
    return api.get('GetHospitalDevelopPhase', params)
  },
  // 新增医院开发阶段
  addHospitalDevelopPhase(params) {
    return api.post('AddHospitalDevelopPhase', params)
  },
  // 删除医院开发阶段
  deleteHospitalDevelopPhase(params) {
    return api.post('DeleteHospitalDevelopPhase', params)
  },
  // 编辑医院开发阶段
  updateHospitalDevelopPhase(params) {
    return api.post('UpdateHospitalDevelopPhase', params)
  },
  // 查询题库数据
  QueryQuestion(params) {
    return api.get('QueryQuestion', params)
  },
  // 删除问题
  DeleteQuestion(params) {
    return api.post('DeleteQuestion', params)
  },
  // 新增问题
  AddQuestion(params) {
    return api.post('AddQuestion', params)
  },
  // 修改问题
  UpdateQuestion(params) {
    return api.post('UpdateQuestion', params)
  },
  // 获取问题
  GetQuestion(params) {
    return api.get('GetQuestion', params)
  },
  // end of api

  queryContactCustomerSelector(params) {
    return api.get('QueryContactCustomerSelector', params)
  },
  queryContactProductLineSelector(params) {
    return api.get('QueryContactProductLineSelector', params)
  },
	// 执行人
	queryOwnerEmployeeSelector(params) {
		return api.get('QueryOwnerEmployeeSelector', params)
	},
	// 查询选择客户列表
	queryCustomerSelector(params) {
		return api.get('QueryCustomerSelector', params)
	}
}
