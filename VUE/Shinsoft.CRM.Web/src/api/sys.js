import HttpApi from './libs/api.request'

const controller = 'sys'

const api = new HttpApi(controller)

export default {
  userLogin(data, processResult = true) {
    return api.post('UserLogin', { data, processResult })
  },
  userLogout(processResult = true) {
    return api.get('UserLogout', { processResult })
  },
  changeIdentity(processResult = true) {
    return api.get('ChangeIdentity', { processResult })
  },
  getCurrentUser(processResult = true) {
    return api.get('GetCurrentUser', { processResult })
  },
  updateUserPwd(data) {
    return api.post('ChangeMyPwd', { data })
  },

  // 通过SSO Token 跳转登录
  userLoginBySsoToken(params) {
    return api.post('UserLoginBySsoToken', params)
  },

  autoLogin(params) {
    return api.post('AutoLogin', params)
  },

  // 获取地图配置地址
  getMapBaseUrlForWeb() {
    return api.get('GetMapBaseUrlForWeb')
  },
  getApiUri() {
    return api.get('GetApiUri')
  }
}
