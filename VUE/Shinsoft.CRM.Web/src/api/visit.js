import HttpApi from './libs/api.request'

const controller = 'Visit'

const api = new HttpApi(controller)

export default {
  NewGuid() {
    return api.get('NewGuid')
  },
  getEnumInfos(params) {
    return api.get('GetEnumInfos', params)
  },

  // 查询拜访
  queryDailyVisit(params) {
    return api.get('QueryDailyVisit', params)
  },
	// 查询客户
	queryContactSelector(params) {
		return api.get('QueryContactSelector', params)
	},
  // 获取日拜访
  getDailyVisit(params) {
    return api.get('GetDailyVisit', params)
  },
  // 查询拜访历史
  queryVisitHistory(params) {
    return api.get('QueryVisitHistory', params)
  },
	// 执行人
	queryOwnerEmployeeSelector(data) {
		return api.get('QueryOwnerEmployeeSelector', data)
	},
	queryEmployeeSelector(data) {
		return api.get('QueryEmployeeSelector', data)
	},
	// 查询医院开发员工成员选择项
	queryEmployeeMemberSelector(data) {
		return api.get('QueryEmployeeMemberSelector', data)
	},
	queryEventProductSelector() {
		return api.get('QueryEventProductSelector')
	},
  queryContactCustomerSelector(params) {
    return api.get('QueryContactCustomerSelector', params)
  },
	// 查询选择客户列表
	queryCustomerSelector(params) {
		return api.get('QueryCustomerSelector', params)
	},
	queryModeEnumInfos(data) {
		return api.get('QueryModeEnumInfos', data)
	},
	// 查询拜访形式
	queryModeSelector(data) {
		return api.get('QueryModeSelector', data)
	},
	// 查询日时间段枚举项
	queryDaySlotEnumInfos(data) {
		return api.get('QueryDaySlotEnumInfos', data)
	},
	queryGoalSelector(data) {
		return api.get('QueryGoalSelector', data)
	},
	queryEventContentSelector(data) {
		return api.get('QueryEventContentSelector', data)
	},
	// 新增拜访
	addVisitPlans(data) {
		return api.post('AddVisitPlans', data)
	},
	// 新增协防
	addExtraVisits(data) {
		return api.post('AddExtraVisits', data)
	},
	// 查询拜访结果
	queryResultSelector(data) {
		return api.get('QueryResultSelector', data)
	},
	// 新增反馈
	addVisitFeedback(data) {
		return api.post('AddVisitFeedback', data)
	},
	// 查询反馈列表
	queryVisitFeedback(data) {
		return api.get('QueryVisitFeedback', data)
	},
	// 查询反馈
	getVisitFeedback(data) {
		return api.get('GetVisitFeedback', data)
	},
	// 查询协访评分
	queryVisitScore(data) {
		return api.get('QueryVisitScore', data)
	},
	// 下属拜访
	querySubVisit(data) {
		return api.get('QuerySubVisit', data)
	},
	// 查询活动评分选择项
	queryEventScoreDictSelector(data) {
		return api.get('QueryEventScoreDictSelector', data)
	},
	// 新增协访评分
	addVisitScore(data) {
		return api.post('AddVisitScore', data)
	},
	// 查询我的拜访
	queryMyDailyVisit(data) {
		return api.get('QueryMyDailyVisit', data)
	},
  // 查询拜访计划审核数据
  queryVisitReview(params) {
    return api.get('QueryVisitReview', params)
  },
  // 获取拜访审核
  getVisitReview(params) {
    return api.get('GetVisitReview', params)
  },
	// 拜访审批
	auditVisitReview(data) {
		return api.post('AuditVisitReview', data)
	},
	// 批量审批
	batchAuditVisitReview(data) {
		return api.post('BatchAuditVisitReview', data)
	},
	updateVisitDate(data) {
		return api.post('UpdateVisitDate', data)
	}
}
