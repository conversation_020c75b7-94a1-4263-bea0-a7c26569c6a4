import { file } from 'jszip/lib/object'
import HttpApi from './libs/api.request'

const controller = 'File'

const api = new HttpApi(controller)

export default {
  upload(file, formData) {
    const fd = new FormData()

    if (formData) {
      for (const [key, valuefor] of formData) {
        fd.append(key, valuefor)
      }
    }
    fd.append('file', file)
    return api.postForm('upload', fd)
  },
  uploadImage(file, formData) {
    const fd = new FormData()

    if (formData) {
      for (const [key, valuefor] of formData) {
        fd.append(key, valuefor)
      }
    }
    fd.append('file', file)
    return api.postForm('UploadImage', fd)
  },
  download(id) {
    return api.get(`DownloadFile/${id}`)
  },
  downloadAttachment(id) {
    return api.post('DownloadAttachment', {
      data: { id: id },
      responseType: 'arraybuffer'
    })
  },
  uploadTemplate(file, formData, controller, method) {
    const uploadController = controller
    const uploadApi = new HttpApi(uploadController)
    const uploadfd = new FormData()
    if (formData) {
      for (const [key, valuefor] of formData) {
        uploadfd.append(key, valuefor)
      }
    }
    uploadfd.append('file', file)
    return uploadApi.postForm(method, uploadfd)
  },
  uploadAttach(file, formData) {
    if (!formData) {
      formData = new FormData()
    }
    formData.append('file', file)
    return api.postForm('UploadAttach', formData)
  },

  // 批量上传
  uploadAttachs(formData) {
    return api.postForm('UploadAttachs', formData)
  },
  // 删除附件
  deleteAttach(params) {
    return api.post('DeleteAttach', params)
  }
}
