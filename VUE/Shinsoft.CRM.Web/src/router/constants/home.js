/* Layout */
import Layout from '@/layout'

export default [{
  name: 'login',
  path: '/login',
  component: () =>
    import('@/views/login'),
  hidden: true,
  meta: {
    title: '登录'
  }
},
{
  name: 'sso',
  path: '/sso',
  component: () =>
    import('@/views/ssologin'),
  hidden: true,
  meta: { title: 'sso登录', anonymous: true }
},
{
  name: '_home',
  path: '/',
  component: Layout,
  redirect: '/home',
  children: [{
    name: 'home',
    path: 'home',
    component: () =>
      import('@/views/home'),
    meta: { title: '首页', icon: 'home', affix: true }
  }]
},
{
  path: '/icon',
  component: Layout,
  redirect: '/icon/index',
  hidden: true,
  children: [{
    path: 'index',
    component: () =>
      import('@/views/icons'),
    name: 'Icons',
    meta: { title: 'Icons', icon: 'icon', noCache: true, anonymous: true }
  }]
},
{
  path: '/documentation',
  component: Layout,
  hidden: true,
  children: [{
    path: 'index',
    component: () =>
      import('@/views/documentation'),
    name: 'Documentation',
    meta: { title: 'Documentation', icon: 'documentation', anonymous: true }
  }]
}
]
