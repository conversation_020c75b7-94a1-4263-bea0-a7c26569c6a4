
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */
import { isArray } from '@/utils/validate'

const constantFiles = require.context('./constants', true, /\.js$/)
const asyncFiles = require.context('./modules', true, /\.js$/)

export const constantRoutes = constantFiles.keys().reduce((constantRoutes, path) => {
  // set './app.js' => 'app'
  const name = path.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = constantFiles(path)
  if (name !== 'end') {
    if (isArray(value.default)) {
      value.default.forEach(route => {
        constantRoutes.push(route)
      })
    } else {
      constantRoutes.push(value.default)
    }
  }
  return constantRoutes
}, [])

export const endRoutes = constantFiles.keys().reduce((endRoutes, path) => {
  // set './app.js' => 'app'
  const name = path.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = constantFiles(path)
  if (name === 'end') {
    if (isArray(value.default)) {
      value.default.forEach(route => {
        endRoutes.push(route)
      })
    } else {
      endRoutes.push(value.default)
    }
  }
  return endRoutes
}, [])

export const asyncRoutes = asyncFiles.keys().reduce((asyncRoutes, path) => {
  // set './app.js' => 'app'
  // const name = path.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = asyncFiles(path)
  if (isArray(value.default)) {
    value.default.forEach(route => {
      asyncRoutes.push(route)
    })
  } else {
    asyncRoutes.push(value.default)
  }
  return asyncRoutes.sort((r1, r2) => {
    const s1 = r1.meta && r1.meta.sort
      ? r1.meta.sort
      : 99999
    const s2 = r2.meta && r2.meta.sort
      ? r2.meta.sort
      : 99999

    if (s1 === s2) {
      return 0
    } else if (s1 < s2) {
      return -1
    } else {
      return 1
    }
  })
}, [])
