/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from "@/layout";

const personRoutes = [
  {
    path: "/profile",
    component: Layout,
    redirect: "/profile/index",
    hidden: true,
    alwaysShow: true,
    meta: {
      title: "个人中心",
      sort: 100,
      icon: "person-setting",
    },
    children: [
      {
        name: "EditPwd",
        path: "editPwd",
        hidden: true,
        component: () => import("@/views/profile/editPwd"),
        meta: {
          title: "修改密码",
          icon: "pwd-setting",
          sort: 2,
          noCache: false,
        },
      },
    ],
  },
];

export default personRoutes;
