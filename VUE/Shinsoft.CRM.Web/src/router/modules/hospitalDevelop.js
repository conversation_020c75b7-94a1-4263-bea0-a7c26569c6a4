/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const hospitalDevelopRoutes = [
  {
    path: '/hospitalDevelop',
    component: Layout,
    redirect: '/hospitalDevelop/hospitalDevelop',
    hidden: false,
    alwaysShow: true,
    meta: {
      title: '医院开发',
      sort: 4,
      icon: 'department'
    },
    children: [
      {
        name: 'hospitalDevelop',
        path: 'hospitalDevelop',
        component: () => import('@/views/hospitalDevelop/hospitalDevelop.vue'),
        meta: {
          title: '医院开发',
          icon: '',
          noCache: false,
          permissions: []
        }
      },
      {
        name: 'hospitalDevelopReview',
        path: 'hospitalDevelopReview',
        component: () => import('@/views/hospitalDevelop/auditHospitalDevelop.vue'),
        meta: {
          title: '医院开发审批',
          icon: '',
          noCache: false,
          permissions: []
        }
      }
    ]
  }
]

export default hospitalDevelopRoutes
