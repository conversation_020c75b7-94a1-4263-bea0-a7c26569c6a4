/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from "@/layout";

const masterData = [
  {
    path: "/masterData",
    component: Layout,
    redirect: "/masterData/index",
    hidden: false,
    alwaysShow: true,
    meta: {
      title: "主数据维护",
      sort: 3,
      icon: "clipboard",
      permissions: ["Customer_Query_All",
                    "Customer_Query_Owner",
                    "Customer_Audit_All",
                    "Customer_Audit_Sub",
                    "CustomerCategory_Maintain",
                    'Contact_Query_All',
                    "Contact_Query_Owner",
                    'Contact_Audit_All',
                    'Contact_Audit_Sub',
                    'BizDict_Maintain',
                    'Product_Query',
                    'DocResource_Query',
                    'meetingCategory_Query',
                    'Question_Query',
                    'Question_Maintain'
                  ],
    },
    children: [
      {
        name: "cuctomer",
        path: "customer",
        component: () => import("@/views/masterData/customer/index"),
        meta: {
          title: "机构管理",
          icon: "",
          noCache: false,
          permissions: ["Customer_Query_All", "Customer_Query_Owner"],
        },
      },
      {
        name: "cuctomerReview",
        path: "cuctomerReview",
        component: () => import("@/views/masterData/customerReview/index"),
        meta: {
          title: "机构审批",
          icon: "",
          noCache: false,
          permissions: ["Customer_Query_All", "Customer_Query_Owner", "Customer_Audit_All", "Customer_Audit_Sub"],
        },
      },
      {
        name: "customerCategory",
        path: "customerCategory",
        component: () => import("@/views/masterData/customerCategory/index"),
        meta: {
          title: "机构分类管理",
          icon: "",
          noCache: false,
          permissions: ["CustomerCategory_Maintain"],
        },
      },
      {
        name: "contact",
        path: "contact",
        component: () => import("@/views/masterData/contact/index"),
        meta: {
          title: "客户管理",
          icon: "",
          noCache: false,
          permissions: ['Contact_Query_All', "Contact_Query_Owner"],
        },
      },
      {
        name: "contactReview",
        path: "contactReview",
        component: () => import("@/views/masterData/contactReview/index"),
        meta: {
          title: "客户审核",
          icon: "",
          noCache: false,
          permissions: ['Contact_Query_All', "Contact_Query_Owner", 'Contact_Audit_All', 'Contact_Audit_Sub'],
        },
      },

      {
        name: "dict",
        path: "dict",
        component: () => import("@/views/masterData/dict/index"),
        meta: {
          title: "业务字典管理",
          icon: "",
          noCache: false,
          permissions: ['BizDict_Maintain'],
        },
      },

      {
        name: "product",
        path: "product",
        component: () => import("@/views/masterData/product/index"),
        meta: {
          title: "产品管理",
          icon: "",
          noCache: false,
          permissions: ['Product_Query'],
        },
      },
      {
        name: "docResource",
        path: "docResource",
        component: () => import("@/views/masterData/docResource/index"),
        meta: {
          title: "资料管理",
          icon: "",
          noCache: false,
          permissions: ['DocResource_Query','DocResource_Maintain'],
        },
      },
      {
        name: "meetingCategory",
        path: "meetingCategory",
        component: () => import("@/views/masterData/meetingCategory/index"),
        meta: {
          title: "会议类型",
          icon: "",
          noCache: false,
          permissions: ['MeetingCategory_Query','MeetingCategory_Maintain'],
        },
      },
      {
        name: "hospitalDevelopCategory",
        path: "hospitalDevelopCategory",
        component: () => import("@/views/masterData/hospitalDevelopCategory/index"),
        meta: {
          title: "医院开发类型",
          icon: "",
          noCache: false,
          permissions: ['HospitalDevelopCategory_Query','HospitalDevelopCategory_Maintain'],
        },
      },
      {
        name: "question",
        path: "question",
        component: () => import("@/views/masterData/question/index"),
        meta: {
          title: "题库",
          icon: "",
          noCache: false,
          permissions: ['Question_Query' , 'Question_Maintain']
        }
      },
    ],
  },
];

export default masterData;
