/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from "@/layout";

const reportRoutes = [
  {
    path: "/report",
    component: Layout,
    redirect: "/report/index",
    hidden: false,
    alwaysShow: false,
    alwaysShowSelf: true,
    meta: {
      title: "报表",
      sort: 15,
      icon: "report-increase",
    },
    children: [
      {
        name: "visitDetail",
        path: "visitDetail",
        component: () => import("@/views/report/visitDetail/index"),
        meta: {
          title: "拜访明细报表",
          icon: "",
          noCache: false,
          permissions: ['Report_PC_View_All', { Report_PC_View: 'Report_PC_VisitDetail' }]
        }
      },
      {
        name: "trainingComplete",
        path: "trainingComplete",
        component: () => import("@/views/report/trainingComplete/index"),
        meta: {
          title: "培训完成报表",
          icon: "",
          noCache: false,
          permissions: ['Report_PC_View_All', { Report_PC_View: 'Report_PC_TrainingComplete' }]
        }
      },
      {
        name: "trainingReport",
        path: "trainingReport",
        component: () => import("@/views/report/training/index"),
        meta: {
          title: "培训课程报表",
          icon: "",
          noCache: false,
          permissions: ['Report_PC_View_All', { Report_PC_View: 'Report_PC_TrainingReport' }]
        }
      },
      {
        name: "meetingReport",
        path: "meetingReport",
        component: () => import("@/views/report/meeting/index"),
        meta: {
          title: "会议报表",
          icon: "",
          noCache: false,
          permissions: ['Report_PC_View_All', { Report_PC_View: 'Report_PC_MeetingReport' }]
        }
      },
    ],
  },
];

export default reportRoutes;
