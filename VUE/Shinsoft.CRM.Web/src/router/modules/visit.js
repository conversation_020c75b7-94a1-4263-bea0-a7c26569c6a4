/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    permissions: ['admin','editor']    control the page permissions (you can set multiple permissions)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

import Layout from '@/layout'

const visitRoutes = [
  {
    path: '/visit',
    component: Layout,
    redirect: '/visit/index',
    hidden: false,
    alwaysShow: true,
    meta: {
      title: '拜访',
      sort: 3,
      icon: 'organization'
    },
    children: [
      {
        name: 'visitPlan',
        path: 'visitPlan',
        component: () => import('@/views/visit/index.vue'),
        meta: {
          title: '拜访计划',
          icon: '',
          noCache: false,
          permissions: []
        }
      },
      {
        name: 'visitReview',
        path: 'visitReview',
        component: () => import('@/views/visit/auditVisit'),
        meta: {
          title: '拜访审批',
          icon: '',
          noCache: false,
          permissions: []
        }
      },
      // {
      //   name: 'visitFeedback',
      //   path: 'visitFeedback',
      //   component: () => import('@/views/masterData/customerCategory/index'),
      //   meta: {
      //     title: '拜访反馈',
      //     icon: '',
      //     noCache: false,
      //     permissions: []
      //   }
      // },
      {
        name: 'subVisit',
        path: 'subVisit',
        component: () => import('@/views/visit/subVisit'),
        meta: {
          title: '下属拜访',
          icon: '',
          noCache: false,
          permissions: []
        }
      }
    ]
  }
]

export default visitRoutes
