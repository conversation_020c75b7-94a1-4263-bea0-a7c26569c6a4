export default {

	showType: {
		type: String,
		default: 'icon'
	},
	title: {
		type: String,
		default: '请选择员工'
	},
	// 数据来源
	// masterData 默认
	// meeting 参会
	// hospitalDevelop 医院开发
	// training 培训
	// visit 拜访
	sourceType: {
		type: String,
		required: true
	},
	// 数据类型  employee/member/owner
	sourceMode: {
		type: String,
		default: () => {
			return 'employee'
		}
	},
	// 是否显示多选
	showSelectAll: {
		type: Boolean,
		default: false
	},
	chooseType: {
		type: String,
		default: () => {
			return 'multiple'
		}
	},
	placeholder: {
		type: String,
		default: () => {
			return '请选择'
		}
	},
	change: {
		type: [Function, null],
		default: null
	},
	initData: {
		type: Array,
		default: () => {
			return []
		}
	}
}
