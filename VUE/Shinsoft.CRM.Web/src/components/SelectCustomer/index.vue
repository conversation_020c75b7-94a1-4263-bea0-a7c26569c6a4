<template>
  <div>
    <el-row>
      <el-col>
        <el-input @focus="handleShowSelect" @clear="clearSelect" v-model="customerName" :placeholder="placeholder" prefix-icon="el-icon-search" clearable ref="retInput">
        </el-input>
      </el-col>
    </el-row>
    <SelectCustomer :showDialog.sync="showSelectDialog" :value.sync="selValue" :customerName.sync="customerName" :title="title"></SelectCustomer>
  </div>
</template>
<script>
import { string } from 'clipboard';

import SelectCustomer from './components/selectCustomer.vue'
import selector from '@/api/selector'
export default {
  components: {
    SelectCustomer
  },
  props: {
    value: {
      type: string,
      default: ""
    },
    title: {
      type: string,
      default: "选择机构"
    },
    placeholder: {
      type: string,
      default: "选择机构"
    }
  },
  data() {
    return {
      showSelectDialog: false,
      selValue: "",
      customerName: ""
    }
  },
  methods: {
    handleShowSelect() {
      this.showSelectDialog = true;
      this.$refs.retInput.blur();
    },
    clearSelect() {
      this.selValue = null;
    },
    getCustomerById(customerId) {
      selector.getCustomerById({ customerId: customerId }).then(res => {
        this.customerName = res.data.name;
      }).catch(res => { })
    }
  },
  watch: {
    selValue(val) {
      this.$emit("update:value", val)
    },
    value: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function (val) {
        if (val) {
          if (!this.customerName) {
            this.getCustomerById(val);
          } else {
            this.selValue = val;
          }
        }
      }
    }

  }

}
</script>

<style scoped>
.tipMessage {
  font-size: 9px !important;
  color: tomato;
}
</style>