<template>
  <div>
    <el-dialog append-to-body :title="title" width="75%" :close-on-click-modal="false" :visible="showDialog" @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="span">
            <el-input v-model="listQuery.keywords" clearable placeholder="关键词" class="filter-item" @keyup.enter.native="handleFilter" />
          </el-col>
          <el-col :span="span">
            <el-select v-model="listQuery.Types" class="filter-item" placeholder="类型" multiple collapse-tags clearable>
              <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-select v-model="listQuery.Statuses" class="filter-item" placeholder="状态" multiple collapse-tags clearable>
              <el-option v-for="item in statusList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table v-loading="listLoading" :data="list" stripe border fit highlight-current-row style="width: 100%" :default-sort="{ prop: 'createTime', order: 'descending' }" :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass" @sort-change="sortChange">
              <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
              <el-table-column label="机构名称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.customerName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="简称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.customerShortName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="性质" min-width="60px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumTypeDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="90px" header-align="center" align="center" prop="EnumStatus">
                <template slot-scope="{ row }">
                  <span>{{ row.enumStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数据状态" width="94px" header-align="center" align="center" prop="EnumReviewState">
                <template slot-scope="{ row }">
                  <span>{{ row.enumReviewStateDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="60" min-width="90" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择" @click="handleCheck(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import SelectorApi from '@/api/selector'
import masterAPI from '@/api/masterData'

export default {
  components: {
    Pagination
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    supplierId: {
      type: String,
      default: ''
    },
    isFilter: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "选择机构"
    }
  },
  data() {
    return {
      span: 5,
      listLoading: false,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      },
      list: [],
      statusList: [],
      typeList: [],
      dialogStatus: '',
      total: 0,
    }
  },
  watch: {
    showDialog: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function (val) {
        if (val === true) {
          this.initStatusList()
          this.initTypeList()
          this.list = []
          this.total = 0
          this.getList()
        }
      }
    }
  },
  methods: {
    created() {

    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    handleModeChange() {
      this.listQuery.customerNo = ''
      this.getList()
    },
    getList() {
      this.listLoading = true
      masterAPI.queryContactCustomerSelector(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
      }).catch(res => { })
    },

    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'CustomerType' }
      SelectorApi.getEnumInfos(param).then((result) => {
        this.typeList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取机构状态枚举
    initStatusList() {
      var param = { enumType: 'CustomerStatus' }
      SelectorApi.getEnumInfos(param).then((result) => {
        this.statusList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      this.$emit("update:value", row.customerId);
      this.$emit("update:customerName", row.customerName);
      this.$emit("update:showDialog", false);
    },
    cancleDialog() {
      this.$emit("update:showDialog", false);
    },
    clear() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '-CreateTime'
      }
    }
  }
}
</script>
<style scoped>
</style>
