<template>
  <div class="gs-week-tab">
    <div class="gs-week-row">
      <div class="gs-day-time">

      </div>
      <div class="tab-week">
        <div class="week-item ">
          <div>
            日
          </div>
        </div>
        <div class="week-item ">
          <div>
            一
          </div>
        </div>
        <div class="week-item ">
          <div>
            二
          </div>
        </div>
        <div class="week-item ">
          <div>
            三
          </div>
        </div>
        <div class="week-item ">
          <div>
            四
          </div>
        </div>
        <div class="week-item ">
          <div>
            五
          </div>
        </div>
        <div class="week-item ">
          <div>
            六
          </div>
        </div>
      </div>
    </div>
    <div v-for="(call,index) in outCalls" :key="index" class="gs-week-row week-item-padding">
      <div class="gs-day-time">
        {{ call.enumOutCallTimeDesc }}
      </div>
      <div class="tab-week">
        <div class="week-item " @click="canEdit?call.sunday=!call.sunday:''">
          <div v-if="canEdit" :class="call.sunday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.sunday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.monday=!call.monday:''">
          <div v-if="canEdit" :class="call.monday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.monday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.tuesday=!call.tuesday:''">
          <div v-if="canEdit" :class="call.tuesday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.tuesday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.wednesday=!call.wednesday:''">
          <div v-if="canEdit" :class="call.wednesday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.wednesday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.thursday=!call.thursday:''">
          <div v-if="canEdit" :class="call.thursday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.thursday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.friday=!call.friday:''">
          <div v-if="canEdit" :class="call.friday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.friday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
        <div class="week-item " @click="canEdit?call.saturday=!call.saturday:''">
          <div v-if="canEdit" :class="call.saturday?'icon-checked':''" class="iconfont icon-banxiao icon-font-size"></div>
          <div v-else>
            <div v-if="call.saturday" class="iconfont icon-banxiao icon-font-size icon-checked"></div>
            <!-- <div v-else style="color: #9c9c9c;">无</div> -->
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  props: {
    outCalls: {
			type: Array,
			default: () => {
				return []
			}
		},
		canEdit: {
			type: Boolean,
			default: false
		}
  },
  data() {
    return {

    }
  },
  watch: {
    outCalls: {
      handler(newVal, oldVal) {
        this.$emit('outCalls', newVal)
      },
      immediate: true
    }
  }
}
</script>
<style>
	.gs-week-tab {
		width: 100%;
	}

	.gs-week-row {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.gs-day-time {
		width: 10%;
		line-height: 250%;
	}

	.gs-week {
		padding: 10px 0.5vw;
		display: flex;
		flex-direction: row;
		height: 50px;
		border-top: 1px solid #ccc;
	}

	.week-item {
		display: flex;
		flex-direction: column;
		flex: 1;
		text-align: center;
		justify-content: space-between;
		padding: 5px;
	}

	.week-item-padding {
		padding: 10px 0px;
	}

	.tab-week {
		display: flex;
		flex-direction: row;
		width: 90%;
	}

	.icon-font-size {
		font-size: 1.2rem;
		color: #9c9c9c;
	}

	.icon-checked {
		color: #55aaff;
	}
</style>
