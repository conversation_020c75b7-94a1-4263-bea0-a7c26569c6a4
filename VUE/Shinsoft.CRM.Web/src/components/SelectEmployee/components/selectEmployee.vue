<template>
  <div>
    <el-dialog
      append-to-body
      :title="title"
      width="75%"
      :close-on-click-modal="false"
      :visible="showDialog"
      @close="cancleDialog()"
    >
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="4">
            <el-input
              v-model="listQuery.jobNo"
              clearable
              placeholder="工号"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="listQuery.displayName"
              clearable
              placeholder="请输入姓名"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="listQuery.email"
              clearable
              placeholder="请输入email"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :span="span">
            <el-button
              class="filter-item-button"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="list-container">
        <el-row>
          <el-col :span="24">
            <el-table
              v-loading="listLoading"
              :data="list"
              stripe
              border
              fit
              highlight-current-row
              style="width: 100%"
              :default-sort="{ prop: 'JobNo', order: 'ascending' }"
              :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass"
              @sort-change="sortChange"
            >
              <el-table-column
                fixed
                label="序号"
                :index="indexMethod"
                type="index"
                align="center"
              />
              <el-table-column
                label="工号"
                min-width="100px"
                align="center"
                header-align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.jobNo }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="姓名"
                min-width="120px"
                align="left"
                header-align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.displayName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="邮箱"
                min-width="150px"
                align="left"
                header-align="center"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.email }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="状态"
                width="80px"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span
                    v-if="row.enumStatus == 1"
                  >{{ row.enumStatusDesc }}
                  </span>
                  <span
                    v-if="row.enumStatus != 1"
                    style="color: red"
                  >离职</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                align="center"
                header-align="center"
                width="70"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="{ row }">
                  <i
                    style="color: #13ce66"
                    class="el-icon-circle-check eltablei"
                    title="选择"
                    @click="handleCheck(row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize"
              @pagination="getList"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import SelectorApi from '@/api/selector'

export default {
  components: {
    Pagination
  },
  // props: {
  //   showDialog: {
  //     type: Boolean,
  //     default: false
  //   },
  //   value: {
  //     type: String,
  //     default: ''
  //   },
  //   employeeName: {
  //     type: String,
  //     default: ''
  //   },
  //   title: {
  //     type: String,
  //     default: '选择员工'
  //   }
  // },
  inject: {
    selectedData: {
      from: 'selectedData',
      default: []
    },
    title: {
      type: String,
      default: '选择员工'
    }
  },
  data() {
    return {
      span: 5,
      showDialog: false,
      listLoading: false,
      showPagination: true,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        order: '+JobNo'
      },
      list: []
    }
  },
  created() {

  },
  methods: {
    init() {
      this.showDialog = true
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      this.listLoading = true
      SelectorApi.queryEmployeeSelector(this.listQuery).then(res => {
        this.listLoading = false
        this.list = res.data.datas
        this.total = res.data.recordCount
        this.listQuery.pageIndex = res.data.pageIndex
        this.showPagination = this.total > this.listQuery.pageSize
      }).catch(res => { })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    handleCheck(row) {
      // this.$emit('update:value', row.id)
      // this.$emit('update:employeeName', row.displayName)
      // this.$emit("update:showDialog", false);
      this.$emit('change', row)
      this.cancleDialog()
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '+JobNo'
      }
      this.showDialog = false
    }
  }
}
</script>
<style scoped>
</style>
