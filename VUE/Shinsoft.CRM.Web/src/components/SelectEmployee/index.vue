<template>
  <div>
    <el-row>
      <el-col>
        <el-input
          ref="retInput"
          v-model="employeeName"
          :placeholder="placeholder"
          prefix-icon="el-icon-search"
          clearable
          @focus="handleShowSelect"
          @clear="clearSelect"
        >
        </el-input>
      </el-col>
    </el-row>
    <selectEmployee
      ref="selectEmployee"
      :value.sync="selValue"
      :employee-name.sync="employeeName"
      @change="change"
    ></selectEmployee>
  </div>
</template>
<script>
import selectEmployee from './components/selectEmployee.vue'
import selector from '@/api/selector'
export default {
  components: {
    selectEmployee
  },
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      showSelectDialog: false,
      selValue: '',
      employeeName: ''
    }
  },
  watch: {
    selValue(val) {
      this.$emit('update:value', val)
    },
    value: {
      immediate: true, // 将立即以表达式的当前值触发回调
      handler: function (val) {
        if (val) {
          if (!this.employeeName) {
            this.getEmployeeById(val)
          } else {
            this.selValue = val
          }
        }
      }
    }
  },
  methods: {
    handleShowSelect() {
      this.$refs.selectEmployee.init()
      this.$refs.retInput.blur()
    },
    clearSelect() {
      this.selValue = null
      this.employeeName = ''
    },
    change(data) {
      this.$emit('change', data)
    },
    getEmployeeById(employeeId) {
      selector.getEmployeeById({ employeeId: employeeId }).then(res => {
        this.employeeName = res.data.displayName
      }).catch(res => { })
    }
  }
}
</script>

<style scoped>
.tipMessage {
  font-size: 9px !important;
  color: tomato;
}
</style>
