<template>
  <div class="components-dialog">
    <el-row>
      <el-col>
        <!-- <el-input
          ref="retInput"
          v-model="employeeName"
          :placeholder="placeholder"
          prefix-icon="el-icon-search"
          clearable
          @focus="handleShowSelect"
          @clear="clearSelect"
        >
        </el-input> -->
        <el-input v-if="showType === 'input' && chooseType === 'single'" v-model="displayName"
          :placeholder="placeholder" prefix-icon="el-icon-search" @focus="handleShowSelect"
          @clear="clearSelect"></el-input>
        <i v-else-if="showType === 'icon'" class="el-icon-user-solid" style="
            font-size: 1.3rem;
            cursor: pointer;
            line-height: 170%;
            color: #304156;
          " @click="handleShowSelect"></i>
        <el-button v-else-if="showType === 'button'" size="mini" type="primary" icon="el-icon-plus"
          @click="handleShowSelect"> 添加</el-button>
      </el-col>
    </el-row>

    <!--选择人-->
    <el-dialog append-to-body :title="title" width="75%" :close-on-click-modal="false" :visible="showDialog"
      @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="span">
            <el-input v-model="listQuery.keywords" clearable placeholder="关键词" class="filter-item" />
          </el-col>
          <el-col :span="span">
            <el-select v-model="listQuery.enumCustomerType" class="filter-item" placeholder="类型" multiple collapse-tags
              clearable>
              <el-option v-for="item in typeList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>
          <!-- <el-col :span="span">
            <el-select
              v-model="listQuery.Statuses"
              class="filter-item"
              placeholder="状态"
              multiple
              collapse-tags
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.desc"
                :value="item.value"
              />
            </el-select>
          </el-col> -->
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row>
          <el-col :span="24">
            <el-table :data="dataList" stripe border fit highlight-current-row style="width: 100%"
              :default-sort="{ prop: 'createTime', order: 'descending' }" :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass" @select-all="handleSelectionAll" @select="handleSelectionRow">
              <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
              <el-table-column label="机构名称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="简称" min-width="120px" header-align="center" align="left">
                <template slot-scope="{ row }">
                  <span>{{ row.shortName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="性质" min-width="60px" header-align="center" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumTypeDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="90px" header-align="center" align="center" prop="EnumStatus">
                <template slot-scope="{ row }">
                  <span>{{ row.enumStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数据状态" width="94px" header-align="center" align="center" prop="EnumReviewState">
                <template slot-scope="{ row }">
                  <span>{{ row.enumReviewStateDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="chooseType === 'single'" fixed="right" label="操作" align="center"
                header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择"
                    @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
              <el-table-column v-else type="selection" width="55">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
        <el-button v-if="chooseType === 'multiple'" type="primary" icon="el-icon-check" @click="submit">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import visitApi from '@/api/visit'
import meetingApi from '@/api/meeting'
import hospitalDevelopApi from '@/api/hospitalDevelop'
import trainingApi from '@/api/training'
import masterData from '@/api/masterData'
import selectorApi from '@/api/selector'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showPagination: false,
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      dataList: [],
      displayName: '',
      selectedData: [],
      resultData: [],
      typeList: [],
      statusList: []
    }
  },
  created() {
    this.initTypeList()
    // this.initStatusList()
  },
  methods: {
    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'CustomerType' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.typeList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取机构状态枚举
    // initStatusList() {
    //   var param = { enumType: 'CustomerStatus' }
    //   selectorApi.getEnumInfos(param).then((result) => {
    //     this.statusList = result.data.datas
    //   })
    //     .catch((error) => {
    //       console.log(error)
    //     })
    // },
    handleShowSelect() {
      this.showDialog = true
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      if (this.enumCustomerType) {
        this.listQuery.enumCustomerType = this.enumCustomerType
      }
      switch (this.sourceType) {
        case '':
        case 'masterData':
          masterData.queryContactCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        case 'visit':
          visitApi.queryCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        case 'hospitalDevelop':
          hospitalDevelopApi.queryCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        case 'mainSpeaker':
        case 'speaker':
          meetingApi.queryCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        case 'meeting':
          this.listQuery.id = this.customerId
          meetingApi.queryCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        case 'training':
          trainingApi.queryCustomerSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
            }
          })
          break
        default:
          break
      }
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 单选
    handleSingleCheck(row) {
      this.displayName = row.name
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(全选)
    handleSelectionAll(selections) {
      if (!this.sourceType || this.sourceType === 'masterData') {
        if (selections) {
          selections.map(sel => {
            if (!this.resultData.some(res => { return res.memberId === sel.memberId })) {
              this.resultData.push(sel)
            }
          })
        } else {
          this.dataList.map(sel => {
            const index = this.resultData.findIndex((item) => {
              return item.memberId === sel.memberId
            })
            this.resultData.splice(index, 1)
          })
        }
      } else {
        if (selections.length > 0) {
          selections.map(sel => {
            if (!this.resultData.some(res => { return res.id === sel.id })) {
              this.resultData.push(sel)
            }
          })
        } else {
          this.dataList.map(sel => {
            const index = this.resultData.findIndex((item) => {
              return item.id === sel.id
            })
            this.resultData.splice(index, 1)
          })
        }
      }
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (!this.sourceType || this.sourceType === 'masterData') {
        if (selection.some(sel => { return sel.memberId === row.memberId })) {
          if (!this.resultData.some(res => { return res.memberId === row.memberId })) {
            this.resultData.push(row)
          }
        } else {
          const index = this.resultData.findIndex((item) => {
            return item.memberId === row.memberId
          })
          this.resultData.splice(index, 1)
        }
      } else {
        if (selection.some(sel => { return sel.id === row.id })) {
          if (!this.resultData.some(res => { return res.id === row.id })) {
            this.resultData.push(row)
          }
        } else {
          const index = this.resultData.findIndex((item) => {
            return item.id === row.id
          })
          this.resultData.splice(index, 1)
        }
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('至少选择一个')
      }
    },
    clearSelect() {
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    }
  }

}
</script>
<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}

::v-deep th .el-checkbox {
  display: none !important;
}
</style>
