export default {
	showType: {
		type: String,
		default: 'icon'
	},
	title: {
		type: String,
		default: '请选择机构'
	},
	// 数据来源
	// masterData 默认
	// meeting 参会人
	// hospitalDevelop 医院开发
	// training 培训
	// visit 拜访
	sourceType: {
		type: String,
		required: true
	},
	// 多选还是单选  multiple / single
	chooseType: {
		type: String,
		default: () => {
			return 'multiple'
		}
	},
	placeholder: {
		type: String,
		default: () => {
			return '请选择'
		}
	},
  customerId: {
    type: String,
    default: () => {
      return ""
    }
	},
	change: {
		type: [Function, null],
		default: null
	}
}
