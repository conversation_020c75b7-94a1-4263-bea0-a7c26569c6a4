<template>
  <div>
    <chooseCustomer ref="chooseCustomer" :show-type="showType" :choose-type="'single'" :source-type="sourceType"
      :placeholder="placeholder" :title="title" :customerId="customerId" @change="handleChooseCustomer">
    </chooseCustomer>
    <chooseContact v-if="customer" ref="chooseContact" :source-type="sourceType" :placeholder="placeholder"
      :choose-type="chooseType" :show-type="'none'" :customer-id="customer.id" :product-ids="productIds"
      :speaker="speaker" :event-mode="eventMode" :disabledEventMode="disabledEventMode" :show-extend="showExtend"
      :init-data="initData" :show-select-all="showSelectAll" @change="handleChooseContact">
    </chooseContact>
  </div>
</template>
<script>
import props from './props'
import chooseCustomer from '../ChooseCustomer/index.vue'
import chooseContact from '../ChooseContact/index.vue'
import selectorApi from '@/api/selector'
export default {
  components: {
    chooseCustomer,
    chooseContact
  },
  props: props,
  data() {
    return {
      customer: null,
      meetingMode: []
      // extendModel: {
      //   enumEventMode: 1
      // }
    }
  },
  created() {
    // console.log(this.eventMode)
    // if (this.showExtend) {
    // this.queryModes()
    // }
  },
  methods: {
    handleChooseCustomer(data) {
      this.customer = data
      this.$nextTick(() => {
        this.$refs.chooseContact.handleShowSelect()
      })
    },
    handleChooseContact(contacts) {
      this.$emit('change', this.customer, contacts)
    }

  }
}
</script>

<style scoped>
/* .components-dialog  ::v-deep .el-input__validateIcon {
  display: none;
} */
</style>
