export default {
	// 显示类型
	showType: {
		type: String,
		default: 'icon'
	},
	// 标题
	title: {
		type: String,
		default: '请选择机构'
	},
	// 数据来源  controller
	sourceType: {
		type: String,
		required: true
	},
	// 是否是讲者
	speaker: {
		type: Boolean,
		default: false
	},
	// 是否启用扩展
	showExtend: {
		type: Boolean,
		default: false
	},
	// 会议形式
	eventMode: {
		type: Number
	},
	// 是否显示多选
	showSelectAll: {
		type: Boolean,
		default: false
	},
	// 多选还是单选  multiple / single
	chooseType: {
		type: String,
		default: () => {
			return 'multiple'
		}
	},
	// 文本框提示  type是input时生效
	placeholder: {
		type: String,
		default: () => {
			return '请选择'
		}
	},
	productIds: {
		type: Array,
		default: () => {
			return []
		}
	},
  customerId: {
    type: String,
    default: () => {
      return ""
    }
	},
	change: {
		type: [Function, null],
		default: null
	},
  disabledEventMode: {
    type: Boolean,
    default: false
	},
	initData: {
		type: Array,
		default: () => {
			return []
		}
	}
}
