<template>
  <div>
    <el-row>
      <el-col>
        <el-input v-if="showType === 'input' && chooseType === 'single'" v-model="displayName"
          :placeholder="placeholder" prefix-icon="el-icon-search" @focus="handleShowSelect"
          @clear="clearSelect"></el-input>
        <i v-else-if="showType === 'icon'" class="el-icon-user-solid" style="
            font-size: 1.3rem;
            cursor: pointer;
            line-height: 170%;
            color: #304156;
          " @click="handleShowSelect"></i>
      </el-col>
    </el-row>

    <!--选择人-->
    <el-dialog append-to-body :title="title" width="75%" :close-on-click-modal="false" :visible="showDialog"
      @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="span">
            <el-input v-model="listQuery.keywords" clearable placeholder="姓名" class="filter-item"
              @keyup.enter.native="handleFilter" />
          </el-col>
          <el-col :span="span">
            <el-select v-model="selFlags" class="filter-item" placeholder="类型" multiple collapse-tags clearable>
              <el-option v-for="item in flagList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>

          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row :class="showSelectAll && chooseType === 'multiple' ? '' : 'components-dialog'">
          <el-col :span="24">
            <el-table ref="refTable" :data="dataList" stripe border fit highlight-current-row style="width: 100%"
              :default-sort="{ prop: 'createTime', order: 'descending' }" :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass" @select-all="handleSelectionAll" @select="handleSelectionRow">
              <el-table-column fixed label="序号" type="index" align="center" :index="indexMethod" />
              <el-table-column label="姓名" width="120px" header-align="center" align="left" prop="name" />
              <el-table-column label="所属机构" min-width="120px" header-align="center" align="left"
                prop="customerName"></el-table-column>
              <el-table-column label="部门" header-align="center" align="center" prop="dept">
              </el-table-column>
              <el-table-column label="职务" header-align="center" align="center" prop="title">
              </el-table-column>
              <el-table-column label="标签" header-align="center" align="center" prop="enumFlagsDesc">
              </el-table-column>
              <el-table-column v-if="chooseType === 'single'" fixed="right" label="操作" align="center"
                header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择"
                    @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
              <el-table-column v-else type="selection" width="55">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
        <el-button v-if="chooseType === 'multiple'" type="primary" icon="el-icon-check" @click="submit">
          提交
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-if="showExtend" append-to-body :title="extendDialogTitle" :width="extendDialogWidth"
      :close-on-click-modal="false" :visible="showSlotDialog" :show-close="false">
      <el-form ref="dataForm" :rules="rules" :model="extendModel" label-position="right" label-width="100px">
        <el-form-item label="参会形式" prop="eventProductIds">
          <el-select :disabled="disabledEventMode" v-model="extendModel.enumEventMode" placeholder="参会形式"
            style="width: 100%" @change="changeEventMode">
            <el-option v-for="item in meetingMode" :key="item.value" :label="item.desc" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主题" prop="meetingTopic">
          <el-input v-model="extendModel.meetingTopic" type="textarea" :rows="3" maxlength="200" show-word-limit
            placeholder="请输入参会主题"></el-input>
        </el-form-item>
      </el-form>
      <!-- <template></template> -->
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleSlotDialog()">
          关闭
        </el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveSlotDialog()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import meetingApi from '@/api/meeting'
import hospitalDevelopApi from '@/api/hospitalDevelop'
import visitApi from '@/api/visit'
import trainingApi from '@/api/training'
import selectorApi from '@/api/selector'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showSlotDialog: false,
      showPagination: false,
      total: 0,
      selFlags: [],
      listQuery: {
        pageIndex: 1,
        pageSize: 10
      },
      dataList: [],
      displayName: '',
      selectedData: [],
      resultData: [],
      flagList: [],
      extendModel: { meetingTopic: '' },
      meetingMode: [],
      rules: {
        meetingTopic: [
          { required: true, message: '请输入主题', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.initTypeList()
    // if (this.showExtend) {
    this.queryModes()
    // }
  },
  methods: {
    // 获取机构类型枚举
    initTypeList() {
      var param = { enumType: 'ContactFlag' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.flagList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    handleShowSelect() {
      this.showDialog = true
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      // this.resultData = JSON.parse(JSON.stringify(this.initData))
      if (this.selFlags) {
        delete this.listQuery.enumFlags
        this.selFlags.map(flag => {
          this.listQuery.enumFlags = this.listQuery.enumFlags | flag
        })
      }

      this.listQuery.customerId = this.customerId
      switch (this.sourceType) {
        case 'speaker':
          this.listQuery.productIds = this.productIds
          meetingApi.querySpeakerMemberSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
              this.initSelect()
            }
          })
          break
        case 'mainSpeaker':
        case 'meeting':
          meetingApi.queryContactMemberSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
              this.initSelect()
            }
          })
          break
        case 'hospitalDevelop':
          meetingApi.queryContactSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
              this.initSelect()
            }
          })
          break
        case 'training':
          trainingApi.queryContactSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
              this.initSelect()
            }
          })
          break
        case 'visit':
          visitApi.queryContactSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.showPagination = this.total > this.listQuery.pageSize
              this.initSelect()
            }
          })
          break
        default:
          break
      }
    },
    checkedValid(row) {
      return row.canChecked
    },
    initSelect() {
      // this.$nextTick(() => {
      //   if (this.resultData.length > 0) {
      //     this.resultData.map(item => {
      //       if (this.dataList.some(s => { return (item.id && item.id === s.id) || (item.memberId && item.memberId === s.memberId) })) {
      //         var scitem = this.dataList.find(f => { return (item.id && item.id === f.id) || (item.memberId && item.memberId === f.memberId) })

      //         this.$refs.refTable.toggleRowSelection(scitem, true)
      //       } else {
      //         this.$refs.refTable.toggleRowSelection(item, false)
      //       }
      //     })

      //     this.dataList.map(item => {
      //           if (this.resultData.some(s => { return (item.id && item.id === s.id) || (item.memberId && item.memberId === s.memberId) })) {
      //             item.canChecked = false
      //           } else {
      //             item.canChecked = true
      //           }
      //           return item
      //         })
      //   }
      // })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    queryModes() {
      const data = {
        enumType: 'EventMode',
        group: 'Member'
      }
      selectorApi.getEnumInfos(data).then((res) => {
        if (res) {
          this.meetingMode = res.data.datas
          if (this.showExtend) {
            this.extendModel.enumEventMode = this.eventMode === 3 || this.eventMode === 0 ? null : this.eventMode
            this.extendModel.enumEventModeDesc = this.eventMode === 3 || this.eventMode === 0 ? '' : this.meetingMode.filter(filter => { return filter.value === this.eventMode })[0].desc
          }
        }
      })
    },
    changeEventMode(mode) {
      // this.extendModel.enumEventMode = this.eventMode === 3 || this.eventMode === 0 ? null : this.eventMode
      this.extendModel.enumEventModeDesc = this.meetingMode.filter(filter => { return filter.value === mode })[0].desc
    },
    // 单选
    handleSingleCheck(row) {
      this.displayName = row.name
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(全选)
    handleSelectionAll(selections) {
      if (this.sourceType === 'meeting' || this.sourceType === 'speaker' || this.sourceType === 'mainSpeaker') {
        if (selections) {
          selections.map(sel => {
            if (!this.resultData.some(res => { return res.memberId === sel.memberId })) {
              this.resultData.push(sel)
            }
          })
        } else {
          this.dataList.map(sel => {
            const index = this.resultData.findIndex((item) => {
              return item.memberId === sel.memberId
            })
            this.resultData.splice(index, 1)
          })
        }
      } else {
        if (selections.length > 0) {
          selections.map(sel => {
            if (!this.resultData.some(res => { return res.id === sel.id })) {
              this.resultData.push(sel)
            }
          })
        } else {
          this.dataList.map(sel => {
            const index = this.resultData.findIndex((item) => {
              return item.id === sel.id
            })
            this.resultData.splice(index, 1)
          })
        }
      }
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (this.showExtend) {
        if (this.speaker && this.eventMode !== 0 && this.eventMode !== 3) {
          row.enumEventMode = this.eventMode
          row.enumEventModeDesc = this.meetingMode.filter(filter => { return filter.value === this.eventMode })[0].desc
        } else {
          row.enumEventMode = null
          row.enumEventModeDesc = ''
        }
        if (selection.some(s => { return s.memberId === row.memberId })) {
          this.extendModel = row
          this.showSlotDialog = true
        } else {
          this.extendModel = {}
        }
      }
      if (this.sourceType === 'meeting' || this.sourceType === 'speaker' || this.sourceType === 'mainSpeaker') {
        if (selection.some(sel => { return sel.memberId === row.memberId })) {
          if (!this.resultData.some(res => { return res.memberId === row.memberId })) {
            this.resultData.push(row)
          }
        } else {
          const index = this.resultData.findIndex((item) => {
            return item.memberId === row.memberId
          })
          this.resultData.splice(index, 1)
        }
      } else {
        if (selection.some(sel => { return sel.id === row.id })) {
          if (!this.resultData.some(res => { return res.id === row.id })) {
            this.resultData.push(row)
          }
        } else {
          const index = this.resultData.findIndex((item) => {
            return item.id === row.id
          })
          this.resultData.splice(index, 1)
        }
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('至少选择一个')
      }
    },
    clearSelect() {
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    },
    saveSlotDialog() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var keys = Object.keys(this.extendModel)
          var index = this.resultData.findIndex(f => { return (f.id && f.id === this.extendModel.id) || (f.memberId && f.memberId === this.extendModel.memberId) })
          keys.map(key => {
            this.$set(this.resultData[index], key, this.extendModel[key])
          })
          this.showSlotDialog = false
          this.extendModel = {}
        }
      })
    },
    cancleSlotDialog() {
      var index = this.resultData.findIndex(f => { return f.id === this.extendModel.id || f.memberId === this.extendModel.memberId })
      this.resultData.splice(index, 1)
      this.$refs.refTable.toggleRowSelection(this.extendModel, false)
      this.showSlotDialog = false
      this.extendModel = {}
    }
  }

}
</script>
<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}

.components-dialog ::v-deep th .el-checkbox {
  display: none !important;
}
</style>
