import { number } from 'echarts/lib/export'

export default {
	modelValue: {
		type: [Array, Object]
	},
	showType: {
		type: String,
		default: 'icon'
	},
	title: {
		type: String,
		default: '请选择客户'
	},
	// 数据来源
	// masterData 默认
	// mainSpeaker 主讲
	// speaker 讲者
	// meeting 参会人
	// hospitalDevelop 医院开发
	// training 培训
	// visit 拜访
	sourceType: {
		type: String,
		required: true
	},
	// 是否显示多选
	showSelectAll: {
		type: Boolean,
		default: false
	},
	initData: {
		type: Array,
		default: () => {
			return []
		}
	},
	customerId: {
		type: String,
		default: () => {
			return ''
		}
	},
	// 多选还是单选  multiple / single
	chooseType: {
		type: String,
		default: () => {
			return 'multiple'
		}
	},
	placeholder: {
		type: String,
		default: () => {
			return '请选择'
		}
	},
	// 讲者类型
	speaker: {
		type: Boolean,
		default: false
	},
	eventMode: {
		type: Number
	},
	zIndex: {
		type: [Number, String],
		default: 100
	},
	closeable: {
		type: Boolean,
		default: false
	},
	productIds: {
		type: Array,
		default: () => {
			return []
		}
	},
  disabledEventMode: {
    type: Boolean,
    default: false
	},
	change: {
		type: [Function, null],
		default: null
	},
	// 扩展弹窗
	showExtend: {
		type: Boolean,
		default: false
	},
	extendDialogWidth: {
		type: String,
		default: '40%'
	},
	extendDialogTitle: {
		type: String,
		default: '扩展信息'
	}
}
