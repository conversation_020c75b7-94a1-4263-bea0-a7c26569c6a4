<template>
  <div>
    <div class="divider">
      <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @click.prevent.native="handleCheckAll">全选</el-checkbox>
    </div>
    <el-checkbox-group
      v-model="checkAllGroup"
      @change="checkAllGroupChange"
    >
      <el-row :gutter="10">
        <el-col v-for="(value,key) in column" :key="value" :label="key" :span="6">
          <el-checkbox :key="value" :label="key">{{ key }}</el-checkbox>
        </el-col>
      </el-row>
    </el-checkbox-group>
    <div slot="footer">
      <el-row>
        <el-col :span="24">
          <el-row type="flex" justify="end" :gutter="10" style="margin-top: 20px;">
            <el-col :span="2.5">
              <el-button icon="el-icon-close" @click="handlecancel()">关闭</el-button>
            </el-col>
            <el-col :span="2.5">
              <el-button type="primary" icon="el-icon-check" @click="handleSubmit()">保存</el-button>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    column: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      indeterminate: false,
      checkAll: false,
      checkAllGroup: []
    }
  },
  computed: {
    allColumn() {
      const array = Object.keys(this.column).map(key => key)
      return array
    }
  },
  watch: {
    column(val) {
      this.indeterminate = false
      this.checkAll = true
      for (const key in val) {
        this.checkAllGroup.push(key)
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.indeterminate = false
      this.checkAll = true
      for (const key in this.column) {
        this.checkAllGroup.push(key)
      }
    },
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false

      if (this.checkAll) {
        this.checkAllGroup = this.allColumn
      } else {
        this.checkAllGroup = []
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.allColumn.length) {
        this.indeterminate = false
        this.checkAll = true
      } else if (data.length > 0) {
        this.indeterminate = true
        this.checkAll = false
      } else {
        this.indeterminate = false
        this.checkAll = false
      }
    },
    handleSubmit() {
      // 检查是否选中列
      if (this.checkAllGroup.length === 0) {
        this.showMessage('未选择任何列，请重新选择需要导出的列', 'info')
        return
      }
      this.$emit('exportSubmitEvent', this.checkAllGroup)
      this.indeterminate = false
      this.checkAll = false
      this.checkAllGroup = []
    },
    handlecancel() {
      this.indeterminate = false
      this.checkAll = false
      this.checkAllGroup = []
      this.$emit('exportCancelEvent')
    },
    showMessage(msg, type) {
      this.$message({
        showClose: true,
        message: msg,
        type: type,
        duration: 1500
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .divider {
  border-bottom: 1px solid #e9e9e9;
  padding-bottom: 6px;
  margin-bottom: 6px;
}
</style>
