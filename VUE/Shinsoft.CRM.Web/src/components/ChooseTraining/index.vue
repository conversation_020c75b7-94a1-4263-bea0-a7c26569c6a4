<template>
  <div class="components-dialog">
    <el-row>
      <el-col>
        <el-input v-model="subject" :placeholder="placeholder" prefix-icon="el-icon-search" clearable
          @focus="handleShowSelect" @clear="clearSelect"></el-input>
      </el-col>
    </el-row>

    <el-dialog append-to-body :title="title" width="60%" :close-on-click-modal="false" :visible="showDialog"
      @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="6">
            <el-input v-model="listQuery.keywords" clearable placeholder="请输入主题" class="filter-item" />
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row class="components-dialog">
          <el-col :span="24">
            <el-table ref="refTable" :data="dataList" stripe border fit highlight-current-row style="width: 100%"
              :header-cell-class-name="'tableStyle'" :row-class-name="handleRowClass">
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="类型" width="130px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.typeName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="BU" min-width="100px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.businessUnitName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="主题" min-width="150px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.title }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" header-align="center" width="70"
                class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择"
                    @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import selectorApi from '@/api/selector'
import trainingApi from '@/api/training'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showPagination: false,
      questionTypeList: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        enumType: [],
      },
      dataList: [],
      subject: '',
      selectedData: [],
      resultData: []
    }
  },
  methods: {
    handleShowSelect() {
      this.showDialog = true
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    getList() {
      trainingApi.queryTrainingSelector(this.listQuery).then((res) => {
        if (res) {
          this.dataList = res.data.datas
          this.total = res.data.recordCount
          this.listQuery.pageIndex = res.data.pageIndex
          this.showPagination = this.total > this.listQuery.pageSize
        }
      })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 单选
    handleSingleCheck(row) {
      this.subject = row.title
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (selection.some(sel => { return sel.eventId === row.eventId })) {
        if (!this.resultData.some(res => { return res.eventId === row.eventId })) {
          this.resultData.push(row)
        }
      } else {
        const index = this.resultData.findIndex((item) => {
          return item.eventId === row.eventId
        })
        this.resultData.splice(index, 1)
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('请至少选择一个')
      }
    },
    clearSelect() {
      this.$emit('change', null)
      this.cancleDialog()
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    }
  }

}
</script>

<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}

.components-dialog ::v-deep th .el-checkbox {
  display: none !important;
}
</style>
