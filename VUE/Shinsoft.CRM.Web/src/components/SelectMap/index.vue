<template>
  <div>
    <el-row>
      <el-col v-if="showText" :span="24">
        <table class="selectButton" style="width:100%;margin-top:-3px">
          <tr>
            <td>
              <el-input v-model="mapFormModel.address" clearable show-word-limit :placeholder="placeholder?placeholder:'请输入地址'" maxlength="200" @clear="inputClear" />
            </td>
            <td style="width:25px">
              <el-button type="primary" icon="el-icon-map-location" title="定位地址" @click="addressClick" />
            </td>
          </tr>
        </table>
      </el-col>
      <el-col v-if="(!showText) && mapBaseUrl && mapFormModel.addressLocX" :span="24">
        <iframe frameborder="0" border="0" :src="mapBaseUrl + 'flag=1&Method=0&Coord=' + mapFormModel.addressLocX + ',' + mapFormModel.addressLocY" width="100%" height="300px"></iframe>
      </el-col>
    </el-row>
    <sfMap ref="mapPage" @locationSelected="updateLocation"></sfMap>
  </div>
</template>

<script>
import sfMap from './components/sfMap.vue'
import sysApi from '@/api/sys'

export default {
  name: '',
  components: {
    sfMap,
    sysApi
  },
  props: {
    showText: {
      type: Boolean,
      default: false
    },
    address: {
      type: String,
      default: ''
    },
    addressLocX: {
      type: Number,
      default: 0
    },
    addressLocY: {
      type: Number,
      default: 0
    },
    placeholder: {
      type: String,
      default: ''
    },
    objectId: {
      // 用作刷新
      type: String,
      default: ''
    }
  },
  data() {
    return {
      span: 12,

      mapFormModel: {},
      mapBaseUrl: '',
      isBrowserSupport: false
    }
  },
  watch: {
    'mapFormModel.address'(val) {
      this.$emit('update:value', val)
    },
    address(val) {
      this.$set(this.mapFormModel, 'address', val)
    },
    addressLocX(val) {
      this.$set(this.mapFormModel, 'addressLocX', val)
    },
    addressLocY(val) {
      this.$set(this.mapFormModel, 'addressLocY', val)
    }

  },
  mounted() {
  },
  created() {
    this.checkBrowser()
    this.init()
  },
  methods: {
    init() {
      this.mapBaseUrl = window.sessionStorage.getItem('MapBaseUrl')
      if (!this.mapBaseUrl) {
        sysApi.getMapBaseUrlForWeb().then((result) => {
          window.sessionStorage.setItem('MapBaseUrl', result.data)

          this.$nextTick(() => {
            this.mapBaseUrl = result.data
          })
        })
          .catch((error) => {
            console.log(error)
          })
      }
    },
    checkBrowser() {
      const userAgent = window.navigator.userAgent.toLowerCase()
      const isChrome = userAgent.indexOf('chrome') > -1 && userAgent.indexOf('edg') < 0

      if (isChrome) {
        this.isBrowserSupport = false
      } else {
        this.isBrowserSupport = true
      }
    },
    addressClick() {
      if (!this.isBrowserSupport) {
        if (this.mapFormModel && (!this.mapFormModel.addressLocX || this.mapFormModel.addressLocX == 0)) {
          this.mapFormModel.addressLocX = 31.228725
        }
        if (this.mapFormModel && (!this.mapFormModel.addressLocY || this.mapFormModel.addressLocY == 0)) {
          this.mapFormModel.addressLocY = 121.475186
        }
      }

      this.$refs.mapPage.init(this.mapFormModel)
    },
    updateLocation(locationData) {
      if (locationData.data.data) {
        const addressInfo = locationData.data.data.arg
        this.$set(this.mapFormModel, 'address', addressInfo.address)
        const locations = addressInfo.location.split(',')

        this.$set(this.mapFormModel, 'addressLocX', parseFloat(locations[0]))
        this.$set(this.mapFormModel, 'addressLocY', parseFloat(locations[1]))
        this.$set(this.mapFormModel, 'regions', [addressInfo.nation, addressInfo.province, addressInfo.city, addressInfo.district])
        this.$forceUpdate()

        if (this.showText) {
          this.$emit('addressSelected', this.mapFormModel)
        }
      }
    },
    inputClear() {
      this.$set(this.mapFormModel, 'address', '')

      this.$set(this.mapFormModel, 'addressLocX', undefined)
      this.$set(this.mapFormModel, 'addressLocY', undefined)
      this.$set(this.mapFormModel, 'regions', [])
      this.$forceUpdate()

      if (this.showText) {
        this.$emit('addressSelected', this.mapFormModel)
      }
    }
  }

}
</script>

<style>
.selectButton .el-button--small {
  padding: 5px 5px;
  font-size: 12px;
  border-radius: 3px;
  width: 25px;
  height: 25px;
}
</style>
