export default {

  showType: {
    type: String,
    default: 'icon'
  },
  title: {
    type: String,
    default: '请选择题目'
  },
  // 数据来源
  // masterData 默认
  // meeting 参会
  // hospitalDevelop 医院开发
  // training 培训
  // visit 拜访
  sourceType: {
    type: String,
    required: true
  },
	// 是否显示多选
	showSelectAll: {
		type: Boolean,
		default: true
	},
	initData: {
		type: Array,
		default: () => {
			return []
		}
	},
  chooseType: {
    type: String,
    default: () => {
      return 'multiple'
    }
  },
  placeholder: {
    type: String,
    default: () => {
      return '请选择'
    }
  },
  change: {
    type: [Function, null],
    default: null
  }
}
