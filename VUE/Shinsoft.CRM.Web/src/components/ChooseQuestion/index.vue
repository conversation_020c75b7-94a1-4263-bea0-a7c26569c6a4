<template>
  <div class="components-dialog">
    <el-row>
      <el-col>
        <el-input v-if="showType === 'input' && chooseType === 'single'" v-model="subject" :placeholder="placeholder"
          prefix-icon="el-icon-search" @focus="handleShowSelect" @clear="clearSelect"></el-input>
        <i v-else class="el-icon-plus" style="
            font-size: 1.3rem;
            cursor: pointer;
            line-height: 170%;
            color: cornflowerblue;
          " @click="handleShowSelect">添加</i>
      </el-col>
    </el-row>

    <el-dialog append-to-body :title="title" width="60%" :close-on-click-modal="false" :visible="showDialog"
      @close="cancleDialog()">
      <div class="search-container-bg">
        <el-row :gutter="10" type="flex" class="filiter-container">
          <el-col :span="6">
            <el-input v-model="listQuery.subject" clearable placeholder="请输入主题" class="filter-item" />
          </el-col>
          <el-col :span="6">
            <el-input v-model="listQuery.keyWord" clearable placeholder="请输入关键字" class="filter-item" />
          </el-col>
          <el-col :span="6">
            <el-select v-model="listQuery.enumType" class="filter-item" placeholder="题型" multiple collapse-tags
              clearable>
              <el-option v-for="item in questionTypeList" :key="item.value" :label="item.desc" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="span">
            <el-button class="filter-item-button" type="primary" icon="el-icon-search" @click="handleFilter">
              查询
            </el-button>
          </el-col>
        </el-row>
      </div>
      <div class="dataList-container">
        <el-row :class="showSelectAll && chooseType === 'multiple'
          ? ''
          : 'components-dialog'
          ">
          <el-col :span="24">
            <el-table ref="refTable" :data="dataList" stripe border fit highlight-current-row style="width: 100%"
              :default-sort="{ prop: 'Subject', order: 'ascending' }" :header-cell-class-name="'tableStyle'"
              :row-class-name="handleRowClass" @select-all="handleSelectionAll" @select="handleSelectionRow">
              <el-table-column fixed label="序号" :index="indexMethod" type="index" align="center" />
              <el-table-column label="题型" width="100px" align="center" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.enumQuestionTypeDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column label="主题" min-width="120px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.subject }}</span>
                </template>
              </el-table-column>
              <el-table-column label="关键字" min-width="120px" align="left" header-align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.keyWord }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="chooseType === 'single'" fixed="right" label="操作" align="center"
                header-align="center" width="70" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <i style="color: #13ce66" class="el-icon-circle-check eltablei" title="选择"
                    @click="handleSingleCheck(row)" />
                </template>
              </el-table-column>
              <el-table-column v-else type="selection" width="55">
              </el-table-column>
            </el-table>
          </el-col>
          <el-col v-if="showPagination" class="el-colRight">
            <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageIndex"
              :limit.sync="listQuery.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="cancleDialog()">
          关闭
        </el-button>
        <el-button v-if="chooseType === 'multiple'" type="primary" icon="el-icon-check" @click="submit">
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import props from './props'
import Pagination from '@/components/Pagination'
import selectorApi from '@/api/selector'
import visitApi from '@/api/visit'
import meetingApi from '@/api/meeting'
import hospitalDevelopApi from '@/api/hospitalDevelop'
import trainingApi from '@/api/training'

export default {
  components: {
    Pagination
  },
  props: props,
  data() {
    return {
      span: 4,
      showDialog: false,
      showPagination: false,
      questionTypeList: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        enumType: [],
        order: '+Subject'
      },
      dataList: [],
      subject: '',
      selectedData: [],
      resultData: []
    }
  },
  methods: {
    handleShowSelect() {
      this.showDialog = true
      this.initTypesList()
      this.getList()
    },
    indexMethod(index) {
      return (
        (this.listQuery.pageIndex - 1) * this.listQuery.pageSize + index + 1
      )
    },
    checkedValid(row) {
      return row.canChecked
    },
    getList() {
      this.resultData = this.initData
      switch (this.sourceType) {
        case 'training':
          trainingApi.queryQuestionSelector(this.listQuery).then((res) => {
            if (res) {
              this.dataList = res.data.datas
              this.total = res.data.recordCount
              this.listQuery.pageIndex = res.data.pageIndex
              this.showPagination = this.total > this.listQuery.pageSize
              // this.initSelect()

              // this.dataList.map(item => {
              //   if (!this.resultData.some(s => { return item.questionId && item.questionId === s.questionId })) {
              //     item.canChecked = false
              //   } else {
              //     item.canChecked = true
              //   }
              //   return item
              // })
            }
          })
          break

        default:
          break
      }
    },
    initSelect() {
      this.$nextTick(() => {
        if (this.resultData.length > 0) {
          this.resultData.map(item => {
            if (this.dataList.some(s => { return item.questionId && item.questionId === s.questionId })) {
              var scitem = this.dataList.find(f => { return (item.questionId && item.questionId === f.questionId) })
              this.$refs.refTable.toggleRowSelection(scitem, true)
            } else {
              this.$refs.refTable.toggleRowSelection(item, false)
            }
          })
        }
      })
    },
    initTypesList() {
      var param = { enumType: 'QuestionType' }
      selectorApi.getEnumInfos(param).then((result) => {
        this.questionTypeList = result.data.datas
      })
        .catch((error) => {
          console.log(error)
        })
    },
    handleFilter() {
      this.listQuery.pageIndex = 1
      this.getList()
    },
    sortChange(column, prop, order) {
      this.listQuery.pageIndex = 1
      let orderSymbol = ''
      if (column.order === 'descending') {
        orderSymbol = '-'
      }
      if (column.order === 'ascending') {
        orderSymbol = '+'
      }
      this.listQuery.order = orderSymbol + column.prop
      this.getList()
    },
    handleRowClass(row, rowIndex) {
      if (row.rowIndex % 2 === 0) {
        return 'cellStyle'
      } else {
        return 'stripedStyle'
      }
    },
    // 单选
    handleSingleCheck(row) {
      this.subject = row.subject
      this.$emit('change', row)
      this.cancleDialog()
    },
    // 多选(全选)
    handleSelectionAll(selections) {
      if (selections.length > 0) {
        selections.map(sel => {
          if (!this.resultData.some(res => { return res.questionId === sel.questionId })) {
            this.resultData.push(sel)
          }
        })
      } else {
        this.dataList.map(sel => {
          const index = this.resultData.findIndex((item) => {
            return item.questionId === sel.questionId
          })
          this.resultData.splice(index, 1)
        })
      }
    },
    // 多选(单选)
    handleSelectionRow(selection, row) {
      if (selection.some(sel => { return sel.questionId === row.questionId })) {
        if (!this.resultData.some(res => { return res.questionId === row.questionId })) {
          this.resultData.push(row)
        }
      } else {
        const index = this.resultData.findIndex((item) => {
          return item.questionId === row.questionId
        })
        this.resultData.splice(index, 1)
      }
    },
    submit() {
      if (this.resultData.length > 0) {
        this.$emit('change', this.resultData)
        this.cancleDialog()
      } else {
        this.$message.error('请至少选择一个')
      }
    },
    clearSelect() {
    },
    cancleDialog() {
      this.listQuery = {
        pageIndex: 1,
        pageSize: 10,
        order: '+Subject'
      }
      this.showDialog = false
      this.resultData = []
      this.dataList = []
    }
  }

}
</script>

<style scoped>
::v-deep .el-input__validateIcon {
  display: none;
}

.components-dialog ::v-deep th .el-checkbox {
  display: none !important;
}
</style>
