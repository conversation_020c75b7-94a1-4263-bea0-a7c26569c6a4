<template>
  <div>
    <el-upload
      v-if="!isView"
      class="upload-demo"
      :on-change="handleChange"
      v-bind="$attrs"
      action=""
      :file-list="fileList"
      :http-request="uploadFile"
      :auto-upload="true"
      :on-remove="handleRemove"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">上传文件大小不能超过10MB</div>
    </el-upload>
    <div v-if="isView" class="file-list">
      <div v-for="(item, index) in value" :key="index" class="list-item">
        <a :href="item.url" target="_blank">{{ item.name }}</a>
      </div>
    </div>
  </div>
</template>
<script>
import fileApi from '@/api/file'
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    showDelete: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: this.value && this.value.length > 0 ? this.value : []
    }
  },
  watch: {
    fileList: {
      handler(newValue, oldValue) {
        this.$emit('input', newValue)
      },
      deep: true
    },
    value: {
      handler(newValue, oldValue) {
        this.fileList = newValue
      },
      deep: true
    }
  },
  methods: {
    uploadFile(params) {
      var file = params.file
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$alert('上传文件大小不能超过 10MB!', '提示', { type: 'error' })
        return false
      }
      fileApi.uploadAttach(file).then(result => {
        if (result.succeed) {
          var data = result.data
          this.fileList.push(data)
          this.$emit('resetField')
        }
      })
    },
    handleChange(file, fileList) {
      this.$emit('resetField')
    },
    handleRemove(file, fileList) {
      var index = this.fileList.findIndex(f => { return f.uid === file.uid })
      this.fileList.splice(index, 1)
      this.$emit('resetField')
    }
  }
}
</script>
<style>
.file-list {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
}
.list-item {
  width: 100%;
  line-height: 200%;
}
</style>
