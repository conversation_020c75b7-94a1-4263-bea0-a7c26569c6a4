<template>

  <div class="upload-container">
    <div class="image-list">
      <div v-for="(image ,index) in value" :key="index" class="image-list-item">
        <el-image
          :src="image.url"
          :preview-src-list="previewList"
          class="gs-image"
        >
        </el-image>
        <div v-if="showDelete && !isView" class="btn-delete" @click="handleRemove(image)">删除</div>
      </div>
      <el-upload
        v-if="!isView"
        ref="upload"
        v-bind="$attrs"
        action=""
        list-type="picture-card"
        :http-request="uploadFile"
        :class="{'auto-upload':autoUpload,'limited':limit && fileList.length >= limit}"
        :auto-upload="autoUpload"
      >
        <i class="el-icon-plus" />
      </el-upload>
    </div>
  </div>
</template>

<script>
// import { mapState } from 'vuex'
import fileApi from '@/api/file'

export default {
  name: 'ImageUpload',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: undefined
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    showDelete: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      previewList: []
    }
  },
  watch: {
    fileList: {
      handler(newValue, oldValue) {
        this.$emit('input', newValue)
        this.previewList = newValue.map(item => { return item.url })
      },
      deep: true
    },
    value: {
      handler(newValue, oldValue) {
        this.previewList = newValue.map(item => { return item.url })
      },
      deep: true
    }

  },
  methods: {
    uploadFile(params) {
      var file = params.file
      var fileType = file.type
      var isImage = fileType.indexOf('image') !== -1

      if (!isImage) {
        this.$alert('请选择图片!', '提示', { type: 'error' })
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$alert('上传图片大小不能超过 10MB!', '提示', { type: 'error' })
        return false
      }
      fileApi.uploadImage(file).then(result => {
        if (result.succeed) {
          var data = result.data
          if (data) {
            this.fileList.push(data)
            // this.previewList.push(data.url)
          }
        }
      })
    },
    handleRemove(file) {
      var index = this.fileList.findIndex(item => file.fileIndexId === item.fileIndexId)
      this.fileList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
    @import "~@/styles/mixin.scss";
    .upload-container {
        position: relative;
        @include clearfix;
    }
    .image-list{
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap:wrap;
    }
.image-list-item{
  width: 148px;
  margin: 0px 15px 20px 0px;
  height: 148px;
}
.gs-image{
  width: 100%;
  height: 100%;
}
.btn-delete{
position: relative;
bottom: 42px;
font-size: 1rem;
background-color: rgba(0,0,0,.5);
text-align: center;
width: 100%;
height: 30px;
cursor: pointer;
color: #fff;
}
::v-deep .el-icon-circle-close{
  color: #fff !important;
}
</style>

