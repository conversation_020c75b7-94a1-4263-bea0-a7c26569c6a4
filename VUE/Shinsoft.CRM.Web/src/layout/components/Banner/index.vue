<template>
  <div class="banner-container">
    <navbar />
  </div>
</template>

<script>

import banner from '@/assets/banner/bannerTop.png'
import Navbar from '../Navbar.vue'

export default {
  components: { Navbar },
  data() {
    return {
      title: this.$cfg.title,
      banner: banner
    }
  }
}
</script>

<style lang="scss" scoped>
.banner-container {
  height: 70px;
  width: 100%;
  background: url("~@/assets/banner/bannerTop.png");
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
