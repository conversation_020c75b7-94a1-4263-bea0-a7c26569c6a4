import cfg from '@cfg'
import { string } from 'clipboard'

export function getToken() {
  const token = sessionStorage.getItem(cfg.tokenName)
  if (token) return token
  else return false
}

export function setToken(token) {
  sessionStorage.setItem(cfg.tokenName, token)
}

export function removeToken() {
  return sessionStorage.removeItem(cfg.tokenName)
}


export function getAutoLoginToken() {
  const autoLoginToken = localStorage.getItem(cfg.autoLoginTokenName)
  if (autoLoginToken) return autoLoginToken
  else return false
}

export function setAutoLoginToken(autoLoginToken) {
  localStorage.setItem(cfg.autoLoginTokenName, autoLoginToken)
}

export function removeAutoLoginToken() {
  return localStorage.removeItem(cfg.autoLoginTokenName)
}

export function isUserPermitted(user, permission) {
  if (permission) {
    if (user && user.permission) {
      if (typeof permission === 'string') {
        return Object.keys(user.permission).includes(permission)
      } else {
        return Object.keys(permission).every(p => (permission[p] instanceof Array && permission[p].length > 0 && // permission 定义为{ '权限'：['标签'] },参数p为'权限'
          Object.keys(user.permission).includes(p) && user.permission[p] instanceof Array && // 用户权限中也定义了p权限，且该权限标签为数组
          permission[p].every(tag => user.permission[p].includes(tag))) || // permission 定义的权限每一个标签，用户权限中都拥有
          (typeof permission[p] === 'string' &&
            Object.keys(user.permission).includes(p) && user.permission[p] instanceof Array && // 用户权限中也定义了p权限，且该权限标签为数组
            user.permission[p].includes(permission[p])) // 用户权限中拥有permission 定义的权限标签
        )
      }
    } else {
      return false
    }
  } else {
    return true
  }
}
