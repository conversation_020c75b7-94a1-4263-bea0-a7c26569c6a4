import { MessageBox, Message } from 'element-ui'

export function tip(result) {
  let type = 'warning'
  switch (result.type) {
    case -5:
    {
      type = 'error'
      break
    }
    case -4:
    {
      type = 'warning'
      break
    }
    case 2:
    {
      type = 'info'
      break
    }
  }

  MessageBox.alert(result.messages.toString().replace(/,/g, '<br/>'), '提示', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定',
    type: type
  })
}

export function message(msg, type) {
  Message({
    showClose: true,
    message: msg,
    type: type,
    duration: 1500
  })
}

const notice = { tip, message }

export default notice
