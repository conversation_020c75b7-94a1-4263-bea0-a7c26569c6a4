import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import http from '/api/index.js'
import config from '/config/config.js'
// import {getUserInfo} from '/utils/sysTool.js'
// import BdiduMap from 'vue-baidu-map'

export function createApp() {
  const app = createSSRApp(App)
  app.provide('config',config)
  app.config.globalProperties.$http=http
  // app.provide('userInfo',getUserInfo())
  // app.use(BdiduMap,{
	 //  ak:'D5vlFgdTyLVw5CX4osGG1lAiDKmXVI1T'
  // })
  return {
    app
  }
}
// #endif