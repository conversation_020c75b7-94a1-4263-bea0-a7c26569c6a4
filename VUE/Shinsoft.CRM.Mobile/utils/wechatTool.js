//引入企业微信jssdk
import * as ww from '@wecom/jssdk'
import {
	TransformCoord
} from '@/utils/transformCoord.js'
class weChat {
	constructor(config, jsApiList, pageUrl) {
		ww.register({
			corpId: config.corpId, // 必填，当前用户企业所属企业ID
			agentId: config.agentId,
			jsApiList: jsApiList, // 必填，需要使用的JSAPI列表
			getConfigSignature, // 必填，根据url生成企业签名的回调函数
			getAgentConfigSignature,
			onConfigSuccess() {},
			onConfigFail(res) {
				uni.showToast({
					title: '企业鉴权失败:' + JSON.stringify(res),
					icon: 'none'
				})
			},
			onAgentConfigSuccess() {

			},
			onAgentConfigFail(res) {
				uni.showToast({
					title: '应用鉴权失败:' + JSON.stringify(res),
					icon: 'none'
				})
			}
		})
		async function getConfigSignature(url) {
			let signature = ww.getSignature(config.ticket)
			return signature;
		}
		async function getAgentConfigSignature(url) {
			let signature = ww.getSignature(config.agentTicket)
			return signature;
		}
	}

	isWeiXinBrowser() {
		// #ifdef H5	
		// window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型	
		let ua = window.navigator.userAgent.toLowerCase() // 通过正则表达式匹配ua中是否含有MicroMessenger字符串	
		if (ua.match(/MicroMessenger/i) == 'micromessenger') {
			return true
		} else {
			return false
		}
		// #endif
		return false
	}

	getLocation() {
		var promise = new Promise((resolve, reject) => {
			let isWX = this.isWeiXinBrowser()
			if (isWX) {
				this.getLocationWX().then(res => {
					resolve(res)
				}).catch(() => {
					reject()
				})
			} else {
				this.getLocationH5().then(res => {
					resolve(res)
				})
			}
		})
		return promise
	}
	getLocationWX() {
		var promise = new Promise((resolve, reject) => {
			ww.getLocation({
				type: 'gcj02',
				success: function(res) {
					let location = {}
					location = {
						latitude: res.latitude,
						longitude: res.longitude
					}
					resolve(location)
				},
				fail: function(res) {
					uni.showToast({
						title: '定位失败:' + res.errMsg,
						icon: 'none',
						duration: 3000
					})
					reject()
				}
			})
		})
		return promise
	}
	getLocationH5() {
		var promise = new Promise((resolve, reject) => {
			let location = {}
			if (navigator.geolocation) {
				navigator.geolocation.getCurrentPosition((position) => {
					location = {
						latitude: position.coords.latitude,
						longitude: position.coords.longitude
					}
					var str_gcj02 = TransformCoord('wgs84', 'gcj02', location.latitude + ',' +
						location.longitude)
					if (str_gcj02.split(",").length = 2) {
						var location_gcj02 = {
							latitude: parseFloat(str_gcj02.split(",")[0]),
							longitude: parseFloat(str_gcj02.split(",")[1])
						}
						resolve(location_gcj02)
					} else {
						uni.showToast({
							title: '坐标转换失败',
							icon: 'none'
						})
					}
				});

			} else {
				uni.showToast({
					title: '该浏览器不支持获取地理位置',
					icon: 'none'
				})
			}
		})
		return promise
	}
}

export default weChat