// 常用日期工具集合
// 1、获取当前日期
// 2、获取当前日期时间
// 3、获取今天是星期几
// 4、获取时间戳
// 5、获取昨天
// 6、获取昨天是星期几
// 7、获取明天
// 8、获取5天前
// 9、获取10天后
// 10、获取最近七天的日期(默认倒序)
// 11、获取最近七天的日期(顺序)
// 12、计算两个日期相隔天数
// 13、计算两个时间差，返回天小时分钟
// 14、获得上周周一和周日的日期
// 15、获得本周周一和周日的日期
// 16、日期时间转换为几秒/几分/几小时/几天前

let config = {
	seperator: "-"
}

//1、获取当前日期
export const getToday = (seperator) => {
	return parseDateToString(new Date(), seperator)
}
//2、获取当前日期时间
const getTodayTime = (seperator) => {
	return parseDateTimeToString(new Date(), seperator)
}

//3、返回的周几 ：星期一
const getWeekDay = (dateStr) => {
	let date = new Date()
	if (dateStr) {
		date = new Date(dateStr)
	}
	const day = date.getDay()
	const days = ['日', '一', '二', '三', '四', '五', '六']
	return `星期${days[day]}`
}

//4、获取今天时间戳
const getCurrentTime = () => new Date().getTime()

//5、获取昨天
const getYesterday = () => {
	return getDate(-1)
}

//6、获取明天
const getTomorrow = () => {
	return getDate(1)
}

//7、获取几天前/几天后的日期
const getDate = (day, seperator) => {
	if (!seperator) seperator = config.seperator
	var today = new Date()
	var targetday = today.getTime() + 1000 * 60 * 60 * 24 * day
	today.setTime(targetday)
	var tYear = today.getFullYear()
	var tMonth = today.getMonth()
	var tDate = today.getDate()
	tMonth = addDate0(tMonth + 1)
	tDate = addDate0(tDate)
	return tYear + seperator + tMonth + seperator + tDate
}

//8、返回最近七天的日期
const getLast7Day = (orderBy) => {
	let days = [];
	for (let i = 0; i <= 24 * 6; i += 24) { //今天加上前6天
		let dateItem = new Date(new Date().getTime() - i * 60 * 60 * 1000); //使用当天时间戳减去以前的时间毫秒（小时*分*秒*毫秒）
		let y = dateItem.getFullYear(); //获取年份
		let m = dateItem.getMonth() + 1; //获取月份js月份从0开始，需要+1
		let d = dateItem.getDate(); //获取日期
		m = addDate0(m); //给为单数的月份补零
		d = addDate0(d); //给为单数的日期补零
		let valueItem = y + '-' + m + '-' + d; //组合
		days.push(valueItem); //添加至数组
	}
	//console.log('最近七天日期：',days);
	if (orderBy == 'asc') {
		return days.reverse()
	}
	return days;
}



//9、计算两个日期相隔天数
const dateDiff = (sDate1, sDate2) => {
	//sDate1=开始时间
	//sDate2=对比时间
	var aDate, oDate1, oDate2, iDays;
	aDate = sDate1.split("-");
	oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]); //转换为yyyy-MM-dd格式
	aDate = sDate2.split("-");
	oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
	iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数
	return iDays + 1; //返回相差天数
}

//10、计算两个时间差  返回天小时分钟
function timeDiff(begin_time, end_time) {
	//年月日时分秒转换为时间戳
	let beginTime = (new Date(begin_time).getTime()) / 1000;
	let endTime = (new Date(end_time).getTime()) / 1000;
	var starttime = ''
	var endtime = ''
	if (beginTime < endTime) {
		starttime = beginTime;
		endtime = endTime;
	} else {
		starttime = endTime;
		endtime = beginTime;
	}
	//计算天数
	var timediff = endtime - starttime;
	var days = parseInt(timediff / 86400);
	//计算小时数
	var remain = timediff % 86400;
	var hours = parseInt(remain / 3600);
	//计算分钟数
	var remain = remain % 3600;
	var mins = parseInt(remain / 60);
	//计算秒数
	var secs = remain % 60;
	//返回结果
	var res = days + '天' + hours + '小时' + mins + '分' + secs + '秒';
	return res;
}

//11、获得上周周一和周日的日期
function getLastWeekData() {
	let lastweek = {};
	let now = new Date(); //当前日期
	let nowDayOfWeek = now.getDay(); //今天本周的第几天
	let nowYear = now.getYear(); //当前年
	nowYear += (nowYear < 2000) ? 1900 : 0; //
	let nowDay = now.getDate(); //当前日
	let nowMonth = now.getMonth(); //当前月
	lastweek.start_date = parseDateToString(new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 6));
	lastweek.end_date = parseDateToString(new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 0));
	console.log('获得上周周一和周日的日期 ：', lastweek);
	return lastweek
}


//12、获得本周周一和周日的日期
function getThisWeekData() {
	let thisweek = {};
	let now = new Date(); //当前日期
	let nowDayOfWeek = now.getDay(); //今天本周的第几天
	let nowDay = now.getDate(); //当前日
	let nowYear = now.getYear(); //当前年
	nowYear += (nowYear < 2000) ? 1900 : 0; //
	let nowMonth = now.getMonth(); //当前月
	thisweek.start_date = parseDateToString(new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1));
	thisweek.end_date = parseDateToString(new Date(nowYear, nowMonth, nowDay + (7 - nowDayOfWeek)));
	console.log('获得上周周一和周日的日期 ：', thisweek);
	return thisweek
}

//13、日期时间转换为几秒/几分/几小时/几天前
function getFriendlyDate(timestamp) {
	var formats = {
		'year': '%n% 年前',
		'month': '%n% 月前',
		'day': '%n% 天前',
		'hour': '%n% 小时前',
		'minute': '%n% 分钟前',
		'second': '%n% 秒前',
	};

	var now = Date.now();
	var seconds = Math.floor((now - timestamp) / 1000);
	var minutes = Math.floor(seconds / 60);
	var hours = Math.floor(minutes / 60);
	var days = Math.floor(hours / 24);
	var months = Math.floor(days / 30);
	var years = Math.floor(months / 12);

	var diffType = '';
	var diffValue = 0;
	if (years > 0) {
		diffType = 'year';
		diffValue = years;
	} else {
		if (months > 0) {
			diffType = 'month';
			diffValue = months;
		} else {
			if (days > 0) {
				diffType = 'day';
				diffValue = days;
			} else {
				if (hours > 0) {
					diffType = 'hour';
					diffValue = hours;
				} else {
					if (minutes > 0) {
						diffType = 'minute';
						diffValue = minutes;
					} else {
						diffType = 'second';
						diffValue = seconds === 0 ? (seconds = 1) : seconds;
					}
				}
			}
		}
	}
	return formats[diffType].replace('%n%', diffValue);
}


//14、时间戳转换为时间
const parseTimestampToDate = () => {
	return new Date(time)
}
//15、时间字符串转换为时间
const parseStringToDate = (dateStr) => {
	return new Date(dateStr)
}
//16、时间转换为时间字符串
const parseDateTimeToString = (date, seperator) => {
	if (!seperator) seperator = config.seperator
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const hour = date.getHours()
	const minute = date.getMinutes()
	const second = date.getSeconds()

	return [year, month, day].map(formatNumber).join(seperator) + ' ' + [hour, minute, second].map(formatNumber)
		.join(':')
}

//17、日期转换为日期字符串
function parseDateToString(date, seperator) {
	if (!seperator) seperator = config.seperator
	let myyear = date.getFullYear();
	let mymonth = date.getMonth() + 1;
	let myweekday = date.getDate();
	if (mymonth < 10) {
		mymonth = "0" + mymonth;
	}
	if (myweekday < 10) {
		myweekday = "0" + myweekday;
	}
	return (myyear + seperator + mymonth + seperator + myweekday);
}

//18、获取本月第一天的日期
const getThisMonthFirstDay = () => {
	let now = new Date()
	let year = now.getFullYear()
	let month = now.getMonth() + 1
	if (month < 10) {
		month = "0" + month
	}
	return year + "-" + month + "-01"
}

//19、获取本月最后一天的日期
const getThisMonthLastDay = () => {
	// 获取当前日期  
	let currentDate = new Date();
	// 获取本月最后一天  
	let lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
	// 将日期转换为指定的格式（年-月-日）  
	let formattedDate = lastDayOfMonth.getFullYear() + '-' + (lastDayOfMonth.getMonth() + 1) + '-' + lastDayOfMonth
		.getDate();
	return formattedDate;
}

//20、判断两个日期是否相差x个月
const validateDiffMonths = (d1, d2, num) => {
	// 将月份字符串转换为日期对象
	let startDate = new Date(d1);
	let endDate = new Date(d2);
	// 计算开始月份和结束月份之间的月份差  
	let monthDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate
		.getMonth());
	if (!num) num = 12
	// 判断月份差是否超过12个月  
	if (monthDiff > num) {
		console.log('选择的日期范围最多支持12个月')
		return true
	}
	return false
}

//21、判断两个日期是否相差x天
const validateDiffDays = (d1, d2, num) => {
	let dateObj1 = new Date(d1);
	let dateObj2 = new Date(d2);
	// 计算开始日期和结束日期之间的日期差  
	var diffDays = Math.abs(dateObj1 - dateObj2) / (1000 * 60 * 60 * 24);
	console.log(dateObj1, dateObj2, diffDays)
	if (!num) num = 31
	// 判断是否超过31天  
	if (diffDays > num) {
		console.log('选择的日期范围最多支持31天')
		return true
	}
	return false
}
//22. 获取一周的日期
const getCurrentWeek = (dateStr, seperator) => {
	if (!seperator) seperator = config.seperator
	let date = new Date()
	if (dateStr) {
		date = new Date(dateStr)
	}
	let weekDates = []
	// 获取当前星期几（0-6，0表示星期日）
	let currentDayOfWeek = date.getDay();

	// 获取一周的日期只要日和星期几
	for (let i = 0; i < 7; i++) {
		let day = new Date(date);
		day.setDate(date.getDate() + i - currentDayOfWeek);
		let dayOfWeek = day.getDay();
		let y = day.getFullYear()
		let m = day.getMonth() + 1
		let d = day.getDate()
		let das = [y, m, d].map(formatNumber).join(seperator)
		let month = [y, m].map(formatNumber).join(seperator)
		let formattedDate = {
			day: day.getDate(),
			date: das,
			dayOfWeek: dayOfWeek,
			month: month
		};
		weekDates.push(formattedDate);
	}
	return weekDates
}


const formatNumber = n => {
	n = n.toString()
	return n[1] ? n : '0' + n
}

//给日期加0
function addDate0(time) {
	if (time.toString().length == 1) {
		time = '0' + time.toString();
	}
	return time;
}

//获取本月
const getThisMonth = () => {
	let now = new Date()
	let year = now.getFullYear()
	let month = now.getMonth() + 1
	if (month < 10) {
		month = "0" + month
	}
	return year + "-" + month
}
/**
 *获取几个月前的输入日期
 *{param:DateTime} date 输入日期(YYYY-MM-DD)
 *{param:number } monthNum 月数
 */
function getPreMonthDay(date, monthNum) {
	var dateArr = date.split('-');
	var year = dateArr[0]; //获取当前日期的年份
	var month = dateArr[1]; //获取当前日期的月份
	var day = dateArr[2]; //获取当前日期的日
	var days = new Date(year, month, 0);
	days = days.getDate(); //获取当前日期中月的天数
	var year2 = year;
	var month2 = parseInt(month) - monthNum;
	if (month2 <= 0) {
		var absM = Math.abs(month2);
		year2 = parseInt(year2) - Math.ceil(absM / 12 == 0 ? 1 : parseInt(absM) / 12);
		month2 = 12 - (absM % 12);
	}
	var day2 = day;
	var days2 = new Date(year2, month2, 0);
	days2 = days2.getDate();
	if (day2 > days2) {
		day2 = days2;
	}
	if (month2 < 10) {
		month2 = '0' + month2;
	}
	var t2 = year2 + '-' + month2 + '-' + day2;
	return t2;
}
export default {
	getToday,
	getTodayTime,
	getWeekDay,
	getCurrentTime,
	getYesterday,
	getTomorrow,
	getDate,
	getLast7Day,
	getThisMonth,

	dateDiff,
	timeDiff,
	getLastWeekData,
	getThisWeekData,
	getFriendlyDate,

	parseTimestampToDate,
	parseStringToDate,
	parseDateTimeToString,
	parseDateToString,

	getThisMonthFirstDay,
	getThisMonthLastDay,

	validateDiffMonths,
	validateDiffDays,
	getCurrentWeek,
	getPreMonthDay
}