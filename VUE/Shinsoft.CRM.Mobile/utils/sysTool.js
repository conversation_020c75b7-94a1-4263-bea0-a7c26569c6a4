import {
	pages
} from "@/pages.json"
import {
	getCurrentInstance
} from 'vue'

const tokenKey = 'token'
const aotuLoginTokenKey = 'alToken'
const permissionKey = 'permission'
const userInfoKey = 'userInfo'
const virtualKey = 'virtual'

//获取缓存用户信息
export function getUserInfo() {
	if (uni.getStorageSync(userInfoKey))
		return JSON.parse(uni.getStorageSync(userInfoKey))
	else
		return false
}
//保存用户信息
export function setUserInfo(userInfoValue) {
	return uni.setStorageSync(userInfoKey, userInfoValue)
}
//删除用户信息
export function reomveUserInfo() {
	return uni.removeStorageSync(userInfoKey)
}
//获取token
export function getToken() {
	return uni.getStorageSync(tokenKey)
}
//设置token
export function setToken(tokenValue) {
	return uni.setStorageSync(tokenKey, tokenValue)
}
//删除token
export function reomveToken() {
	return uni.removeStorageSync(tokenKey)
}
//获取权限
export function getPermission() {
	let userinfo = getUserInfo()
	if (userinfo)
		return Object.keys(userinfo.permission)
	// return uni.getStorageSync(permissionKey)
}
//获取token
export function getAutoToken() {
	let autoToken = uni.getStorageSync(aotuLoginTokenKey)
	return uni.getStorageSync(aotuLoginTokenKey)
}
//设置token
export function setAutoToken(tokenValue) {
	return uni.setStorageSync(aotuLoginTokenKey, tokenValue)
}

export function reomveAutoToken() {
	return uni.removeStorageSync(aotuLoginTokenKey)
}
//获取token
export function getVirtual() {
	// var promise = new Promise((resolve, reject) => {
	// 	let virtua =uni.getStorageSync(virtualKey)
	// 	resolve(virtua)
	// })
	return uni.getStorageSync(virtualKey)
}
//设置token
export function setVirtual(tokenValue) {
	return uni.setStorageSync(virtualKey, tokenValue)
}



// export function setPermission(permissionValue) {
// 	return uni.setStorageSync(permission, permissionValue)
// }
//验证权限
export function checkPermission(url) {
	let result = false;
	let permissions = getPermission()
	// let pages = pages;
	let pliceUrl = url.slice(1)
	let page = pages.filter((filter) => {
		return filter.path == pliceUrl
	})[0]
	if (permissions && permissions.some((per) => {
			return per == page.pageCode
		})) {
		result = true
	}
	return result;
}


export function goHome() {
	uni.reLaunch({
		url: '/pages/tabBar/allServices/allServices'
	})
}
export function goBack(delta = 1) {
	let hisPages = getCurrentPages()
	if (hisPages && hisPages.length > 1) {
		uni.navigateBack({
			delta: delta,
			success() {

			}
		})
	} else {
		history.go(-delta)
	}
}



(function(exports) {
	exports.format = function() {
		var args = Array.prototype.slice.call(arguments),
			sourceStr = args.shift();

		function execReplace(text, replacement, index) {
			return text.replace(new RegExp("\\{" + index + "\\}", 'g'), replacement);
		}

		return args.reduce(execReplace, sourceStr);
	}
})(window.utils = window.utils || {});

export function goBackRefresh() {
	let pages = getCurrentPages();
	uni.navigateBack({
		success: () => {
			// pages[pages.length-2].search()
			// console.log(pages[pages.length - 2])
		}
	})
}

export function isUserPermitted(permission) {
	if (permission) {
		let user = getUserInfo()
		if (user && user.permission) {
			if (typeof permission === 'string') {
				return Object.keys(user.permission).includes(permission)
			} else {
				return Object.keys(permission).every(p => (permission[p] instanceof Array && permission[p].length >
					0 &&
					permission[p].every(tag => user.permission[p].includes(tag))) || Object.keys(user
					.permission).includes(p))
			}
		} else {
			return false
		}
	} else {
		return true
	}
}

export function isWeiXinBrowser() {
	// #ifdef H5	
	// window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型	
	let ua = window.navigator.userAgent.toLowerCase() // 通过正则表达式匹配ua中是否含有MicroMessenger字符串	
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		return true
	} else {
		return false
	}
	// #endif
	return false
}

// import * as ww from '@wecom/jssdk'
// ww.register({
// 	corpId: 'ww944997698b10eb0d', // 必填，当前用户企业所属企业ID
// 	jsApiList: ['getLocation'], // 必填，需要使用的JSAPI列表
// 	getConfigSignature // 必填，根据url生成企业签名的回调函数
// })
// function getConfigSignature (){

// }
export function getLocation() {
	var promise = new Promise((resolve, reject) => {
		let location = {}
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition((position)=> {
				location = {
					latitude: position.coords.latitude,
					longitude: position.coords.longitude
				}
				resolve(location)
			});

		} else {
			uni.showToast({
				title: '该浏览器不支持获取地理位置',
				icon: 'none'
			})
		}
	})
	return promise
	// var promise = new Promise((resolve, reject) => {
	// 	// let p = ""
	// 	uni.getLocation({
	// 		type: "gcj02",
	// 		isHighAccuracy: true,
	// 		success: (res) => {
	// 			resolve(res)
	// 		},
	// 		fail(res) {
	// 			uni.showToast({
	// 				title: res.errMsg,
	// 				icon: 'none'
	// 			})
	// 		}
	// 	})
	// wx.getLocation({
	// 	type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
	// 	isHighAccuracy:true,
	// 	success: function(res) {
	// 		resolve(res)
	// 	},
	// 	fail: (res) => {
	// 		uni.showToast({
	// 			title: JSON.stringify(res),
	// 			icon: 'none',
	// 			duration:5000
	// 		})
	// 	}
	// });
	// })
	// return promise
}
const navigationQQUrl =
	'https://apis.map.qq.com/uri/v1/routeplan?type=drive&fromcoord={0}&from={1}&tocoord={2}&to={3}&referer=shinsoft'
const navigationBaiDuUrl =
	'https://api.map.baidu.com/direction?mode=driving&origin={0}&origin_region=Departure&destination={2}&destination_region={3}&output=html&src=shinsoft'
const navigationGaoDeUrl = 'https://uri.amap.com/navigation?from={0}&to={2}&mode=car&src=shinsoft'
export function getMapNavigation(mapType, toLocation, toName, fromLocation = '', fromName = '当前位置') {
	let navigationUrl = ""
	if (mapType == 1)
		navigationUrl = navigationBaiDuUrl;
	else if (mapType == 2)
		navigationUrl = navigationQQUrl
	else if (mapType == 3) {
		navigationUrl = navigationGaoDeUrl
		let arrto = toLocation.split(',')
		toLocation = arrto[1] + "," + arrto[0]
	} else {
		uni.showToast({
			title: '请传入正确的地图类型',
			icon: 'none'
		})
		return false
	}
	let url = ""
	if (fromLocation == "") {
		let res = getLocation()
		//高德地图经纬度相反
		fromLocation = mapType == 3 ? res.longitude + "," + res.latitude : res.latitude + "," + res
			.longitude

		url = utils.format(navigationUrl, fromLocation, fromName, toLocation, toName)
		// window.location.href = url;
	} else {

		url = utils.format(navigationUrl, fromLocation, fromName, toLocation, toName)
		// window.location.href = url;
	}
	return url
}


export function isLoadingExist() {
	// 获取当前页面实例对象
	const instance = getCurrentInstance()

	if (instance && instance.proxy.$loading) {
		return true;
	} else {
		return false;
	}
}