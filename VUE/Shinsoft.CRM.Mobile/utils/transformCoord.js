﻿

//转换坐标系（纬度,经度）
export function TransformCoord(FromType, ToType, FromCoord) {
    try {
        var lat = parseFloat(FromCoord.split(",")[0]);
        var lng = parseFloat(FromCoord.split(",")[1]);
        var ToCoord;

        if (FromType == "wgs84" && ToType == "gcj02") {
            ToCoord = wgs84ToGcj02(lng, lat);
        }
        else if (FromType == "gcj02" && ToType == "wgs84") {
            ToCoord = gcj02ToWgs84(lng, lat);
        }
        else if (FromType == "bd09" && ToType == "gcj02") {
            ToCoord = bd09ToGcj02(lng, lat);
        }
        else if (FromType == "gcj02" && ToType == "bd09") {
            ToCoord = gcj02ToBd09(lng, lat);
        }
        else if (FromType == "wgs84" && ToType == "bd09") {
            ToCoord = wgs84ToGcj02(lng, lat);
            ToCoord = gcj02ToBd09(ToCoord[0], ToCoord[1]);
        }
        else if (FromType == "bd09" && ToType == "wgs84") {
            ToCoord = bd09ToGcj02(lng, lat);
            ToCoord = gcj02ToWgs84(ToCoord[0], ToCoord[1]);
        }
        else {
            return FromCoord;
        }

        return ToCoord[1] + "," + ToCoord[0];
    }
    catch (error) {
        return "fail";
    }
}

const PI = 3.14159265358979324;
const x_PI = PI * 3000.0 / 180.0;
const a = 6378245.0;
const ee = 0.00669342162296594323;

//WGS84转GCJ02
function wgs84ToGcj02(lng, lat) {
    if (out_of_china(lng, lat)) {
        return [lng, lat]
    } else {
        var dlat = transformlat(lng - 105.0, lat - 35.0)
        var dlng = transformlng(lng - 105.0, lat - 35.0)
        var radlat = lat / 180.0 * PI
        var magic = Math.sin(radlat)
        magic = 1 - ee * magic * magic
        var sqrtmagic = Math.sqrt(magic)
        dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
        dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
        var mglat = lat + dlat
        var mglng = lng + dlng
        return [mglng, mglat]
    }
}

//GCJ02转WGS84
function gcj02ToWgs84(lng, lat) {
    if (out_of_china(lng, lat)) {
        return [lng, lat]
    } else {
        var dlat = transformlat(lng - 105.0, lat - 35.0)
        var dlng = transformlng(lng - 105.0, lat - 35.0)
        var radlat = lat / 180.0 * PI
        var magic = Math.sin(radlat)
        magic = 1 - ee * magic * magic
        var sqrtmagic = Math.sqrt(magic)
        dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
        dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
        var mglat = lat + dlat
        var mglng = lng + dlng
        return [lng * 2 - mglng, lat * 2 - mglat]
    }
}

//BD09转GCJ02
function bd09ToGcj02(lng, lat) {
    var x = lng - 0.0065
    var y = lat - 0.006
    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI)
    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI)
    var gg_lng = z * Math.cos(theta)
    var gg_lat = z * Math.sin(theta)
    return [gg_lng, gg_lat]
}

//GCJ02转换BD09
function gcj02ToBd09(lng, lat) {
    var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI)
    var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI)
    var bd_lng = z * Math.cos(theta) + 0.0065
    var bd_lat = z * Math.sin(theta) + 0.006
    return [bd_lng, bd_lat]
}

//转换方法中使用到的其他方法
function transformlat(lng, lat) {
    var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
    ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
    return ret
}

function transformlng(lng, lat) {
    var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
    ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
    return ret
}

// 判断是否在国内，不在国内则不做偏移
function out_of_china(lng, lat) {
    return (lng < 72.004 || lng > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false)
}
