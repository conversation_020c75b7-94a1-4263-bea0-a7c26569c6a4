import http from '../request.js'

export default {
	/**
	 * 上传音频文件进行语音识别
	 * @param {FormData} formData 包含音频文件的表单数据
	 * @returns {Promise} 识别结果
	 */
	uploadAudioFile(formData) {
		return http.post('/Speech/UploadAudioFile', formData)
	},

	/**
	 * 获取语音识别历史记录
	 * @param {Object} params 查询参数
	 * @param {number} params.pageIndex 页码
	 * @param {number} params.pageSize 页大小
	 * @returns {Promise} 历史记录列表
	 */
	getSpeechHistory(params) {
		return http.get('/Speech/GetSpeechHistory', params)
	},

	/**
	 * 建立WebSocket连接进行实时语音识别
	 * @returns {WebSocket} WebSocket连接对象
	 */
	connectWebSocket() {
		// 获取WebSocket URL
		const baseUrl = getApp().globalData.config.baseUrl || 'ws://localhost:5000'
		const wsUrl = baseUrl.replace('http', 'ws') + '/Speech/ConnectWebSocket'
		
		return new WebSocket(wsUrl)
	},

	/**
	 * 创建实时语音识别连接
	 * @param {Function} onMessage 接收消息回调
	 * @param {Function} onError 错误回调
	 * @param {Function} onClose 连接关闭回调
	 * @returns {Object} 包含WebSocket连接和控制方法的对象
	 */
	createRealtimeRecognition(onMessage, onError, onClose) {
		let websocket = null
		let isConnected = false

		const connect = () => {
			try {
				websocket = this.connectWebSocket()
				
				websocket.onopen = () => {
					isConnected = true
					console.log('WebSocket连接已建立')
				}

				websocket.onmessage = (event) => {
					try {
						const data = JSON.parse(event.data)
						if (onMessage) {
							onMessage(data)
						}
					} catch (error) {
						console.error('解析WebSocket消息失败:', error)
					}
				}

				websocket.onerror = (error) => {
					console.error('WebSocket错误:', error)
					if (onError) {
						onError(error)
					}
				}

				websocket.onclose = () => {
					isConnected = false
					console.log('WebSocket连接已关闭')
					if (onClose) {
						onClose()
					}
				}
			} catch (error) {
				console.error('创建WebSocket连接失败:', error)
				if (onError) {
					onError(error)
				}
			}
		}

		const sendAudioData = (audioData) => {
			if (websocket && isConnected && websocket.readyState === WebSocket.OPEN) {
				websocket.send(audioData)
			} else {
				console.warn('WebSocket未连接，无法发送音频数据')
			}
		}

		const disconnect = () => {
			if (websocket) {
				websocket.close()
				websocket = null
				isConnected = false
			}
		}

		const getConnectionStatus = () => {
			return isConnected
		}

		return {
			connect,
			sendAudioData,
			disconnect,
			getConnectionStatus
		}
	}
}
