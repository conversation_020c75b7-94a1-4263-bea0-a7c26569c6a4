<template>
	<view class="voice-assistant">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="navbar-title" @longpress="toggleDebugMode">语音助手</view>
				<view class="navbar-right" @click="showFilter">
					<uni-icons type="tune" size="20" color="#333"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 快捷操作区域 -->
		<view class="quick-actions">
			<view class="action-title">快捷操作</view>
			<view class="action-list">
				<view class="action-item" v-for="(item, index) in quickActions" :key="index" @click="selectQuickAction(item)">
					<view class="action-icon">
						<uni-icons :type="item.icon" size="16" color="#007AFF"></uni-icons>
					</view>
					<text class="action-text">{{ item.text }}</text>
				</view>
				<!-- 测试API连接按钮 -->
				<view class="action-item test-api-btn" @click="testApiConnection">
					<view class="action-icon">
						<uni-icons type="gear" size="16" color="#FF6B35"></uni-icons>
					</view>
					<text class="action-text">测试API</text>
				</view>
			</view>
		</view>

		<!-- 录音区域 -->
		<view class="recording-area">
			<view class="recording-container">
				<!-- 录音按钮 -->
				<view class="record-button-container">
					<view
						class="record-button"
						:class="{ 'recording': isRecording, 'disabled': !deviceSupported }"
						@touchstart="deviceSupported ? startRecording() : showDeviceError()"
						@touchend="deviceSupported ? stopRecording() : null"
						@touchcancel="deviceSupported ? stopRecording() : null"
					>
						<view class="record-icon">
							<uni-icons type="mic" size="40" :color="isRecording ? '#fff' : '#007AFF'"></uni-icons>
						</view>
						<view class="record-waves" v-if="isRecording">
							<view class="wave" v-for="n in 3" :key="n"></view>
						</view>
					</view>
				</view>

				<!-- 录音状态 -->
				<view class="record-status">
					<text v-if="!deviceSupported">设备不支持录音</text>
					<text v-else-if="!isRecording">按住录音</text>
					<text v-else>录音中... {{ recordingTime }}s</text>
				</view>

				<!-- 录音时长显示 -->
				<view class="record-timer" v-if="isRecording">
					<text>{{ formatTime(recordingTime) }}</text>
				</view>
			</view>
		</view>

		<!-- 识别结果区域 -->
		<view class="result-area" v-if="recognitionResults.length > 0">
			<view class="result-title">识别结果</view>
			<view class="result-list">
				<view class="result-item" v-for="(result, index) in recognitionResults" :key="index">
					<view class="result-header">
						<text class="result-time">{{ formatDateTime(result.createTime) }}</text>
						<text class="result-confidence">置信度: {{ (result.confidence * 100).toFixed(1) }}%</text>
					</view>
					<view class="result-content">
						<text>{{ result.text }}</text>
					</view>
					<view class="result-actions">
						<view class="action-btn" @click="copyText(result.text)">
							<uni-icons type="copy" size="14" color="#666"></uni-icons>
							<text>复制</text>
						</view>
						<view class="action-btn" @click="shareText(result.text)">
							<uni-icons type="share" size="14" color="#666"></uni-icons>
							<text>分享</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 筛选抽屉 -->
		<uni-drawer ref="filterDrawer" mode="right" :width="300">
			<view class="filter-drawer">
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<view class="filter-close" @click="closeFilter">
						<uni-icons type="close" size="20" color="#333"></uni-icons>
					</view>
				</view>
				
				<view class="filter-content">
					<!-- 时间筛选 -->
					<view class="filter-section">
						<text class="filter-label">时间</text>
						<view class="filter-item">
							<uni-datetime-picker 
								v-model="filterData.startTime" 
								type="datetime" 
								placeholder="开始时间"
								@change="onStartTimeChange"
							></uni-datetime-picker>
						</view>
						<view class="filter-item">
							<uni-datetime-picker 
								v-model="filterData.endTime" 
								type="datetime" 
								placeholder="结束时间"
								@change="onEndTimeChange"
							></uni-datetime-picker>
						</view>
					</view>

					<!-- 部门筛选 -->
					<view class="filter-section">
						<text class="filter-label">部门</text>
						<view class="filter-item">
							<input 
								class="filter-input" 
								v-model="filterData.department" 
								placeholder="请输入部门名称"
							/>
						</view>
					</view>

					<!-- 执行人筛选 -->
					<view class="filter-section">
						<text class="filter-label">执行人</text>
						<view class="filter-item">
							<input 
								class="filter-input" 
								v-model="filterData.executor" 
								placeholder="请输入执行人姓名"
							/>
						</view>
					</view>
				</view>

				<view class="filter-footer">
					<view class="filter-btn reset-btn" @click="resetFilter">重置</view>
					<view class="filter-btn confirm-btn" @click="applyFilter">确定</view>
				</view>
			</view>
		</uni-drawer>

		<!-- 调试信息 -->
		<view class="debug-info" v-if="debugMode">
			<view class="debug-title">调试信息</view>
			<view class="debug-item">设备支持: {{ deviceSupported ? '是' : '否' }}</view>
			<view class="debug-item">录音状态: {{ isRecording ? '录音中' : '未录音' }}</view>
			<view class="debug-item">临时文件: {{ tempFilePath || '无' }}</view>
			<view class="debug-item">平台: {{ platformInfo }}</view>
		</view>

		<!-- 加载提示 -->
		<uni-load-more v-if="isProcessing" status="loading" :content-text="loadingText"></uni-load-more>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { getUserInfo } from "@/utils/sysTool.js"

const { proxy, appContext } = getCurrentInstance()
const $http = appContext.config.globalProperties.$http

// 响应式数据
const isRecording = ref(false)
const recordingTime = ref(0)
const isProcessing = ref(false)
const loadingText = ref('正在识别...')
const recognitionResults = ref([])
const deviceSupported = ref(true)
const debugMode = ref(false) // 可以通过点击某个按钮开启调试模式
const platformInfo = ref('')

// 录音相关
let recordingTimer = null
let recorderManager = null
let tempFilePath = ''

// 筛选数据
const filterData = reactive({
	startTime: '',
	endTime: '',
	department: '',
	executor: ''
})

// 快捷操作数据
const quickActions = ref([
	{ icon: 'calendar', text: '创建日程', action: 'createSchedule' },
	{ icon: 'contact', text: '添加联系人', action: 'addContact' },
	{ icon: 'chatboxes', text: '发送消息', action: 'sendMessage' },
	{ icon: 'location', text: '记录位置', action: 'recordLocation' },
	{ icon: 'compose', text: '创建备忘', action: 'createMemo' },
	{ icon: 'phone', text: '拨打电话', action: 'makeCall' }
])

// 用户信息
const userInfo = ref({})

onMounted(() => {
	userInfo.value = getUserInfo()
	loadHistoryRecords()
	checkDeviceSupport()
	initRecorder()
})

onUnmounted(() => {
	if (recordingTimer) {
		clearInterval(recordingTimer)
	}
	if (recorderManager) {
		recorderManager.stop()
	}
})

// 检查录音权限
async function checkRecordPermission() {
	return new Promise((resolve) => {
		uni.getSetting({
			success: (res) => {
				if (res.authSetting['scope.record'] === false) {
					// 用户拒绝了录音权限，引导用户去设置页面
					uni.showModal({
						title: '需要录音权限',
						content: '请在设置中开启录音权限',
						confirmText: '去设置',
						success: (modalRes) => {
							if (modalRes.confirm) {
								uni.openSetting({
									success: (settingRes) => {
										if (settingRes.authSetting['scope.record']) {
											resolve(true)
										} else {
											resolve(false)
										}
									},
									fail: () => resolve(false)
								})
							} else {
								resolve(false)
							}
						}
					})
				} else if (res.authSetting['scope.record'] === undefined) {
					// 用户还没有授权，请求授权
					uni.authorize({
						scope: 'scope.record',
						success: () => resolve(true),
						fail: () => resolve(false)
					})
				} else {
					// 用户已经授权
					resolve(true)
				}
			},
			fail: () => resolve(false)
		})
	})
}

// 初始化录音器
function initRecorder() {
	try {
		recorderManager = uni.getRecorderManager()

		recorderManager.onStart(() => {
			console.log('录音开始')
		})

		recorderManager.onStop((res) => {
			console.log('录音结束', res)
			tempFilePath = res.tempFilePath
			processRecording()
		})

		recorderManager.onError((err) => {
			console.error('录音错误', err)
			isRecording.value = false
			if (recordingTimer) {
				clearInterval(recordingTimer)
				recordingTimer = null
			}

			let errorMessage = '录音失败'
			if (err.errMsg) {
				if (err.errMsg.includes('permission')) {
					errorMessage = '请授权录音权限'
				} else if (err.errMsg.includes('device')) {
					errorMessage = '录音设备不可用'
				} else if (err.errMsg.includes('not found')) {
					errorMessage = '未找到录音设备'
				} else {
					errorMessage = `录音失败: ${err.errMsg}`
				}
			}

			uni.showToast({
				title: errorMessage,
				icon: 'none',
				duration: 3000
			})
		})
	} catch (error) {
		console.error('初始化录音器失败:', error)
		uni.showToast({
			title: '录音功能不可用',
			icon: 'none'
		})
	}
}

// 检查设备支持
function checkDeviceSupport() {
	try {
		// 检查是否支持录音管理器
		if (typeof uni.getRecorderManager !== 'function') {
			deviceSupported.value = false
			uni.showModal({
				title: '设备不支持',
				content: '当前设备不支持录音功能',
				showCancel: false
			})
			return false
		}

		// 检查平台信息
		const systemInfo = uni.getSystemInfoSync()
		platformInfo.value = `${systemInfo.platform} ${systemInfo.system} ${systemInfo.version}`
		console.log('设备信息:', systemInfo)

		// 在某些平台上可能需要特殊处理
		if (systemInfo.platform === 'devtools') {
			debugMode.value = true // 在开发者工具中自动开启调试模式
			uni.showToast({
				title: '开发者工具中录音功能可能受限',
				icon: 'none',
				duration: 3000
			})
		}

		deviceSupported.value = true
		return true
	} catch (error) {
		console.error('设备检查失败:', error)
		deviceSupported.value = false
		uni.showToast({
			title: '设备检查失败',
			icon: 'none'
		})
		return false
	}
}

// 显示设备错误
function showDeviceError() {
	uni.showModal({
		title: '设备不支持',
		content: '当前设备不支持录音功能，请在支持录音的设备上使用此功能',
		showCancel: false
	})
}

// 切换调试模式
function toggleDebugMode() {
	debugMode.value = !debugMode.value
	uni.showToast({
		title: debugMode.value ? '调试模式已开启' : '调试模式已关闭',
		icon: 'none'
	})
}

// 返回上一页
function goBack() {
	uni.navigateBack()
}

// 显示筛选抽屉
function showFilter() {
	proxy.$refs.filterDrawer.open()
}

// 关闭筛选抽屉
function closeFilter() {
	proxy.$refs.filterDrawer.close()
}

// 开始录音
async function startRecording() {
	try {
		if (!recorderManager) {
			uni.showToast({
				title: '录音器未初始化',
				icon: 'none'
			})
			return
		}

		// 检查录音权限
		const hasPermission = await checkRecordPermission()
		if (!hasPermission) {
			uni.showToast({
				title: '需要录音权限才能使用此功能',
				icon: 'none',
				duration: 3000
			})
			return
		}

		isRecording.value = true
		recordingTime.value = 0
		tempFilePath = ''

		// 开始录音
		recorderManager.start({
			duration: 60000, // 最长录音时间60秒
			sampleRate: 16000, // 采样率
			numberOfChannels: 1, // 录音通道数
			encodeBitRate: 96000, // 编码码率
			format: 'wav', // 音频格式
			frameSize: 50 // 指定帧大小，单位 KB
		})

		// 开始计时
		recordingTimer = setInterval(() => {
			recordingTime.value++
			// 超过60秒自动停止
			if (recordingTime.value >= 60) {
				stopRecording()
			}
		}, 1000)

	} catch (error) {
		console.error('录音启动失败:', error)
		uni.showToast({
			title: '录音启动失败',
			icon: 'none'
		})
		isRecording.value = false
	}
}

// 停止录音
function stopRecording() {
	if (!isRecording.value) return

	isRecording.value = false

	if (recordingTimer) {
		clearInterval(recordingTimer)
		recordingTimer = null
	}

	if (recorderManager) {
		recorderManager.stop()
	}
}

// 处理录音数据
async function processRecording() {
	if (!tempFilePath) {
		console.warn('没有录音文件')
		return
	}

	isProcessing.value = true
	loadingText.value = '正在识别语音...'

	try {
		// 上传录音文件
		const uploadResult = await uni.uploadFile({
			url: getApp().globalData.config.baseUrl + '/Speech/UploadAudioFile',
			filePath: tempFilePath,
			name: 'audioFile',
			header: {
				'Authorization': 'Bearer ' + uni.getStorageSync('token')
			}
		})

		if (uploadResult.statusCode === 200) {
			const response = JSON.parse(uploadResult.data)

			if (response && response.success && response.data) {
				const result = {
					...response.data,
					createTime: new Date()
				}
				recognitionResults.value.unshift(result)

				uni.showToast({
					title: '识别成功',
					icon: 'success'
				})
			} else {
				throw new Error(response?.message || '识别失败')
			}
		} else {
			throw new Error(`上传失败: ${uploadResult.statusCode}`)
		}
	} catch (error) {
		console.error('语音识别失败:', error)
		uni.showToast({
			title: error.message || '识别失败，请重试',
			icon: 'none',
			duration: 3000
		})
	} finally {
		isProcessing.value = false
		// 清理临时文件
		if (tempFilePath) {
			try {
				uni.removeSavedFile({
					filePath: tempFilePath
				})
			} catch (e) {
				console.warn('清理临时文件失败:', e)
			}
		}
	}
}

// 选择快捷操作
function selectQuickAction(item) {
	uni.showToast({
		title: `执行${item.text}`,
		icon: 'none'
	})
	// 这里可以根据不同的action执行不同的操作
}

// 测试API连接
async function testApiConnection() {
	uni.showLoading({
		title: '测试连接中...'
	})

	try {
		const response = await $http.speech.testAliBaiLianConnection()

		uni.hideLoading()

		if (response && response.success) {
			uni.showModal({
				title: '测试成功',
				content: `API连接正常\n状态: ${response.data.Status}\n时间: ${response.data.Timestamp}`,
				showCancel: false
			})
		} else {
			uni.showModal({
				title: '测试失败',
				content: response?.message || '未知错误',
				showCancel: false
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('API测试失败:', error)
		uni.showModal({
			title: '测试失败',
			content: `连接错误: ${error.message || '网络异常'}`,
			showCancel: false
		})
	}
}

// 复制文本
function copyText(text) {
	uni.setClipboardData({
		data: text,
		success: () => {
			uni.showToast({
				title: '已复制到剪贴板',
				icon: 'success'
			})
		}
	})
}

// 分享文本
function shareText(text) {
	uni.share({
		provider: 'weixin',
		type: 1,
		summary: text,
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		}
	})
}

// 格式化时间
function formatTime(seconds) {
	const mins = Math.floor(seconds / 60)
	const secs = seconds % 60
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化日期时间
function formatDateTime(date) {
	return new Date(date).toLocaleString()
}

// 加载历史记录
async function loadHistoryRecords() {
	try {
		const response = await $http.speech.getSpeechHistory({
			pageIndex: 1,
			pageSize: 20
		})
		
		if (response && response.data && response.data.items) {
			recognitionResults.value = response.data.items
		}
	} catch (error) {
		console.error('加载历史记录失败:', error)
	}
}

// 筛选相关方法
function onStartTimeChange(value) {
	filterData.startTime = value
}

function onEndTimeChange(value) {
	filterData.endTime = value
}

function resetFilter() {
	filterData.startTime = ''
	filterData.endTime = ''
	filterData.department = ''
	filterData.executor = ''
}

function applyFilter() {
	// 应用筛选条件
	console.log('应用筛选条件:', filterData)
	closeFilter()
	// 这里可以根据筛选条件重新加载数据
}
</script>

<style scoped>
.voice-assistant {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 15px;
	padding-top: var(--status-bar-height);
}

.navbar-left, .navbar-right {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

/* 快捷操作区域 */
.quick-actions {
	margin-top: calc(44px + var(--status-bar-height) + 20px);
	background-color: #fff;
	margin: calc(44px + var(--status-bar-height) + 20px) 15px 20px;
	border-radius: 12px;
	padding: 20px;
}

.action-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
}

.action-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.action-item {
	flex: 1;
	min-width: calc(50% - 5px);
	background-color: #f8f9fa;
	border-radius: 8px;
	padding: 12px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.action-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-text {
	font-size: 14px;
	color: #333;
}

.test-api-btn {
	border: 1px dashed #FF6B35;
	background-color: #FFF5F2;
}

/* 录音区域 */
.recording-area {
	background-color: #fff;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 40px 20px;
}

.recording-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20px;
}

.record-button-container {
	position: relative;
}

.record-button {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	background-color: #fff;
	border: 3px solid #007AFF;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease;
}

.record-button.recording {
	background-color: #ff4757;
	border-color: #ff4757;
	transform: scale(1.1);
}

.record-button.disabled {
	background-color: #f5f5f5;
	border-color: #ddd;
	opacity: 0.6;
	cursor: not-allowed;
}

.record-waves {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.wave {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid rgba(255, 71, 87, 0.3);
	animation: wave-animation 2s infinite;
}

.wave:nth-child(2) {
	animation-delay: 0.5s;
}

.wave:nth-child(3) {
	animation-delay: 1s;
}

@keyframes wave-animation {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.5);
		opacity: 0;
	}
}

.record-status {
	font-size: 16px;
	color: #666;
	text-align: center;
}

.record-timer {
	font-size: 24px;
	font-weight: 600;
	color: #ff4757;
}

/* 识别结果区域 */
.result-area {
	background-color: #fff;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 20px;
}

.result-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
}

.result-item {
	border-bottom: 1px solid #f0f0f0;
	padding: 15px 0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.result-time {
	font-size: 12px;
	color: #999;
}

.result-confidence {
	font-size: 12px;
	color: #007AFF;
}

.result-content {
	margin-bottom: 10px;
}

.result-content text {
	font-size: 14px;
	color: #333;
	line-height: 1.5;
}

.result-actions {
	display: flex;
	gap: 15px;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 4px 8px;
	border-radius: 4px;
	background-color: #f8f9fa;
}

.action-btn text {
	font-size: 12px;
	color: #666;
}

/* 筛选抽屉 */
.filter-drawer {
	height: 100%;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #e5e5e5;
}

.filter-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.filter-close {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-content {
	flex: 1;
	padding: 20px;
}

.filter-section {
	margin-bottom: 25px;
}

.filter-label {
	display: block;
	font-size: 14px;
	font-weight: 600;
	color: #333;
	margin-bottom: 10px;
}

.filter-item {
	margin-bottom: 10px;
}

.filter-input {
	width: 100%;
	height: 40px;
	border: 1px solid #e5e5e5;
	border-radius: 6px;
	padding: 0 12px;
	font-size: 14px;
}

.filter-footer {
	display: flex;
	gap: 10px;
	padding: 15px 20px;
	border-top: 1px solid #e5e5e5;
}

.filter-btn {
	flex: 1;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 600;
}

.reset-btn {
	background-color: #f8f9fa;
	color: #666;
}

.confirm-btn {
	background-color: #007AFF;
	color: #fff;
}

/* 调试信息样式 */
.debug-info {
	background-color: #f8f9fa;
	margin: 0 15px 20px;
	border-radius: 8px;
	padding: 15px;
	border: 1px solid #e9ecef;
}

.debug-title {
	font-size: 14px;
	font-weight: 600;
	color: #495057;
	margin-bottom: 10px;
}

.debug-item {
	font-size: 12px;
	color: #6c757d;
	margin-bottom: 5px;
	font-family: monospace;
}
</style>
