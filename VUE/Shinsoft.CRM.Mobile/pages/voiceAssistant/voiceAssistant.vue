<template>
	<view class="voice-assistant">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="navbar-title">语音助手</view>
				<view class="navbar-right" @click="showFilter">
					<uni-icons type="tune" size="20" color="#333"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 快捷操作区域 -->
		<view class="quick-actions">
			<view class="action-title">快捷操作</view>
			<view class="action-list">
				<view class="action-item" v-for="(item, index) in quickActions" :key="index" @click="selectQuickAction(item)">
					<view class="action-icon">
						<uni-icons :type="item.icon" size="16" color="#007AFF"></uni-icons>
					</view>
					<text class="action-text">{{ item.text }}</text>
				</view>
				<!-- 测试API连接按钮 -->
				<view class="action-item test-api-btn" @click="testApiConnection">
					<view class="action-icon">
						<uni-icons type="gear" size="16" color="#FF6B35"></uni-icons>
					</view>
					<text class="action-text">测试API</text>
				</view>
			</view>
		</view>

		<!-- 录音区域 -->
		<view class="recording-area">
			<view class="recording-container">
				<!-- 录音按钮 -->
				<view class="record-button-container">
					<view 
						class="record-button" 
						:class="{ 'recording': isRecording }"
						@touchstart="startRecording"
						@touchend="stopRecording"
						@touchcancel="stopRecording"
					>
						<view class="record-icon">
							<uni-icons type="mic" size="40" :color="isRecording ? '#fff' : '#007AFF'"></uni-icons>
						</view>
						<view class="record-waves" v-if="isRecording">
							<view class="wave" v-for="n in 3" :key="n"></view>
						</view>
					</view>
				</view>

				<!-- 录音状态 -->
				<view class="record-status">
					<text v-if="!isRecording">按住录音</text>
					<text v-else>录音中... {{ recordingTime }}s</text>
				</view>

				<!-- 录音时长显示 -->
				<view class="record-timer" v-if="isRecording">
					<text>{{ formatTime(recordingTime) }}</text>
				</view>
			</view>
		</view>

		<!-- 识别结果区域 -->
		<view class="result-area" v-if="recognitionResults.length > 0">
			<view class="result-title">识别结果</view>
			<view class="result-list">
				<view class="result-item" v-for="(result, index) in recognitionResults" :key="index">
					<view class="result-header">
						<text class="result-time">{{ formatDateTime(result.createTime) }}</text>
						<text class="result-confidence">置信度: {{ (result.confidence * 100).toFixed(1) }}%</text>
					</view>
					<view class="result-content">
						<text>{{ result.text }}</text>
					</view>
					<view class="result-actions">
						<view class="action-btn" @click="copyText(result.text)">
							<uni-icons type="copy" size="14" color="#666"></uni-icons>
							<text>复制</text>
						</view>
						<view class="action-btn" @click="shareText(result.text)">
							<uni-icons type="share" size="14" color="#666"></uni-icons>
							<text>分享</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 筛选抽屉 -->
		<uni-drawer ref="filterDrawer" mode="right" :width="300">
			<view class="filter-drawer">
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<view class="filter-close" @click="closeFilter">
						<uni-icons type="close" size="20" color="#333"></uni-icons>
					</view>
				</view>
				
				<view class="filter-content">
					<!-- 时间筛选 -->
					<view class="filter-section">
						<text class="filter-label">时间</text>
						<view class="filter-item">
							<uni-datetime-picker 
								v-model="filterData.startTime" 
								type="datetime" 
								placeholder="开始时间"
								@change="onStartTimeChange"
							></uni-datetime-picker>
						</view>
						<view class="filter-item">
							<uni-datetime-picker 
								v-model="filterData.endTime" 
								type="datetime" 
								placeholder="结束时间"
								@change="onEndTimeChange"
							></uni-datetime-picker>
						</view>
					</view>

					<!-- 部门筛选 -->
					<view class="filter-section">
						<text class="filter-label">部门</text>
						<view class="filter-item">
							<input 
								class="filter-input" 
								v-model="filterData.department" 
								placeholder="请输入部门名称"
							/>
						</view>
					</view>

					<!-- 执行人筛选 -->
					<view class="filter-section">
						<text class="filter-label">执行人</text>
						<view class="filter-item">
							<input 
								class="filter-input" 
								v-model="filterData.executor" 
								placeholder="请输入执行人姓名"
							/>
						</view>
					</view>
				</view>

				<view class="filter-footer">
					<view class="filter-btn reset-btn" @click="resetFilter">重置</view>
					<view class="filter-btn confirm-btn" @click="applyFilter">确定</view>
				</view>
			</view>
		</uni-drawer>

		<!-- 加载提示 -->
		<uni-load-more v-if="isProcessing" status="loading" :content-text="loadingText"></uni-load-more>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { getUserInfo } from "@/utils/sysTool.js"

const { proxy, appContext } = getCurrentInstance()
const $http = appContext.config.globalProperties.$http

// 响应式数据
const isRecording = ref(false)
const recordingTime = ref(0)
const isProcessing = ref(false)
const loadingText = ref('正在识别...')
const recognitionResults = ref([])

// 录音相关
let recordingTimer = null
let mediaRecorder = null
let audioChunks = []

// 筛选数据
const filterData = reactive({
	startTime: '',
	endTime: '',
	department: '',
	executor: ''
})

// 快捷操作数据
const quickActions = ref([
	{ icon: 'calendar', text: '创建日程', action: 'createSchedule' },
	{ icon: 'contact', text: '添加联系人', action: 'addContact' },
	{ icon: 'chatboxes', text: '发送消息', action: 'sendMessage' },
	{ icon: 'location', text: '记录位置', action: 'recordLocation' },
	{ icon: 'compose', text: '创建备忘', action: 'createMemo' },
	{ icon: 'phone', text: '拨打电话', action: 'makeCall' }
])

// 用户信息
const userInfo = ref({})

onMounted(() => {
	userInfo.value = getUserInfo()
	loadHistoryRecords()
})

onUnmounted(() => {
	if (recordingTimer) {
		clearInterval(recordingTimer)
	}
	if (mediaRecorder && mediaRecorder.state !== 'inactive') {
		mediaRecorder.stop()
	}
})

// 返回上一页
function goBack() {
	uni.navigateBack()
}

// 显示筛选抽屉
function showFilter() {
	proxy.$refs.filterDrawer.open()
}

// 关闭筛选抽屉
function closeFilter() {
	proxy.$refs.filterDrawer.close()
}

// 开始录音
async function startRecording() {
	try {
		isRecording.value = true
		recordingTime.value = 0
		audioChunks = []

		// 获取录音权限
		const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
		
		mediaRecorder = new MediaRecorder(stream)
		
		mediaRecorder.ondataavailable = (event) => {
			if (event.data.size > 0) {
				audioChunks.push(event.data)
			}
		}

		mediaRecorder.onstop = () => {
			processRecording()
		}

		mediaRecorder.start()

		// 开始计时
		recordingTimer = setInterval(() => {
			recordingTime.value++
		}, 1000)

	} catch (error) {
		console.error('录音启动失败:', error)
		uni.showToast({
			title: '录音权限获取失败',
			icon: 'none'
		})
		isRecording.value = false
	}
}

// 停止录音
function stopRecording() {
	if (!isRecording.value) return

	isRecording.value = false
	
	if (recordingTimer) {
		clearInterval(recordingTimer)
		recordingTimer = null
	}

	if (mediaRecorder && mediaRecorder.state !== 'inactive') {
		mediaRecorder.stop()
	}
}

// 处理录音数据
async function processRecording() {
	if (audioChunks.length === 0) return

	isProcessing.value = true
	loadingText.value = '正在识别语音...'

	try {
		// 创建音频文件
		const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
		const formData = new FormData()
		formData.append('audioFile', audioBlob, 'recording.wav')

		// 调用后端API进行语音识别
		const response = await $http.speech.uploadAudioFile(formData)
		
		if (response && response.data) {
			const result = {
				...response.data,
				createTime: new Date()
			}
			recognitionResults.value.unshift(result)
			
			uni.showToast({
				title: '识别成功',
				icon: 'success'
			})
		}
	} catch (error) {
		console.error('语音识别失败:', error)
		uni.showToast({
			title: '识别失败，请重试',
			icon: 'none'
		})
	} finally {
		isProcessing.value = false
	}
}

// 选择快捷操作
function selectQuickAction(item) {
	uni.showToast({
		title: `执行${item.text}`,
		icon: 'none'
	})
	// 这里可以根据不同的action执行不同的操作
}

// 测试API连接
async function testApiConnection() {
	uni.showLoading({
		title: '测试连接中...'
	})

	try {
		const response = await $http.speech.testAliBaiLianConnection()

		uni.hideLoading()

		if (response && response.success) {
			uni.showModal({
				title: '测试成功',
				content: `API连接正常\n状态: ${response.data.Status}\n时间: ${response.data.Timestamp}`,
				showCancel: false
			})
		} else {
			uni.showModal({
				title: '测试失败',
				content: response?.message || '未知错误',
				showCancel: false
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('API测试失败:', error)
		uni.showModal({
			title: '测试失败',
			content: `连接错误: ${error.message || '网络异常'}`,
			showCancel: false
		})
	}
}

// 复制文本
function copyText(text) {
	uni.setClipboardData({
		data: text,
		success: () => {
			uni.showToast({
				title: '已复制到剪贴板',
				icon: 'success'
			})
		}
	})
}

// 分享文本
function shareText(text) {
	uni.share({
		provider: 'weixin',
		type: 1,
		summary: text,
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		}
	})
}

// 格式化时间
function formatTime(seconds) {
	const mins = Math.floor(seconds / 60)
	const secs = seconds % 60
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化日期时间
function formatDateTime(date) {
	return new Date(date).toLocaleString()
}

// 加载历史记录
async function loadHistoryRecords() {
	try {
		const response = await $http.speech.getSpeechHistory({
			pageIndex: 1,
			pageSize: 20
		})
		
		if (response && response.data && response.data.items) {
			recognitionResults.value = response.data.items
		}
	} catch (error) {
		console.error('加载历史记录失败:', error)
	}
}

// 筛选相关方法
function onStartTimeChange(value) {
	filterData.startTime = value
}

function onEndTimeChange(value) {
	filterData.endTime = value
}

function resetFilter() {
	filterData.startTime = ''
	filterData.endTime = ''
	filterData.department = ''
	filterData.executor = ''
}

function applyFilter() {
	// 应用筛选条件
	console.log('应用筛选条件:', filterData)
	closeFilter()
	// 这里可以根据筛选条件重新加载数据
}
</script>

<style scoped>
.voice-assistant {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 15px;
	padding-top: var(--status-bar-height);
}

.navbar-left, .navbar-right {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

/* 快捷操作区域 */
.quick-actions {
	margin-top: calc(44px + var(--status-bar-height) + 20px);
	background-color: #fff;
	margin: calc(44px + var(--status-bar-height) + 20px) 15px 20px;
	border-radius: 12px;
	padding: 20px;
}

.action-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
}

.action-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.action-item {
	flex: 1;
	min-width: calc(50% - 5px);
	background-color: #f8f9fa;
	border-radius: 8px;
	padding: 12px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.action-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-text {
	font-size: 14px;
	color: #333;
}

.test-api-btn {
	border: 1px dashed #FF6B35;
	background-color: #FFF5F2;
}

/* 录音区域 */
.recording-area {
	background-color: #fff;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 40px 20px;
}

.recording-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20px;
}

.record-button-container {
	position: relative;
}

.record-button {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	background-color: #fff;
	border: 3px solid #007AFF;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease;
}

.record-button.recording {
	background-color: #ff4757;
	border-color: #ff4757;
	transform: scale(1.1);
}

.record-waves {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.wave {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid rgba(255, 71, 87, 0.3);
	animation: wave-animation 2s infinite;
}

.wave:nth-child(2) {
	animation-delay: 0.5s;
}

.wave:nth-child(3) {
	animation-delay: 1s;
}

@keyframes wave-animation {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.5);
		opacity: 0;
	}
}

.record-status {
	font-size: 16px;
	color: #666;
	text-align: center;
}

.record-timer {
	font-size: 24px;
	font-weight: 600;
	color: #ff4757;
}

/* 识别结果区域 */
.result-area {
	background-color: #fff;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 20px;
}

.result-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
}

.result-item {
	border-bottom: 1px solid #f0f0f0;
	padding: 15px 0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.result-time {
	font-size: 12px;
	color: #999;
}

.result-confidence {
	font-size: 12px;
	color: #007AFF;
}

.result-content {
	margin-bottom: 10px;
}

.result-content text {
	font-size: 14px;
	color: #333;
	line-height: 1.5;
}

.result-actions {
	display: flex;
	gap: 15px;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 4px 8px;
	border-radius: 4px;
	background-color: #f8f9fa;
}

.action-btn text {
	font-size: 12px;
	color: #666;
}

/* 筛选抽屉 */
.filter-drawer {
	height: 100%;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #e5e5e5;
}

.filter-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.filter-close {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-content {
	flex: 1;
	padding: 20px;
}

.filter-section {
	margin-bottom: 25px;
}

.filter-label {
	display: block;
	font-size: 14px;
	font-weight: 600;
	color: #333;
	margin-bottom: 10px;
}

.filter-item {
	margin-bottom: 10px;
}

.filter-input {
	width: 100%;
	height: 40px;
	border: 1px solid #e5e5e5;
	border-radius: 6px;
	padding: 0 12px;
	font-size: 14px;
}

.filter-footer {
	display: flex;
	gap: 10px;
	padding: 15px 20px;
	border-top: 1px solid #e5e5e5;
}

.filter-btn {
	flex: 1;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 600;
}

.reset-btn {
	background-color: #f8f9fa;
	color: #666;
}

.confirm-btn {
	background-color: #007AFF;
	color: #fff;
}
</style>
