<template>
	<view>
		<view class="home-header">
			<uni-badge class="uni-badge-left-margin message" :text="msgCount" absolute="rightTop" :offset="[-3, 3]"
				size="small" @click="showMsg">
				<uni-icons type="email-filled" color="#fff" size="30" @click="showMsg"></uni-icons>
			</uni-badge>
			<view class="home-user">
				<view class="user">
					<wu-image width="50" height="50" shape="circle" :src="image"></wu-image>
					<view class="user-info">
						<view>{{userInfo.displayName}}</view>
						<!-- <view class="">
							销售
						</view> -->
					</view>
				</view>
			</view>
		</view>
		<!-- <workingPlan></workingPlan> -->
		<view class="head-icon-view" style="background-color: white; margin-top: 25px;">
			<view style="font-weight: bold;">
				行为管理
			</view>
			<view class="home-icons">
				<uni-row>
					<uni-col :span="span">
						<view class="home-icon-item" @click="jointCall">
							<view class="icon-layou">
								<view class="icon-position icon-font-blue">
									<view class="iconfont icon-handshake1 btn-font "></view>
								</view>
								<view class="icon-text">拜访</view>
							</view>
						</view>
					</uni-col>
					<!-- <uni-col :span="span">
						<view class="home-icon-item" @click="jointCall">
							<view class="icon-layou">
								<view class="icon-position icon-font-green">
									<view class="iconfont icon-shejiao btn-font "></view>
								</view>
								<view class="icon-text">协访</view>
							</view>
						</view>
					</uni-col> -->
					<uni-col :span="span">
						<view class="home-icon-item" @click="training">
							<view class="icon-layou">
								<view class="icon-position icon-font-zi">
									<view class="iconfont icon-__yx_meeting btn-font "></view>
								</view>
								<view class="icon-text">培训</view>
							</view>
						</view>
					</uni-col>

					<uni-col :span="span">
						<view class="home-icon-item" @click="meeting">
							<view class="icon-layou">
								<view class="icon-position icon-font-cheng">
									<view class="iconfont icon-huiyishi btn-font "></view>
								</view>
								<view class="icon-text">会议</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="hospitalDevelop">
							<view class="icon-layou">
								<view class="icon-position icon-font-red">
									<view class="iconfont icon-yiyuan2 btn-font "></view>
								</view>
								<view class="icon-text">医院开发</view>
							</view>
						</view>
					</uni-col>
				</uni-row>
				<uni-row>
					<uni-col :span="span">
						<view class="home-icon-item" @click="voiceAssistant">
							<view class="icon-layou">
								<view class="icon-position icon-font-purple">
									<view class="iconfont icon-add btn-font "></view>
								</view>
								<view class="icon-text">语音助手</view>
							</view>
						</view>
					</uni-col>
				</uni-row>
			</view>
		</view>


		<!-- <workingPlan></workingPlan> -->
		<view class="head-icon-view" style="background-color: white;margin-top: 15px;">
			<view style="font-weight: bold;">
				行为审批
			</view>
			<view class="home-icons">
				<uni-row>
					<uni-col :span="span">
						<view class="home-icon-item" @click="jointCallApprove">
							<view class="icon-layou">
								<view class="icon-position icon-font-blue">
									<view class="iconfont icon-handshake1 btn-font-approve "></view>
									<view class="icon-extend">
										<uni-icons  type="checkbox-filled" size="18"
											color="#4c86d2"></uni-icons>
									</view>
									
								</view>
								<view class="icon-text">拜访</view>
							</view>
						</view>
					</uni-col>
					<!-- <uni-col :span="span">
						<view class="home-icon-item" @click="jointCallApprove">
							<view class="icon-layou">
								<view class="icon-position icon-font-green">
									<view class="iconfont icon-shejiao btn-font-approve "></view>
									<view class="icon-extend">
										<uni-icons  type="checkbox-filled" size="18"
											color="#67d379"></uni-icons>
									</view>
								</view>
								<view class="icon-text">协访</view>
							</view>
						</view>
					</uni-col> -->
					<uni-col :span="span">
						<view class="home-icon-item" @click="trainingApprove">
							<view class="icon-layou">
								<view class="icon-position icon-font-zi">
									<view class="iconfont icon-__yx_meeting btn-font-approve " style="font-size: 2rem;">
									</view>
									<view class="icon-extend">
										<uni-icons  type="checkbox-filled" size="18"
											color="#eeb9e9"></uni-icons>
									</view>
								</view>
								<view class="icon-text">培训</view>
							</view>
						</view>
					</uni-col>
					
					<uni-col :span="span">
						<view class="home-icon-item" @click="meetingApprove">
							<view class="icon-layou">
								<view class="icon-position icon-font-cheng">
									<view class="iconfont icon-huiyishi btn-font-approve "></view>
									<view class="icon-extend">
										<uni-icons  type="checkbox-filled" size="18"
											color="#ffaa7f"></uni-icons>
									</view>
								</view>
								<view class="icon-text">会议</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="hospitalDevelopApprove">
							<view class="icon-layou">
								<view class="icon-position icon-font-red">
									<view class="iconfont icon-yiyuan2 btn-font-approve "></view>
									<view class="icon-extend">
										<uni-icons type="checkbox-filled" size="18"
											color="#ff694e"></uni-icons>
									</view>
								</view>
								<view class="icon-text">医院开发</view>
							</view>
						</view>
					</uni-col>
				</uni-row>
			</view>
		</view>
		

		<view class="head-icon-view" style="background-color: white;margin-top: 15px;">
			<view style="font-weight: bold;">
				基础信息
			</view>
			<view class="home-icons">
				<uni-row>
					<uni-col :span="span">
						<view class="home-icon-item" @click="customer">
							<view class="icon-layou">
								<view class="icon-position icon-font-yellow">
									<view class="iconfont icon-yiyuan1 btn-font "></view>
								</view>
								<view class="icon-text">机构</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="freezeabelCustomer">
							<view class="icon-layou">
								<view class="icon-position icon-font-zi">
									<view class="iconfont icon-suoding btn-font "></view>
								</view>
								<view class="icon-text">已冻结机构</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="contact">
							<view class="icon-layou">
								<view class="icon-position icon-font-cheng">
									<view class="iconfont icon-ic_lianxiren btn-font "></view>
								</view>
								<view class="icon-text">客户</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="doctorOut">
							<view class="icon-layou">
								<view class="icon-position icon-font-blue">
									<view class="iconfont icon-doctor_scheduling btn-font "></view>
								</view>
								<view class="icon-text">医生出诊</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="product">
							<view class="icon-layou">
								<view class="icon-position icon-font-green">
									<view class="iconfont icon-chanpin-copy btn-font "></view>
								</view>
								<view class="icon-text">产品</view>
							</view>
						</view>
					</uni-col>
					<uni-col :span="span">
						<view class="home-icon-item" @click="docResource">
							<view class="icon-layou">
								<view class="icon-position icon-font-red">
									<view class="iconfont icon-ziliaoku btn-font "></view>
								</view>
								<view class="icon-text">资料</view>
							</view>
						</view>
					</uni-col>
				</uni-row>
			</view>
		</view>

		<view class="head-icon-view" style="background-color: white;margin-top: 15px;margin-bottom: 30px;">
			<view style="font-weight: bold;">
				其他
			</view>
			<view class="home-icons">
				<uni-row>
					<uni-col :span="span">
						<view class="home-icon-item" @click="appealSalesFlow">
							<view class="icon-layou">
								<view class="icon-position icon-font-red">
									<view class="iconfont icon-shensu2 btn-font "></view>
								</view>
								<view class="icon-text">流向申诉</view>
							</view>
						</view>
					</uni-col>
				</uni-row>
			</view>
		</view>

	</view>
</template>

<script setup>
	import {
		ref,
		getCurrentInstance,
		inject,
		toRaw
	} from 'vue'
	import defimg from "@/static/default-header.png"
	import {
		getUserInfo
	} from "@/utils/sysTool.js"

	const {
		proxy,
		appContext
	} = getCurrentInstance();
	const $http = appContext.config.globalProperties.$http
	const image = ref(defimg)
	const userInfo = ref({})
	userInfo.value = getUserInfo()
	if (userInfo.value.avatar) {
		image.value = userInfo.value.avatar
	}
	const span = 6
	const msgCount = ref()

	function customer() {
		uni.navigateTo({
			url: '/pages/customer/customer'
		})
	}

	function freezeabelCustomer() {
		uni.navigateTo({
			url: '/pages/customer/freezeabelCustomer'
		})
	}

	function training() {
		uni.navigateTo({
			url: '/pages/training/training'
		})
	}

	function contact() {
		uni.navigateTo({
			url: '/pages/contact/contact'
		})
	}

	function callOn() {
		uni.navigateTo({
			url: '/pages/callOn/callOn'
		})
	}

	function meeting() {
		uni.navigateTo({
			url: '/pages/meeting/meeting'
		})
	}

	function hospitalDevelop() {
		uni.navigateTo({
			url: '/pages/hospitalDevelop/hospitalDevelop'
		})
	}

	function jointCall() {
		uni.navigateTo({
			url: '/pages/coopVisit/coopVisit'
		})
	}

	function doctorOut() {
		uni.navigateTo({
			url: '/pages/doctorOutCalls/doctorOutCalls'
		})
	}

	function statistics() {
		uni.navigateTo({
			url: '/pages/statistics/statistics'
		})
	}

	function showMsg() {
		uni.navigateTo({
			url: '/pages/msg/msg'
		})
	}

	function product() {
		uni.navigateTo({
			url: '/pages/product/product'
		})
	}

	function docResource() {
		uni.navigateTo({
			url: '/pages/docResource/docResource'
		})
	}

	function callOnApprove() {
		uni.navigateTo({
			url: '/pages/callOn/approveCallOn/approveList'
		})
	}

	function jointCallApprove() {
		uni.navigateTo({
			url: '/pages/coopVisit/approveCoopVisit/approveList'
		})
	}

	function trainingApprove() {
		uni.navigateTo({
			url: '/pages/training/approveTraining/approveList'
		})
	}

	function hospitalDevelopApprove() {
		uni.navigateTo({
			url: '/pages/hospitalDevelop/approveHospitalDevelop/approveList'
		})
	}

	function meetingApprove() {
		uni.navigateTo({
			url: '/pages/meeting/approveMeeting/approveMeetingList'
		})
	}
	function appealSalesFlow() {
		uni.navigateTo({
			url: '/pages/appeal/salesFlowSummary/salesFlowSummary'
		})
	}

	function voiceAssistant() {
		uni.navigateTo({
			url: '/pages/voiceAssistant/voiceAssistant'
		})
	}

	function loadEventCount() {
		$http.today.getDailyEventCount({}).then((res) => {
			let count = 0
			if (res) {
				count = res.data
			}

			if (count && count > 0) {
				uni.setTabBarBadge({
					index: 0,
					text: count.toString()
				})
			} else {
				uni.removeTabBarBadge({ //移除指定位置的tabbar右上角的标志
					index: 0,
				})
			}
		})
	}
	// loadEventCount()

	function loadMsgCount() {
		$http.home.getMyMsgUnreadCount({}).then((res) => {
			let count = 0
			if (res) {
				count = res.data
			}

			msgCount.value = count && count > 0 ? count.toString() : ''
		})
	}
	// loadMsgCount()

	import {
		onShow
	} from '@dcloudio/uni-app';
	onShow(() => {
		loadMsgCount()
		loadEventCount()
	})
</script>

<style>

</style>