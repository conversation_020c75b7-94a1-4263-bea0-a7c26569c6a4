<script>
	import routingIntercept from "/api/permission.js"
	import {
		getToken,
		setVirtual
	} from '@/utils/sysTool.js'
	import {
		getCurrentInstance,
	} from 'vue'


	export default {
		onLaunch: function() {
			// console.log('App Launch')
			routingIntercept()
			// if(!getToken()){
			// 	uni.navigateTo({
			// 		url:"/pages/login/login"
			// 	})
			// }
			// const {
			// 	appContext
			// } = getCurrentInstance();
			// const $http = appContext.config.globalProperties.$http
			// $http.users.getRawUri().then((res) => {
			// 	if (res) {
			// 		console.log(res)
			// 		// setVirtual(res)
			// 	}
			// })
			// let pathname = window.location.pathname
			// pathname = pathname.slice(0,pathname.length-1);
			// console.log(pathname.split("/"))
		},
		onShow: function() {
			// console.log('App Show')
		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style>
	@import url("static/css/common.css");
	@import url("static/css/customer.scss");
	@import url("static/css/station.scss");
	@import url("static/font/iconfont.css");
	/* @import url("static/css/test.scss"); */
	/*每个页面公共css */
</style>