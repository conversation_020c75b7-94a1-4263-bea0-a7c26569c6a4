## wu-navbar 按钮

> **组件名：wu-navbar**

此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，自动状态栏高度、小程序胶囊预留位置等，一般建议使用 `uni-app` 带的导航栏。

## [查看文档](https://wu.geeks.ink/zh-CN/components/navbar.html)

## [更多组件, 请查看 `wu-ui` 组件库](https://ext.dcloud.net.cn/plugin?name=wu--ui)
(请勿下载插件zip)

<a href="https://ext.dcloud.net.cn/plugin?name=wu--ui">
	<img src="https://wu.geeks.ink/intr.png">
</a>

**如使用过程中有任何问题，或者您对wu-ui有一些好的建议。<br>欢迎加入 [wu-ui 交流群](https://wu.geeks.ink/zh-CN/components/qqFeedBack.html)**