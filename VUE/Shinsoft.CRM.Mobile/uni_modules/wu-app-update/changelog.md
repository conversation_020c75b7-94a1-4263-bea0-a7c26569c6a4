## 1.0.7（2023-09-22）
静默更新完成后，改为确认框，让用户手动确认，优化流程，更贴近用户使用场景
## 1.0.6（2023-09-22）
将热更新重启，改为原生方法杀死app，让用户手动重启, 来解决热更新样式错乱的bug
## 1.0.5（2023-09-09）
比对版本成功后将强制安装，不在二次比对，因为客户手机可能存在安装包缓存
## 1.0.4（2023-09-06）
增加检测更新平台platform字段
## 1.0.3（2023-09-05）
修复没有与后台比对版本的bug
## 1.0.2（2023-09-05）
修复v2、v3 promise化差异带来的bug
## 1.0.1（2023-09-04）
修复checkVersion 执行错误的reject
## 1.0.0（2023-09-04）
app升级、整包更新、热更新、强制更新、ios跳转更新、android跳转应用市场，间隔式提醒用户更新，所有细节都可在config.js中修改。
