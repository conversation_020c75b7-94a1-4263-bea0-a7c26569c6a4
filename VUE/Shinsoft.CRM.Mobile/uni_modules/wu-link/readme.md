## wu-link 超链接

> **组件名：wu-link**

该组件为超链接组件，在不同平台有不同表现形式：

+ 在APP平台会通过plus环境打开内置浏览器
+ 在小程序中把链接复制到粘贴板，同时提示信息
+ 在H5中通过window.open打开链接

## [查看文档](https://wu.geeks.ink/zh-CN/components/link.html)

## [更多组件, 请查看 `wu-ui` 组件库](https://ext.dcloud.net.cn/plugin?name=wu--ui)
(请勿下载插件zip)

<a href="https://ext.dcloud.net.cn/plugin?name=wu--ui">
	<img src="https://wu.geeks.ink/intr.png">
</a>

**如使用过程中有任何问题，或者您对wu-ui有一些好的建议。<br>欢迎加入 [wu-ui 交流群](https://wu.geeks.ink/zh-CN/components/qqFeedBack.html)**