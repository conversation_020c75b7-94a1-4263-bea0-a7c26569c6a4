## 2.6.2（2023-10-31）
1.`修复` 在源码中有异常字符导致的在vue3中编译报错的问题。  
2.`修复` 在微信小程序中`z-paging-refresh`的height无效的问题（by xiaohe0601）。  
近期更新：  
=============================  
1.`新增` 手动更新自定义下拉刷新view高度方法。  
2.`新增` 点击返回顶部按钮添加事件监听&支持拦截。  
3.`新增` 是否开启下拉刷新状态栏占位，适用于隐藏导航栏时，下拉刷新需要避开状态栏高度的情况。  
4.`新增` 支持配置网络请求失败触发`reject`。  
5.`修复` 显示空数据图时，滚动到底部依然可以加载更多的问题。  
6.`修复` 在vue2中底部加载更多相关`slot`使用`template`插入无效的问题。  
7.`修复` `complete`的`Promise`可能无效的问题。  
8.`优化` `hooks`判断`z-paging`为空则不调用。 
  


