{"id": "wu--ui", "displayName": "wu-ui如虎添翼 之兼容vue3+2、app、h5、小程序等多端，无论平台，一致体验。", "version": "1.1.2", "description": "wu-ui 是 全面兼容多端的uni-app生态框架，基于vue2、vue3和nvue开发。丰富UI组件库，便捷工具库，简单高效。无论平台，一致体验。选择我们，开发更轻松！", "keywords": ["wu-ui,", "wuui,", "ui框架,", "uni-app,", "nvue"], "repository": "", "engines": {"HBuilderX": "^3.4.15"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["wu-calendar", "wu-sku", "wu-image", "wu-button", "wu-icon", "wu-loading-icon", "wu-ui-tools", "wu-row", "wu-link", "wu-text", "wu-number-box", "wu-transition", "wu-input", "wu-code", "wu-navbar", "wu-status-bar", "wu-safe-bottom", "wu-radio", "wu-checkbox", "wu-textarea", "wu-gap", "wu-cell", "wu-app-update", "wu-popup", "wu-overlay", "wu-action-sheet"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}