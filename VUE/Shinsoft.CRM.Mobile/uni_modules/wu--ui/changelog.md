## 1.1.2（2023-09-18）
1.修复wu-calendar回到今日错误
2.优化wu-calendar picker日期与当前日历日期同步
3.wu-calendar新增mode属性，用来控制单日期、多日期、日期选择范围模式
4.优化wu-calendar date属性来更好的指定多日期、范围日期的默认值
## 1.1.1（2023-09-12）
1. 新增wu-popup弹出层组件
2. 新增wu-overlay遮罩层组件
3. 新增wu-action-sheet操作菜单组件
## 1.1.0（2023-09-12）
1. 新增wu-popup弹出层组件
2. 新增wu-overlay遮罩层组件
3. 新增wu-action-sheet操作菜单组件
## 1.0.9（2023-09-11）
1. wu-calendar修复每月仅展示当月数据时星期与日期对比错误
2. 修复wu-line在app端的线条表现
3. 修复所有组件click事件会触发两次bug
4. wu-caleardar新增rangeEndRepick属性用来指定日期范围选择后点击范围内的日期是否可以重选结束日期
5. 优化wu-app-update比对版本成功后的安装方式改为强制安装，不在二次比对，因为客户手机可能存在安装包缓存
6. 优化wu-safe-bottom底部安全区域计算方法
## 1.0.8（2023-09-05）
更新pages.json文件
## 1.0.7（2023-09-05）
1. 新增app-update组建
2. 优化wu-ui-tools工具Color方法依赖包，改为本地访问
3. 优化wu-calendar动态计算算法
4. 优化wu-calendar点击时加载缓慢
## 1.0.6（2023-09-01）
1. 新增gap组件
2. 新增status-bar组件
3. 新增safe-bottom组件
4. 新增textarea组件
5. 新增checkbox组件
6. 新增cell组件
7. 新增code组件
8. 新增input组件
9. 新增radio组件
10. 新增navbar组件
## 1.0.5（2023-08-21）
新增项目示例
## 1.0.4（2023-08-20）
增加日历组件
## 1.0.3（2023-08-12）
1. 新增sku组件
2. 新增layout组件
3. 新增text组件
4. 新增link组件
5. 新增numberBox组件
6. 新增loadingIcon组件
## 1.0.2（2023-08-12）
1. 新增sku组件
2. 新增layout组件
3. 新增text组件
4. 新增link组件
5. 新增numberBox组件
6. 新增loadingIcon组件
## 1.0.1（2023-08-08）
1. 将内置组件方式改为连接式组件以此来增加产出
2. 更新说明
## 1.0.0（2023-08-08）
1. 新增button组件
2. 新增icon组件
3. 新增loading-icon组件
4. 新增tools工具库
