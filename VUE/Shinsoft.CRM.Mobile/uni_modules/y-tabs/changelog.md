## 2.0.7（2023-10-13）
+ "新闻"列表案例进行非App端缓存处理
+ 将click事件的执行顺序放在change之前
## 2.0.6（2023-08-30）
+ 修复Tabs Props的设定的type值
+ 增加loaded事件，该事件会在组件内部初始化完成后调用
+ 增加示例“在Popup中的标签页”

## 2.0.5（2023-08-29）
+ 修复发布版本
## 2.0.3（2023-08-25）
+ 修复存在标签栏插槽时，标签滚动至中心位置不正确的问题
+ 修复在动态增加、删除标签时，切换标签错误的bug
## 2.0.2（2023-07-28）
+ 修复自定义导航栏下滚动导航定位不准确的问题
+ 增加示例“自定义导航栏下的标签页滚动吸顶”、“ 可收起标签页-防小红书首页标签栏 ”
## 2.0.1（2023-05-28）
+ <font color="#ff0000" size="4">2.0.1为非兼容性升级，重命名了诸多样式以及提供的属性名，也移除了部分属性及事件，如无必要，请勿升级。</font>
+ 如果不小心升级，可以在y-tabs/components/version中找到之前的版本。
+ 从2.0.1版本开始，对于吸顶以及滚动导航定位标签的实现已调整了内部逻辑，因此无需在页面中的onPageScroll生命周期调用this.$emit("onPageScroll",e)；
+ 新增bar-animate-mode属性，用于设置滑块切换的动画模式；
+ 新增bar-style属性，用于设置滑块样式；
+ 新增bar插槽，用于自定义滑块的内容；
+ line-width、line-height属性名改为bar-width、bar-height；
+ 移除部分tabs Props: tab-click-scroll-top、navHeight 、navWidth 、contentHeight；
+ 请注意部分属性默认值的调整；
+ 移除scroll事件，如果有用到该事件判断标签栏是否吸顶，请替换为sticky-change事件； 
+ 新增sticky-change事件，该事件传递了{isFixed}参数，用于说明标签栏是否吸顶固定；
+ 移除'滚动吸顶+滑动切换'案例
+ 新增'与swiper组件联动'案例；
+ 在QQ/百度/字节跳动/飞书小程序中，自定义组件在渲染时会比App/H5端多一级节点，导致标签内容样式失效，需在组件上添加".y-tab-virtual"的样式；

## 1.1.6（2023-02-07）
+ 增加shrink属性用于开启收缩布局
## 1.1.5（2023-01-12）
+ 修复侧边栏导航模式下，左侧标签联动不准确的问题
## 1.1.4（2022-12-29）
+ bug修复
## 1.1.3（2022-11-15）
+ 增加滚动吸顶+滑动切换案例
## 1.1.2（2022-11-14）
+ bug修复
## 1.1.1（2022-11-04）
+ vue3环境下修复H5、app端使用标题插槽时下划线错位的问题
## 1.1.0（2022-10-27）
+ 对于禁止重复点击的比较使用==
## 1.0.9（2022-10-17）
+ 修复与优化
## 1.0.8（2022-10-16）
+ 优化侧边栏导航与滚动导航
+ 增加标签页的相关属性：navHeight、navWidth、contentHeight、pageScroll
+ 增加奶茶点单以及仿京东的商品分类联动导航示例
## 1.0.7（2022-10-13）
+ 优化
+ 对标签项提供了yui-tab__prev，yui-tab__next class类，用于标记是当前标签项附近的两个标签项
## 1.0.6（2022-10-11）
+ 优化底部线条动画
## 1.0.5（2022-10-11）
+ 新增postion属性用于控制在存在图标或图片的条件下标题所在的位置
## 1.0.4（2022-10-11）
+ 新增标题区域设置图标或图片的属性
## 1.0.3（2022-09-28）
+ 解决动态改变标题区域位置后底部线条错位的bug
## 1.0.2（2022-09-27）
+ 优化左右滑动切换的效果
## 1.0.1（2022-09-27）
+ 增加侧边栏导航
## 1.0.0（2022-09-25）
+ 写法更接近vant的tabs组件
