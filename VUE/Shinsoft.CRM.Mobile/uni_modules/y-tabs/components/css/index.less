.y-tabs {
	position: relative;

	// 标签栏垂直方位下的根容器样式
	&.is-vertical {
		display: flex;
		flex-wrap: nowrap;
		flex-direction: row;
		justify-content: flex-end;
		flex: 1;

		// 垂直时标签栏scroll-view的子项垂直排列
		.y-tabs__scroll {
			flex-direction: column;
		}
	}

	// 区域滚动下的滚动导航
	&.is-areaScroll.is-scrollNav {
		display: flex;
		height: 100vh;
		flex-direction: column;

		// 标签栏不收缩
		.y-tabs__wrap {
			flex-shrink: 0;
		}

		.y-tabs__track,
		.y-tabs__content-scrollview {
			height: 100%;
		}
	}

	// 区域滚动下的侧边栏导航
	&.is-areaScroll.is-sidebarNav {
		display: flex;
		height: 100vh;
		flex-direction: row;

		.y-tabs__scroll {
			height: 100%;
		}

		// 标签栏不收缩
		.y-tabs__wrap {
			flex-shrink: 0;
		}

		.y-tabs__track,
		.y-tabs__content-scrollview {
			height: 100%;
		}
	}
}

// uni-view,
// view {
// 	flex-direction: row;
// 	display: block;
// }

// 依赖元素
.y-tabs__depend {
	position: absolute;
	top: 0;
	left: 0;
	height: 1px; //必须保证有高度，否则observer无效
	width: 100%;
}

// 透明标签栏所需的依赖元素
.y-tabs__depend--transparent {
	position: absolute;
	top: 0;
	left: 0;
	height: 1px; //必须保证有高度，否则observer无效
	width: 1px;
}

// 模拟标签栏吸顶时设置offset时距屏幕顶部的元素
.y-tabs__depend--offset {
	position: fixed;
	top: 0;
	left: 0;
	z-index: -1;
	height: 1px;
}

// 标签栏占位元素
.y-tabs__placeholder {
	position: relative;
}

// 标签垂直展示且吸顶时，标签栏占位元素不伸缩
.y-tabs.is-fixed.is-vertical .y-tabs__placeholder {
	flex-shrink: 0;
}

// 文字省略
.y-tabs__ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

// 导航区域包裹层
.y-tabs__wrap {
	position: relative;
	display: flex;
	align-items: center;
	overflow: hidden;
	visibility: visible;
	background: #fff;
	touch-action: none;

	// 标签栏垂直展示时包裹层样式
	&.is-vertical {
		width: 100px;
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
	}

	// 粘性定位布局下的导航区域包裹层
	&.is-fixed {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		z-index: 99;
	}

	// 标签垂直展示且吸顶时，给定bottom，否则scroll-view不会滚动
	&.is-fixed.is-vertical {
		bottom: 0;
	}

	// 透明的导航区域包裹层
	&.is-transparent {
		background: rgba(255, 255, 255, 0);
	}

	// 导航区域包裹层--卡片风格
	&.is-card {
		margin: 0 16px;
		border-radius: 4px;
		box-sizing: border-box;
		border: 1px solid #0022ab;
	}
	// 标签栏水平时按钮风格的包裹层
	&.is-button:not(.is-vertical),
	&.is-line-button:not(.is-vertical) {
		padding: 0 8px;
	}
}

// ::v-deep.y-tabs__wrap .uni-scroll-view {
// 	// 在真正的滚动区域设置 阻断滚动穿透，滚动不会传播给祖先 (Firefox、Chromium 和 Edge 通过 AutoRefixer 支持此功能，但 Safari 目前还不支持此功能)
// 	overscroll-behavior-y: contain !important;
// }

// IOS 13 以下的系统，当滚动区域设置了-webkit-overflow-scrolling: touch;时（必须设置，否者几乎无法滚动），::-webkit-scrollbar 相关属性会失效，iOS 13 已经修复了此Bug。

// scroll-view组件样式
.y-tabs__scroll {
	position: relative;
	width: 100%;
	white-space: nowrap; // 使用横向滚动时，需要给<scroll-view>添加white-space: nowrap;样式
	flex-direction: row;

	// 小程序端: 去除 scroll-view 组件的滚动条
	&::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	// H5端去滚动条
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		-webkit-appearance: none;
		background: transparent;
		color: transparent;
	}
}

// 导航区域
.y-tabs__nav {
	position: relative;
	box-sizing: border-box;
	user-select: none;
	flex: 1;
	display: flex;

	// 标签栏垂直时导航区域样式
	&.is-vertical {
		flex-direction: column;
		height: auto;

		.y-tab {
			flex: unset;
		}
	}

	// 标签左侧、右侧的补充区域
	&-left,
	&-right {
		position: relative;
		display: inline-flex;
		white-space: nowrap;
	}
}

// 导航标签
.y-tab {
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	height: 40px;
	font-size: 28rpx;
	color: #646566;
	text-align: center;
	padding: 0 4px;
	flex: 1;
	cursor: pointer;
	// webkit的css扩展：1、-webkit-tap-highlight-color:这个属性是用于设定元素在移动设备（如Adnroid、iOS）上被触发点击事件时，响应的背景框的颜色。有事件监听的元素被点击的时候会被高亮显示，比如我的android上表现为一个蓝框加上半透明的背景
	-webkit-tap-highlight-color: transparent;
	// transition-duration: 0.2s;
	// transition-property: background;
	flex-shrink: 0;
	z-index: 2;

	// 选中状态
	&.is-active {
		color: #323233;
		font-weight: 500;
	}

	// 禁用状态
	&.is-disabled {
		color: #c8c9cc !important;
		cursor: not-allowed;
	}

	// 收缩布局
	&.is-shrink {
		flex: none;
		padding: 0 8px;
	}

	//卡片风格
	&.is-card {
		height: 28px;
		line-height: 28px;
	}

	// 非滑块动画的线条风格(选中状态)
	// &.is-line.is-active:not(.is-animated) {
	// 	.y-tab__line {
	// 		display: inline-block;
	// 		position: absolute;
	// 		bottom: 3px;
	// 		left: 50%;
	// 		transform: translateX(-50%);
	// 		width: 20px;
	// 		height: 3px;
	// 		background-color: #0022ab;
	// 		border-radius: 3px;
	// 	}
	// }

	// 非滑块动画的按钮风格
	// &.is-button:not(.is-animated),
	// &.is-line-button:not(.is-animated) {
	// 	height: 26px;
	// 	line-height: 26px;
	// 	margin: 7px 0;
	// 	flex: auto;
	// 	border-radius: 26px;
	// 	padding: 0 10px;
	// }

	// 非滑块动画的线性按钮风格
	// &.is-line-button:not(.is-animated) {
	// 	border: 1px solid transparent;
	// }
}

// 标题区域
.y-tab__title {
	overflow: hidden;
	display: flex;
	align-items: center;
	height: inherit;
}

// 标题区域垂直排列
.y-tab__title--top,
.y-tab__title--bottom {
	flex-direction: column;
}

// 标题文字
.y-tab__text {
	position: relative;
	display: block;
	line-height: 1.2;
	order: 2;
	white-space: nowrap; //字节会设置white-space:normal
}

// 标签垂直展示时，未达到文字超出隐藏的条件时
.y-tabs__nav.is-vertical .y-tab__text:not(.y-tabs__ellipsis) {
	white-space: normal;
}

// 使用order排序
.y-tab__text--left,
.y-tab__text--top {
	order: 0;
}

// 标题图标/图片包裹层
.y-tab__icons {
	display: flex;
	align-items: center;
	order: 1;
	z-index: 1;
}

//标题图片
.y-tab__image {
	width: 20px;
	height: 20px;
}

// 右上角信息区域
.y-tab__info {
	display: inline-flex;
	position: relative;

	&--dot,
	&--badge {
		display: inline-block;
		position: absolute;
		top: 0;
		left: 0;
		box-sizing: border-box;
		background-color: #e53935;
		transform-origin: 100%;
	}

	// 小红点
	&--dot {
		width: 6px;
		height: 6px;
		border-radius: 100%;
		transform: translate(0%, -180%);
	}

	// 徽标
	&--badge {
		line-height: 13px;
		min-width: 18px;
		border-radius: 18px;
		padding: 0 2px;
		transform: translate(0%, -120%);
		font-size: 18rpx;
		font-weight: 500;
		text-align: center;
		color: #fff;
	}
}

// 底部条滑块
.y-tabs__bar {
	position: absolute;
	display: inline-flex;
	left: 0;
	z-index: 1;

	// 底部条实际的内容
	&-inner {
		background-color: #0022ab;
		box-sizing: border-box;
	}

	// line风格的滑块,z-index与y-tab一样,避免被遮挡
	&.is-line {
		z-index: 2;

		// 标签水平展示时
		&:not(.is-vertical) {
			bottom: 3px;
			.y-tabs__bar-inner {
				width: 20px;
				height: 3px;
				border-radius: 3px;
			}
		}

		// 标签垂直展示时
		&.is-vertical {
			top: 0;
			left: 3px;
			.y-tabs__bar-inner {
				width: 3px;
				height: 20px;
				border-radius: 3px;
			}
		}
	}

	// button、line-button风格的滑块
	&.is-button,
	&.is-line-button {
		top: 0;
		justify-content: center;
		align-items: center;

		// 标签水平展示时
		&:not(.is-vertical) {
			height: 100%;
			.y-tabs__bar-inner {
				height: calc(100% - 8px);
				border-radius: 26px;
			}
		}

		// 标签垂直展示时
		&.is-vertical {
			width: 100%;
			.y-tabs__bar-inner {
				width: calc(100% - 8px);
				height: calc(100% - 8px);
				border-radius: 26px;
			}
		}
	}

	// 线性按钮风格的滑块
	&.is-line-button .y-tabs__bar-inner {
		background-color: transparent;
		border: 2rpx solid transparent;
	}
}

// 标签内容
.y-tabs__content {
	position: relative;
	overflow: hidden;

	// 标签栏垂直展示，内容减去标签栏默认宽度
	&.is-vertical {
		// width: calc(100% - 100px);
		width: 100%;
		// flex-shrink: 0;
	}
}

// 标签内容的滑动轨道容器
.y-tabs__track {
	position: relative;
	display: flex;
	width: 100%;
	will-change: left;

	// 滚动导航模式下内容卡片垂直排列
	&.is-scrollspy {
		flex-direction: column;
	}
}

// 标签内容卡片
.y-tab__pane {
	flex-shrink: 0;
	box-sizing: border-box;
	width: 100%;
	height: 0;
	position: relative;
	flex-direction: row;
	display: block;

	// 选中时
	&.is-active {
		height: auto;
	}

	// 滚动导航
	&.is-scrollspy {
		height: auto !important;
	}
}

// 区域滚动下的标签内容scroll-view
.y-tabs__content-scrollview {
	flex-direction: column;
}
