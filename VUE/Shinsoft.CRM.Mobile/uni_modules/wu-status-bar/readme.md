## wu-status-bar 按钮

> **组件名：wu-status-bar**

本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。

## [查看文档](https://wu.geeks.ink/zh-CN/components/safeAreaInset.html)

## [更多组件, 请查看 `wu-ui` 组件库](https://ext.dcloud.net.cn/plugin?name=wu--ui)
(请勿下载插件zip)

<a href="https://ext.dcloud.net.cn/plugin?name=wu--ui">
	<img src="https://wu.geeks.ink/intr.png">
</a>

**如使用过程中有任何问题，或者您对wu-ui有一些好的建议。<br>欢迎加入 [wu-ui 交流群](https://wu.geeks.ink/zh-CN/components/qqFeedBack.html)**