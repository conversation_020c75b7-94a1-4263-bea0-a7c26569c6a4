import {
	defineConfig
} from 'vite';
import uni from '@dcloudio/vite-plugin-uni';

// 读取 manifest.json ，修改后重新写入
const fs = require('fs');

// 读取 manifest.json ，修改后重新写入
const manifestPath = `${__dirname}/manifest.json`;
let Manifest = fs.readFileSync(manifestPath, {
	encoding: 'utf-8'
});

function replaceManifest(path, value) {
	const arr = path.split('.');
	const len = arr.length;
	const lastItem = arr[len - 1];
	let i = 0;
	let ManifestArr = Manifest.split(/\n/);

	for (let index = 0; index < ManifestArr.length; index++) {
		const item = ManifestArr[index];
		if (new RegExp(`"${arr[i]}"`).test(item)) ++i;
		if (i === len) {
			const hasComma = /,/.test(item);
			ManifestArr[index] = item.replace(
				new RegExp(`"${lastItem}"[\\s\\S]*:[\\s\\S]*`),
				`"${lastItem}": ${typeof value === 'string'? '"'+value+'"' : value}${hasComma ? ',' : ''}`
			);
			break;
		}
	}
	Manifest = ManifestArr.join('\n');
}
var customer = process.env.UNI_CUSTOM_DEFINE ? JSON.parse(process.env.UNI_CUSTOM_DEFINE) : {}

function trim(str) {
	return str.replace(/(^\s*)|(\s*$)/g, '');
}

function readConfig(strConfig, key) {
	let arryConfig = strConfig.split(/\n/)
	let strKeyValue = arryConfig.filter(item => item.indexOf(key) > 0)[0]
	let arryV = strKeyValue.split('=')
	let value = ""
	if (arryV.length > 0) {
		value = trim(arryV[1])
	}
	value = value.replace(/'/g, "");
	return value
}
// import {devMapKey,prdMapKey,testMapKey} from './static/js/sysConfig.js'
const sysConfigPath = `${__dirname}/static/js/sysConfig.js`;
let sysConfig = fs.readFileSync(sysConfigPath, {
	encoding: 'utf-8'
});
let devMapKey = readConfig(sysConfig, 'devMapKey')
let testMapKey = readConfig(sysConfig, 'testMapKey')
let prdMapKey = readConfig(sysConfig, 'prdMapKey')
if (customer.MY_TEST === 'testing') {
	replaceManifest('h5.sdkConfigs.maps.qqmap.key', testMapKey)
} else if (process.env.NODE_ENV === 'production') {
	replaceManifest('h5.sdkConfigs.maps.qqmap.key', prdMapKey)
} else if (process.env.NODE_ENV === 'development') {
	replaceManifest('h5.sdkConfigs.maps.qqmap.key', devMapKey)
}

fs.writeFileSync(manifestPath, Manifest, {
	flag: 'w',
});

export default defineConfig({
	plugins: [uni()],
	
	// //设置代理
	// server: {
	//     host: "localhost", // 指定服务器应该监听哪个IP地址,默认：localhost
	//     port: 5173,        // 指定开发服务器端口,默认：5173
	//     proxy: {           // 为开发服务器配置自定义代理规则
	//        // 带选项写法：http://localhost:5173/api/posts -> http://jsonplaceholder.typicode.com/posts
	//       "/": {
	//         target: "https://crm.sperogenix.com:58352", // 目标接口
	//         changeOrigin: true,            // 是否换源
	//         rewrite: (path) => path.replace(/^\api/, ""),
	//       }
	//     },
	// 	}
});