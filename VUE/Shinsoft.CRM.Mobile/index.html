<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" href="./static/favicon.ico">
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<title></title>
		<!--preload-links-->
		<!--app-context-->
		<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="Pragma" content="no-cache" />
		<meta http-equiv="Expires" content="0" />
		<!-- <script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS
				.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script> -->
		<!-- <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js" /> -->
		<!-- <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script> -->
		<script>
			window.jWeixin = window.wx;
			delete window.wx;
		</script>
	</head>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/main.js"></script>

		<!-- <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script> -->
		<!-- <script type="text/javascript" src="https://apis.map.qq.com/tools/geolocation/min?key=your key&referer=myapp"></script> -->
		<!-- <script type="module" src="utils/MapService.js"></script> -->
		<!-- <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=D5vlFgdTyLVw5CX4osGG1lAiDKmXVI1T"></script> -->
	</body>
</html>